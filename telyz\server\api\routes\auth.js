/**
 * Authentication Routes
 * 
 * API routes for authentication.
 */

const express = require('express');
const router = express.Router();
const authController = require('../controllers/authController');
const authMiddleware = require('../middlewares/authMiddleware');
const { validateLogin } = require('../validators/authValidator');

/**
 * @route   POST /api/auth/login
 * @desc    Login user
 * @access  Public
 */
router.post('/login', validateLogin, authController.login);

/**
 * @route   POST /api/auth/logout
 * @desc    Logout user
 * @access  Private
 */
router.post('/logout', authMiddleware.authenticate, authController.logout);

/**
 * @route   GET /api/auth/me
 * @desc    Get current user
 * @access  Private
 */
router.get('/me', authMiddleware.authenticate, authController.getCurrentUser);

/**
 * @route   POST /api/auth/refresh
 * @desc    Refresh token
 * @access  Public
 */
router.post('/refresh', authController.refreshToken);

/**
 * @route   POST /api/auth/social/google
 * @desc    Google OAuth login
 * @access  Public
 */
router.post('/social/google', authController.googleLogin);

/**
 * @route   POST /api/auth/social/facebook
 * @desc    Facebook OAuth login
 * @access  Public
 */
router.post('/social/facebook', authController.facebookLogin);

/**
 * @route   POST /api/auth/social/apple
 * @desc    Apple OAuth login
 * @access  Public
 */
router.post('/social/apple', authController.appleLogin);

module.exports = router;
