/**
 * EDUCATION HUB STYLES - GUARDIOLA AI
 * Premium Education Platform Design
 */

/* ================================================
   CSS VARIABLES & ROOT STYLING
   ================================================ */

:root {
    /* Primary Brand Colors */
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --primary-light: #818cf8;
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    
    /* Secondary Colors */
    --secondary-color: #10b981;
    --secondary-dark: #059669;
    --accent-color: #f59e0b;
    --accent-dark: #d97706;
    
    /* Education Theme Colors */
    --education-primary: #6366f1;
    --education-secondary: #8b5cf6;
    --education-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --live-color: #ef4444;
    --premium-color: #f59e0b;
    --success-color: #10b981;
    
    /* Neutral Colors */
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    --background-primary: #ffffff;
    --background-secondary: #f9fafb;
    --background-tertiary: #f3f4f6;
    --border-color: #e5e7eb;
    --border-light: #f3f4f6;
    
    /* Dark Mode Colors */
    --dark-bg-primary: #111827;
    --dark-bg-secondary: #1f2937;
    --dark-text-primary: #f9fafb;
    --dark-text-secondary: #d1d5db;
    --dark-border: #374151;
    
    /* Spacing */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;
    --space-3xl: 4rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    
    /* Typography */
    --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-family-display: 'Poppins', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    
    /* Font Sizes */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    --text-5xl: 3rem;
    
    /* Font Weights */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    --font-weight-black: 900;
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
    
    /* Z-Index */
    --z-dropdown: 10;
    --z-sticky: 20;
    --z-fixed: 30;
    --z-modal-backdrop: 40;
    --z-modal: 50;
    --z-popover: 60;
    --z-tooltip: 70;
    --z-toast: 80;
    --z-loading: 90;
    
    /* Container Sizes */
    --container-sm: 640px;
    --container-md: 768px;
    --container-lg: 1024px;
    --container-xl: 1280px;
    --container-2xl: 1536px;
    
    /* Header Height */
    --header-height: 80px;
    --sidebar-width: 320px;
    --sidebar-width-collapsed: 80px;
}

/* ================================================
   RESET & BASE STYLES
   ================================================ */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

body {
    font-family: var(--font-family-primary);
    font-size: var(--text-base);
    font-weight: var(--font-weight-normal);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background-secondary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
}

/* ================================================
   LOADING SCREEN
   ================================================ */

.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--education-primary) 0%, var(--education-secondary) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-loading);
    transition: opacity var(--transition-slow), visibility var(--transition-slow);
}

.loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-container {
    text-align: center;
    color: white;
}

.loading-logo {
    font-size: 4rem;
    margin-bottom: var(--space-lg);
    animation: float 2s ease-in-out infinite;
}

.loading-text h3 {
    font-family: var(--font-family-display);
    font-size: var(--text-2xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--space-sm);
}

.loading-text p {
    font-size: var(--text-lg);
    opacity: 0.9;
    margin-bottom: var(--space-xl);
}

.loading-progress {
    width: 200px;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-sm);
    overflow: hidden;
    margin: 0 auto;
}

.loading-progress .progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #ffffff 0%, #f0f9ff 100%);
    border-radius: var(--radius-sm);
    animation: loading-progress 2s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes loading-progress {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

/* ================================================
   LAYOUT & CONTAINERS
   ================================================ */

.container {
    max-width: var(--container-2xl);
    margin: 0 auto;
    padding: 0 var(--space-lg);
}

@media (max-width: 640px) {
    .container {
        padding: 0 var(--space-md);
    }
}

/* ================================================
   TYPOGRAPHY
   ================================================ */

h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family-display);
    font-weight: var(--font-weight-bold);
    line-height: 1.2;
    margin-bottom: var(--space-md);
    color: var(--text-primary);
}

h1 { font-size: var(--text-4xl); }
h2 { font-size: var(--text-3xl); }
h3 { font-size: var(--text-2xl); }
h4 { font-size: var(--text-xl); }
h5 { font-size: var(--text-lg); }
h6 { font-size: var(--text-base); }

p {
    margin-bottom: var(--space-md);
    color: var(--text-secondary);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--primary-dark);
}

/* ================================================
   BUTTONS
   ================================================ */

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
    padding: var(--space-sm) var(--space-lg);
    font-family: var(--font-family-primary);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
    line-height: 1;
    border: 1px solid transparent;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    white-space: nowrap;
    user-select: none;
    position: relative;
    overflow: hidden;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Button Sizes */
.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: var(--text-xs);
}

.btn-lg {
    padding: var(--space-md) var(--space-xl);
    font-size: var(--text-base);
}

/* Button Variants */
.btn-primary {
    background: var(--education-gradient);
    color: white;
    border-color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    background: var(--primary-dark);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: white;
}

.btn-secondary {
    background: var(--background-tertiary);
    color: var(--text-primary);
    border-color: var(--border-color);
}

.btn-secondary:hover {
    background: var(--background-primary);
    box-shadow: var(--shadow-sm);
}

/* ================================================
   HEADER
   ================================================ */

.education-header {
    position: sticky;
    top: 0;
    background: var(--background-primary);
    border-bottom: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);
    z-index: var(--z-sticky);
    height: var(--header-height);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    gap: var(--space-lg);
}

/* Logo Section */
.logo-section {
    flex-shrink: 0;
}

.logo-link {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    color: var(--text-primary);
    text-decoration: none;
}

.logo-container {
    width: 48px;
    height: 48px;
    background: var(--education-gradient);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.logo-text {
    display: flex;
    flex-direction: column;
}

.platform-name {
    font-family: var(--font-family-display);
    font-size: var(--text-lg);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
}

.section-subtitle {
    font-size: var(--text-sm);
    color: var(--text-secondary);
}

/* Search Section */
.search-section {
    flex: 1;
    max-width: 600px;
}

.search-container {
    position: relative;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: var(--background-secondary);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-xl);
    padding: var(--space-sm);
    transition: all var(--transition-fast);
}

.search-input-wrapper:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.search-icon {
    color: var(--text-light);
    margin-left: var(--space-md);
    margin-right: var(--space-sm);
}

.search-input {
    flex: 1;
    border: none;
    background: transparent;
    font-size: var(--text-base);
    color: var(--text-primary);
    padding: var(--space-sm) 0;
}

.search-input::placeholder {
    color: var(--text-light);
}

.search-input:focus {
    outline: none;
}

.search-btn {
    background: var(--education-gradient);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    padding: var(--space-sm) var(--space-lg);
    cursor: pointer;
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-fast);
}

.search-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

/* Search Suggestions */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--background-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    z-index: var(--z-dropdown);
    display: none;
    max-height: 300px;
    overflow-y: auto;
    margin-top: var(--space-xs);
}

/* Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    flex-shrink: 0;
}

.action-btn {
    position: relative;
    width: 44px;
    height: 44px;
    border: none;
    background: var(--background-secondary);
    color: var(--text-secondary);
    border-radius: var(--radius-lg);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
}

.action-btn:hover {
    background: var(--background-tertiary);
    color: var(--text-primary);
    transform: translateY(-1px);
}

.premium-btn {
    width: auto;
    padding: 0 var(--space-lg);
    background: var(--premium-color);
    color: white;
    gap: var(--space-sm);
}

.premium-btn:hover {
    background: var(--accent-dark);
    color: white;
}

/* Notification Badge */
.notification-count,
.favorites-count {
    position: absolute;
    top: -4px;
    right: -4px;
    background: var(--live-color);
    color: white;
    font-size: 10px;
    font-weight: var(--font-weight-bold);
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* User Profile */
.user-profile-header {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-sm);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.user-profile-header:hover {
    background: var(--background-secondary);
}

.profile-pic {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    object-fit: cover;
}

.user-info {
    display: flex;
    flex-direction: column;
}

.username {
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
}

.user-level {
    font-size: var(--text-xs);
    color: var(--premium-color);
    font-weight: var(--font-weight-medium);
}

.dropdown-icon {
    color: var(--text-light);
    font-size: var(--text-xs);
}

/* ================================================
   MAIN CONTENT
   ================================================ */

.education-main {
    min-height: calc(100vh - var(--header-height));
    padding-top: var(--space-xl);
}

/* ================================================
   HERO SECTION
   ================================================ */

.hero-section {
    margin-bottom: var(--space-3xl);
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-3xl);
    align-items: center;
}

.hero-text {
    max-width: 600px;
}

.hero-title {
    font-family: var(--font-family-display);
    font-size: var(--text-5xl);
    font-weight: var(--font-weight-black);
    line-height: 1.1;
    margin-bottom: var(--space-lg);
}

.title-main {
    display: block;
    color: var(--text-primary);
}

.title-highlight {
    display: block;
    background: var(--education-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.title-sub {
    display: block;
    color: var(--text-primary);
}

.hero-description {
    font-size: var(--text-xl);
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--space-xl);
}

.hero-stats {
    display: flex;
    gap: var(--space-xl);
    margin-bottom: var(--space-xl);
}

.stat-item {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.stat-item i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.stat-content {
    display: flex;
    flex-direction: column;
}

.stat-number {
    font-size: var(--text-xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    line-height: 1;
}

.stat-label {
    font-size: var(--text-sm);
    color: var(--text-secondary);
}

.hero-actions {
    display: flex;
    gap: var(--space-lg);
    flex-wrap: wrap;
}

/* Hero Visual */
.hero-visual {
    position: relative;
}

.hero-image-container {
    position: relative;
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: var(--shadow-2xl);
}

.hero-image {
    width: 100%;
    height: 400px;
    object-fit: cover;
}

.floating-elements {
    position: absolute;
    inset: 0;
    pointer-events: none;
}

.floating-card {
    position: absolute;
    background: white;
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
    animation: float 3s ease-in-out infinite;
}

.live-indicator {
    top: 20px;
    left: 20px;
    color: var(--live-color);
}

.live-dot {
    color: var(--live-color);
    animation: pulse 2s infinite;
}

.expert-card {
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
}

.expert-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    object-fit: cover;
}

.achievement-card {
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    color: var(--accent-color);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* ================================================
   QUICK ACCESS SECTION
   ================================================ */

.quick-access-section {
    margin-bottom: var(--space-3xl);
}

.quick-access-header {
    text-align: center;
    margin-bottom: var(--space-xl);
}

.quick-access-header h2 {
    font-size: var(--text-3xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--space-sm);
}

.quick-access-header p {
    font-size: var(--text-lg);
    color: var(--text-secondary);
}

.quick-access-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-lg);
}

.quick-access-card {
    background: var(--background-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    padding: var(--space-xl);
    display: flex;
    align-items: center;
    gap: var(--space-lg);
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.quick-access-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--education-gradient);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.quick-access-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-4px);
    border-color: var(--primary-color);
}

.quick-access-card:hover::before {
    transform: scaleX(1);
}

.card-icon {
    width: 60px;
    height: 60px;
    background: var(--education-gradient);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.card-content {
    flex: 1;
}

.card-content h3 {
    font-size: var(--text-lg);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--space-xs);
    color: var(--text-primary);
}

.card-content p {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin-bottom: var(--space-sm);
}

.card-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-xs);
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-md);
    font-size: var(--text-xs);
    font-weight: var(--font-weight-medium);
}

.live-sessions-card .card-badge {
    background: rgba(239, 68, 68, 0.1);
    color: var(--live-color);
}

.video-library-card .card-badge {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.premium-badge {
    background: rgba(245, 158, 11, 0.1);
    color: var(--premium-color);
}

.progress-preview {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-size: var(--text-xs);
    color: var(--text-secondary);
}

.progress-preview .progress-bar {
    flex: 1;
    height: 6px;
    background: var(--background-tertiary);
    border-radius: var(--radius-sm);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--education-gradient);
    border-radius: var(--radius-sm);
    transition: width var(--transition-normal);
}

.card-arrow {
    color: var(--text-light);
    font-size: var(--text-lg);
    transition: color var(--transition-fast);
}

.quick-access-card:hover .card-arrow {
    color: var(--primary-color);
    transform: translateX(4px);
}

/* ================================================
   MAIN LAYOUT
   ================================================ */

.main-layout {
    display: grid;
    grid-template-columns: var(--sidebar-width) 1fr;
    gap: var(--space-xl);
    align-items: start;
}

/* ================================================
   SIDEBAR
   ================================================ */

.content-sidebar {
    position: sticky;
    top: calc(var(--header-height) + var(--space-lg));
    background: var(--background-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    padding: var(--space-xl);
    max-height: calc(100vh - var(--header-height) - var(--space-2xl));
    overflow-y: auto;
}

.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-xl);
    padding-bottom: var(--space-lg);
    border-bottom: 1px solid var(--border-light);
}

.sidebar-header h3 {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-size: var(--text-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0;
}

.sidebar-toggle {
    display: none;
    width: 32px;
    height: 32px;
    border: none;
    background: var(--background-secondary);
    color: var(--text-secondary);
    border-radius: var(--radius-md);
    cursor: pointer;
    align-items: center;
    justify-content: center;
}

/* Filter Sections */
.filter-section {
    margin-bottom: var(--space-xl);
}

.filter-header {
    margin-bottom: var(--space-lg);
}

.filter-header h4 {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-size: var(--text-base);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0;
}

/* Sport Selector */
.sport-selector {
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
}

.sport-option {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    padding: var(--space-md);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.sport-option:hover {
    border-color: var(--primary-color);
    background: rgba(99, 102, 241, 0.05);
}

.sport-option.active {
    border-color: var(--primary-color);
    background: rgba(99, 102, 241, 0.1);
    color: var(--primary-color);
}

.sport-option i {
    font-size: 1.25rem;
}

.sport-option span {
    font-weight: var(--font-weight-medium);
}

/* Filter Options */
.filter-options {
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
}

.filter-option {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    padding: var(--space-sm) 0;
    cursor: pointer;
    position: relative;
}

.filter-option input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.checkmark,
.radiomark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
}

.radiomark {
    border-radius: 50%;
}

.filter-option input:checked ~ .checkmark,
.filter-option input:checked ~ .radiomark {
    border-color: var(--primary-color);
    background: var(--primary-color);
}

.filter-option input:checked ~ .checkmark::after {
    content: '\2713';
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.filter-option input:checked ~ .radiomark::after {
    content: '';
    width: 8px;
    height: 8px;
    background: white;
    border-radius: 50%;
}

.option-text {
    flex: 1;
    font-size: var(--text-sm);
    color: var(--text-primary);
}

.count {
    font-size: var(--text-xs);
    color: var(--text-light);
    padding: 2px 6px;
    background: var(--background-tertiary);
    border-radius: var(--radius-sm);
}

/* Filter Actions */
.filter-actions {
    margin-top: var(--space-xl);
    padding-top: var(--space-lg);
    border-top: 1px solid var(--border-light);
}

.clear-filters {
    width: 100%;
}

/* ================================================
   MAIN CONTENT AREA
   ================================================ */

.main-content {
    min-height: 500px;
}

/* Content Header */
.content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-xl);
    padding-bottom: var(--space-lg);
    border-bottom: 1px solid var(--border-light);
}

.content-title-section h2 {
    font-size: var(--text-2xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--space-xs);
}

.content-title-section p {
    color: var(--text-secondary);
    margin: 0;
}

.content-controls {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
}

.filters-toggle-btn {
    display: none;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-sm) var(--space-md);
    border: 1px solid var(--border-color);
    background: var(--background-primary);
    color: var(--text-primary);
    border-radius: var(--radius-md);
    cursor: pointer;
    font-size: var(--text-sm);
}

.view-toggle {
    display: flex;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    overflow: hidden;
}

.view-btn {
    padding: var(--space-sm) var(--space-md);
    border: none;
    background: var(--background-primary);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.view-btn.active,
.view-btn:hover {
    background: var(--primary-color);
    color: white;
}

.sort-dropdown {
    position: relative;
}

.sort-select {
    padding: var(--space-sm) var(--space-lg);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--background-primary);
    color: var(--text-primary);
    font-size: var(--text-sm);
    cursor: pointer;
}

/* ================================================
   CONTENT SECTIONS
   ================================================ */

.content-section {
    margin-bottom: var(--space-3xl);
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-xl);
}

.section-title {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.section-title h3 {
    font-size: var(--text-xl);
    font-weight: var(--font-weight-semibold);
    margin: 0;
}

.live-indicator {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    padding: var(--space-xs) var(--space-sm);
    background: rgba(239, 68, 68, 0.1);
    color: var(--live-color);
    border-radius: var(--radius-md);
    font-size: var(--text-xs);
    font-weight: var(--font-weight-medium);
}

.section-badge {
    padding: var(--space-xs) var(--space-sm);
    background: var(--education-gradient);
    color: white;
    border-radius: var(--radius-md);
    font-size: var(--text-xs);
    font-weight: var(--font-weight-medium);
}

.content-count {
    color: var(--text-secondary);
    font-size: var(--text-sm);
}

.view-all-link {
    color: var(--primary-color);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.view-all-link:hover {
    color: var(--primary-dark);
}

/* Content Grids */
.live-sessions-grid,
.video-library-grid,
.featured-content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--space-lg);
}

/* ================================================
   LOAD MORE SECTION
   ================================================ */

.load-more-section {
    text-align: center;
    padding: var(--space-3xl) 0;
}

.load-more-btn {
    margin-bottom: var(--space-lg);
}

.load-more-info {
    color: var(--text-secondary);
    font-size: var(--text-sm);
    margin: 0;
}

/* ================================================
   MODALS
   ================================================ */

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.75);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--background-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-2xl);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform var(--transition-normal);
}

.modal-overlay.active .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-xl);
    border-bottom: 1px solid var(--border-light);
}

.modal-header h3 {
    margin: 0;
    font-size: var(--text-xl);
    font-weight: var(--font-weight-semibold);
}

.modal-close {
    width: 32px;
    height: 32px;
    border: none;
    background: var(--background-secondary);
    color: var(--text-secondary);
    border-radius: var(--radius-md);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--background-tertiary);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--space-xl);
}

/* Live Session Modal */
.live-session-modal {
    width: 1200px;
    height: 700px;
}

.live-session-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--space-xl);
    height: 100%;
}

.video-section {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
}

.video-container {
    position: relative;
    background: #000;
    border-radius: var(--radius-lg);
    overflow: hidden;
    flex: 1;
}

.video-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: white;
    font-size: 2rem;
}

.live-indicator-overlay {
    position: absolute;
    top: var(--space-lg);
    left: var(--space-lg);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    background: var(--live-color);
    color: white;
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-bold);
}

.session-info {
    background: var(--background-secondary);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
}

.expert-info {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    margin-bottom: var(--space-lg);
}

.expert-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.expert-details h4 {
    margin: 0 0 var(--space-xs) 0;
    font-size: var(--text-lg);
}

.expert-details p {
    margin: 0 0 var(--space-sm) 0;
    color: var(--text-secondary);
    font-size: var(--text-sm);
}

.expert-rating {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.stars {
    display: flex;
    gap: 2px;
    color: var(--accent-color);
}

.session-details h5 {
    margin: 0 0 var(--space-sm) 0;
    font-size: var(--text-lg);
}

.session-details p {
    margin: 0 0 var(--space-md) 0;
    color: var(--text-secondary);
}

.session-meta {
    display: flex;
    gap: var(--space-lg);
    color: var(--text-secondary);
    font-size: var(--text-sm);
}

.session-meta span {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

/* Chat Section */
.chat-section {
    display: flex;
    flex-direction: column;
    background: var(--background-secondary);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.chat-header {
    padding: var(--space-lg);
    border-bottom: 1px solid var(--border-light);
    background: var(--background-primary);
}

.chat-header h4 {
    margin: 0;
    font-size: var(--text-lg);
}

.chat-messages {
    flex: 1;
    padding: var(--space-lg);
    overflow-y: auto;
    min-height: 300px;
}

.chat-input-section {
    border-top: 1px solid var(--border-light);
    padding: var(--space-lg);
    background: var(--background-primary);
}

.chat-input-wrapper {
    display: flex;
    gap: var(--space-sm);
    margin-bottom: var(--space-sm);
}

.chat-input {
    flex: 1;
    padding: var(--space-sm) var(--space-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    font-size: var(--text-sm);
}

.chat-send-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: var(--primary-color);
    color: white;
    border-radius: var(--radius-lg);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.premium-notice {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    color: var(--premium-color);
    font-size: var(--text-xs);
}

/* Premium Modal */
.premium-modal {
    width: 600px;
}

.premium-hero {
    text-align: center;
    margin-bottom: var(--space-xl);
}

.premium-hero h4 {
    font-size: var(--text-2xl);
    margin-bottom: var(--space-sm);
}

.premium-features {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
    margin-bottom: var(--space-xl);
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: var(--space-md);
}

.feature-item i {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-top: var(--space-xs);
}

.feature-item h5 {
    margin: 0 0 var(--space-xs) 0;
    font-size: var(--text-base);
}

.feature-item p {
    margin: 0;
    color: var(--text-secondary);
    font-size: var(--text-sm);
}

.premium-plans {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-lg);
}

.plan-card {
    position: relative;
    background: var(--background-secondary);
    border: 2px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--space-xl);
    text-align: center;
}

.plan-card.recommended {
    border-color: var(--primary-color);
    background: var(--background-primary);
}

.recommended-badge {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--primary-color);
    color: white;
    padding: var(--space-xs) var(--space-md);
    border-radius: var(--radius-md);
    font-size: var(--text-xs);
    font-weight: var(--font-weight-bold);
}

.plan-card h5 {
    margin: 0 0 var(--space-lg) 0;
    font-size: var(--text-lg);
}

.plan-price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    margin-bottom: var(--space-md);
}

.currency {
    font-size: var(--text-lg);
    color: var(--text-secondary);
}

.amount {
    font-size: var(--text-4xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin: 0 var(--space-xs);
}

.period {
    color: var(--text-secondary);
}

.plan-savings {
    color: var(--success-color);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
    margin-bottom: var(--space-lg);
}

/* ================================================
   MOBILE OVERLAY
   ================================================ */

.mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: var(--z-modal-backdrop);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-fast);
}

.mobile-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* ================================================
   TOAST NOTIFICATIONS
   ================================================ */

.toast-container {
    position: fixed;
    top: calc(var(--header-height) + var(--space-lg));
    right: var(--space-lg);
    z-index: var(--z-toast);
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
}

.toast {
    background: var(--background-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--space-md) var(--space-lg);
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: var(--space-md);
    max-width: 400px;
    transform: translateX(100%);
    transition: transform var(--transition-normal);
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    border-left: 4px solid var(--success-color);
}

.toast.error {
    border-left: 4px solid var(--live-color);
}

.toast.warning {
    border-left: 4px solid var(--accent-color);
}

.toast.info {
    border-left: 4px solid var(--primary-color);
}

/* ================================================
   RESPONSIVE DESIGN
   ================================================ */

@media (max-width: 1024px) {
    .main-layout {
        grid-template-columns: 1fr;
        gap: var(--space-lg);
    }

    .content-sidebar {
        position: fixed;
        top: var(--header-height);
        left: 0;
        width: var(--sidebar-width);
        height: calc(100vh - var(--header-height));
        transform: translateX(-100%);
        transition: transform var(--transition-normal);
        z-index: var(--z-fixed);
        max-height: none;
    }

    .content-sidebar.active {
        transform: translateX(0);
    }

    .sidebar-toggle {
        display: flex;
    }

    .filters-toggle-btn {
        display: flex;
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: var(--space-xl);
        text-align: center;
    }

    .hero-stats {
        justify-content: center;
    }

    .live-session-modal {
        width: 95vw;
        height: 90vh;
    }

    .live-session-content {
        grid-template-columns: 1fr;
        gap: var(--space-lg);
    }
}

@media (max-width: 768px) {
    .header-content {
        gap: var(--space-md);
    }

    .search-section {
        max-width: 400px;
    }

    .search-btn span {
        display: none;
    }

    .user-info {
        display: none;
    }

    .hero-title {
        font-size: var(--text-3xl);
    }

    .hero-description {
        font-size: var(--text-lg);
    }

    .hero-stats {
        flex-direction: column;
        gap: var(--space-lg);
        align-items: center;
    }

    .hero-actions {
        justify-content: center;
    }

    .quick-access-grid {
        grid-template-columns: 1fr;
    }

    .content-header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-lg);
    }

    .content-controls {
        justify-content: space-between;
    }

    .live-sessions-grid,
    .video-library-grid,
    .featured-content-grid {
        grid-template-columns: 1fr;
    }

    .premium-plans {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 640px) {
    .container {
        padding: 0 var(--space-md);
    }

    .logo-text {
        display: none;
    }

    .search-section {
        max-width: 300px;
    }

    .header-actions {
        gap: var(--space-sm);
    }

    .premium-btn span {
        display: none;
    }

    .hero-title {
        font-size: var(--text-2xl);
    }

    .stat-item {
        flex-direction: column;
        text-align: center;
        gap: var(--space-sm);
    }

    .hero-actions {
        flex-direction: column;
        width: 100%;
    }

    .hero-actions .btn {
        width: 100%;
    }

    .quick-access-card {
        flex-direction: column;
        text-align: center;
        gap: var(--space-md);
    }

    .card-arrow {
        display: none;
    }

    .content-sidebar {
        width: 100%;
    }

    .live-session-modal,
    .premium-modal {
        width: 95vw;
        height: 90vh;
        margin: var(--space-md);
    }

    .modal-header,
    .modal-body {
        padding: var(--space-lg);
    }
}

/* ================================================
   ANIMATIONS
   ================================================ */

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.animate-slide-up {
    animation: slideInUp 0.6s ease-out;
}

.animate-slide-left {
    animation: slideInLeft 0.6s ease-out;
}

.animate-fade-in {
    animation: fadeIn 0.6s ease-out;
}

/* ================================================
   UTILITY CLASSES
   ================================================ */

.hidden {
    display: none !important;
}

.invisible {
    visibility: hidden !important;
}

.sr-only {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    border: 0 !important;
}

.text-center {
    text-align: center !important;
}

.text-left {
    text-align: left !important;
}

.text-right {
    text-align: right !important;
}

.font-bold {
    font-weight: var(--font-weight-bold) !important;
}

.font-semibold {
    font-weight: var(--font-weight-semibold) !important;
}

.text-primary {
    color: var(--text-primary) !important;
}

.text-secondary {
    color: var(--text-secondary) !important;
}

.text-light {
    color: var(--text-light) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.bg-secondary {
    background-color: var(--secondary-color) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}

.rounded {
    border-radius: var(--radius-md) !important;
}

.rounded-lg {
    border-radius: var(--radius-lg) !important;
}

.shadow {
    box-shadow: var(--shadow-md) !important;
}

.shadow-lg {
    box-shadow: var(--shadow-lg) !important;
}

/* ================================================
   PRINT STYLES
   ================================================ */

@media print {
    .education-header,
    .content-sidebar,
    .modal-overlay,
    .toast-container,
    .mobile-overlay {
        display: none !important;
    }

    .main-layout {
        grid-template-columns: 1fr !important;
    }

    .hero-visual,
    .floating-elements {
        display: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }
}

/* Additional Content Card Styles */
.content-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    overflow: hidden;
    transition: var(--transition-normal);
    border: 2px solid var(--gray-200);
    box-shadow: var(--shadow-md);
    position: relative;
}

.content-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.content-thumbnail {
    position: relative;
    overflow: hidden;
    height: 200px;
}

.content-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.content-card:hover .content-thumbnail img {
    transform: scale(1.05);
}

.premium-badge {
    position: absolute;
    top: var(--space-3);
    right: var(--space-3);
    background: var(--premium-gradient);
    color: var(--white);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: var(--font-weight-bold);
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

.content-duration {
    position: absolute;
    bottom: var(--space-3);
    left: var(--space-3);
    background: rgba(0, 0, 0, 0.8);
    color: var(--white);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius);
    font-size: var(--text-xs);
    font-weight: var(--font-weight-medium);
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

.content-price {
    position: absolute;
    bottom: var(--space-3);
    right: var(--space-3);
    background: var(--success-color);
    color: var(--white);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-bold);
}

.content-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition-fast);
}

.content-card:hover .content-overlay {
    opacity: 1;
}

.play-btn {
    width: 60px;
    height: 60px;
    background: var(--white);
    color: var(--primary-color);
    border: none;
    border-radius: var(--radius-full);
    font-size: var(--text-2xl);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-lg);
}

.play-btn:hover {
    transform: scale(1.1);
    background: var(--primary-color);
    color: var(--white);
}

.content-info {
    padding: var(--space-6);
}

.content-header {
    margin-bottom: var(--space-4);
}

.content-header h4 {
    font-size: var(--text-lg);
    font-weight: var(--font-weight-bold);
    color: var(--gray-900);
    margin-bottom: var(--space-2);
    line-height: 1.4;
}

.content-stats {
    display: flex;
    gap: var(--space-4);
    font-size: var(--text-sm);
    color: var(--gray-500);
}

.content-stats span {
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

.content-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-2);
    margin-bottom: var(--space-4);
}

.tag {
    background: var(--gray-100);
    color: var(--gray-700);
    font-size: var(--text-xs);
    font-weight: var(--font-weight-medium);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-full);
    border: 1px solid var(--gray-200);
}

.content-actions {
    display: flex;
    gap: var(--space-2);
}

.content-action-btn {
    flex: 1;
}

/* Live Session Card Styles */
.live-session-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    overflow: hidden;
    transition: var(--transition-normal);
    border: 2px solid var(--gray-200);
    box-shadow: var(--shadow-md);
    position: relative;
}

.live-session-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--live-color);
}

.session-thumbnail {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.session-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.live-badge {
    position: absolute;
    top: var(--space-3);
    left: var(--space-3);
    background: var(--live-color);
    color: var(--white);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: var(--font-weight-bold);
    display: flex;
    align-items: center;
    gap: var(--space-1);
    animation: pulse 2s ease-in-out infinite;
}

.session-viewers {
    position: absolute;
    top: var(--space-3);
    right: var(--space-3);
    background: rgba(0, 0, 0, 0.8);
    color: var(--white);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius);
    font-size: var(--text-xs);
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

.session-duration {
    position: absolute;
    bottom: var(--space-3);
    right: var(--space-3);
    background: rgba(0, 0, 0, 0.8);
    color: var(--white);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius);
    font-size: var(--text-xs);
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

.session-content {
    padding: var(--space-6);
}

.session-header {
    margin-bottom: var(--space-4);
}

.session-header h4 {
    font-size: var(--text-lg);
    font-weight: var(--font-weight-bold);
    color: var(--gray-900);
    margin-bottom: var(--space-3);
}

.session-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-2);
}

.session-actions {
    display: flex;
    gap: var(--space-2);
    margin-top: var(--space-4);
}

.join-session-btn {
    flex: 1;
}

/* Toast Notification Styles */
.toast-container {
    position: fixed;
    top: var(--space-6);
    right: var(--space-6);
    z-index: var(--z-toast);
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
    max-width: 400px;
}

.toast {
    background: var(--white);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    box-shadow: var(--shadow-xl);
    display: flex;
    align-items: center;
    gap: var(--space-3);
    transform: translateX(100%);
    transition: var(--transition-normal);
    position: relative;
}

.toast.show {
    transform: translateX(0);
}

.toast-success {
    border-color: var(--success-color);
    color: var(--success-color);
}

.toast-error {
    border-color: var(--error-color);
    color: var(--error-color);
}

.toast-warning {
    border-color: var(--warning-color);
    color: var(--warning-color);
}

.toast-info {
    border-color: var(--info-color);
    color: var(--info-color);
}

.toast-close {
    background: transparent;
    border: none;
    color: var(--gray-400);
    font-size: var(--text-sm);
    cursor: pointer;
    padding: var(--space-1);
    border-radius: var(--radius);
    transition: var(--transition-fast);
    margin-left: auto;
}

.toast-close:hover {
    background: var(--gray-100);
    color: var(--gray-600);
}