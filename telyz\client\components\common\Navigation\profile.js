/**
 * Profile Navigation Component
 * 
 * Handles the functionality of the user profile navigation, including:
 * - Profile dropdown menu
 * - Profile tooltips
 * - Eye icon functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    // Profile dropdown functionality
    initializeProfileDropdown();
    
    // Eye icon functionality
    initializeEyeIcon();
    
    // Profile tooltips
    initializeProfileTooltips();
});

/**
 * Initialize profile dropdown menu functionality
 */
function initializeProfileDropdown() {
    const toggleButton = document.getElementById('profileDropdownToggle');
    const dropdownMenu = document.getElementById('profileDropdownMenu');
    if (!toggleButton || !dropdownMenu) return;
    
    const userProfile = document.querySelector('.user-profile-sidebar');
    
    let isMenuOpen = false;
    
    // Function to show/hide menu
    toggleButton.addEventListener('click', function(e) {
        e.stopPropagation();
        isMenuOpen = !isMenuOpen;
        dropdownMenu.classList.toggle('show', isMenuOpen);
        
        // Ensure icons are visible
        if (isMenuOpen) {
            setTimeout(() => {
                const icons = dropdownMenu.querySelectorAll('i');
                icons.forEach(icon => {
                    icon.style.display = 'inline-block';
                });
            }, 50);
        }
    });
    
    // Close menu when clicking outside
    document.addEventListener('click', function(e) {
        if (!dropdownMenu.contains(e.target) && !toggleButton.contains(e.target) && !userProfile.contains(e.target)) {
            isMenuOpen = false;
            dropdownMenu.classList.remove('show');
        }
    });
    
    // Prevent menu from closing when clicking inside it
    dropdownMenu.addEventListener('click', function(e) {
        e.stopPropagation();
    });
    
    // Prevent menu from closing when hovering over it
    dropdownMenu.addEventListener('mouseenter', function() {
        if (isMenuOpen) {
            dropdownMenu.classList.add('show');
        }
    });
}

/**
 * Initialize eye icon functionality
 */
function initializeEyeIcon() {
    const watchingEyeIcon = document.getElementById('watchingEyeIcon');
    if (!watchingEyeIcon) return;
    
    watchingEyeIcon.addEventListener('click', function() {
        // Create notification for premium feature
        showNotification('Talent Monitoring System', '5 scouts and clubs are actively monitoring your performance and career development. Complete an AI analysis and subscribe to premium to enhance your visibility to top clubs.');
    });
}

/**
 * Initialize tooltips for profile menu items
 */
function initializeProfileTooltips() {
    const profileTooltip = document.getElementById('profile-tooltip');
    if (!profileTooltip) return;
    
    const dropdownMenu = document.getElementById('profileDropdownMenu');
    if (!dropdownMenu) return;
    
    const profileMenuItems = dropdownMenu.querySelectorAll('.profile-dropdown-item');
    
    profileMenuItems.forEach((item) => {
        // Show tooltip on hover
        item.addEventListener('mouseenter', function(event) {
            const description = this.getAttribute('data-tooltip');
            const rect = this.getBoundingClientRect();
            
            // Configure tooltip
            profileTooltip.textContent = description;
            profileTooltip.style.backgroundColor = 'rgba(106, 13, 173, 0.9)';
            profileTooltip.style.left = (rect.right + 15) + 'px';
            profileTooltip.style.top = (rect.top + rect.height/2 - 20) + 'px';
            
            // Configure arrow color
            profileTooltip.style.setProperty('--tooltip-color', 'rgba(106, 13, 173, 0.9)');
            
            // Show tooltip
            profileTooltip.classList.add('visible');
        });
        
        // Hide tooltip when mouse leaves
        item.addEventListener('mouseleave', function() {
            profileTooltip.classList.remove('visible');
        });
    });
}

/**
 * Show a notification
 * 
 * @param {string} title - The title of the notification
 * @param {string} message - The message to display
 */
function showNotification(title, message) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = 'feature-notification';
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-eye"></i>
            <div class="notification-text">
                <strong>${title}</strong>
                <p>${message}</p>
            </div>
            <button class="notification-close"><i class="fas fa-times"></i></button>
        </div>
    `;
    
    // Add inline styles for the notification
    notification.style.position = 'fixed';
    notification.style.bottom = '20px';
    notification.style.right = '20px';
    notification.style.backgroundColor = 'white';
    notification.style.borderRadius = '8px';
    notification.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
    notification.style.zIndex = '9999';
    notification.style.overflow = 'hidden';
    notification.style.maxWidth = '350px';
    notification.style.animation = 'slideIn 0.3s forwards';
    
    // Styles for notification content
    const notificationContent = notification.querySelector('.notification-content');
    notificationContent.style.display = 'flex';
    notificationContent.style.padding = '15px';
    notificationContent.style.alignItems = 'center';
    
    // Styles for icon
    const icon = notification.querySelector('.fas.fa-eye');
    icon.style.color = '#2ecc71';
    icon.style.fontSize = '24px';
    icon.style.marginRight = '15px';
    icon.style.textShadow = '0 0 5px rgba(46, 204, 113, 0.5)';
    
    // Styles for text
    const notificationText = notification.querySelector('.notification-text');
    notificationText.style.flex = '1';
    
    // Styles for close button
    const closeButton = notification.querySelector('.notification-close');
    closeButton.style.background = 'none';
    closeButton.style.border = 'none';
    closeButton.style.cursor = 'pointer';
    closeButton.style.fontSize = '16px';
    closeButton.style.color = '#666';
    
    // Add notification to DOM
    document.body.appendChild(notification);
    
    // Add event for closing notification
    closeButton.addEventListener('click', function() {
        notification.style.animation = 'slideOut 0.3s forwards';
        setTimeout(() => {
            notification.remove();
        }, 300);
    });
    
    // Auto-close after 5 seconds
    setTimeout(() => {
        if (document.body.contains(notification)) {
            notification.style.animation = 'slideOut 0.3s forwards';
            setTimeout(() => {
                notification.remove();
            }, 300);
        }
    }, 5000);
}
