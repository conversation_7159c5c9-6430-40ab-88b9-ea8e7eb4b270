/**
 * <PERSON><PERSON> Component
 * 
 * A reusable button component with different variants and sizes.
 */

class Button {
  /**
   * Create a button component
   * 
   * @param {Object} options - Button options
   * @param {string} options.text - Button text
   * @param {string} options.variant - Button variant (primary, secondary, outline, text)
   * @param {string} options.size - Button size (small, medium, large)
   * @param {string} options.icon - Button icon class (optional)
   * @param {boolean} options.disabled - Whether the button is disabled
   * @param {Function} options.onClick - Click event handler
   */
  constructor(options) {
    this.text = options.text || '';
    this.variant = options.variant || 'primary';
    this.size = options.size || 'medium';
    this.icon = options.icon || null;
    this.disabled = options.disabled || false;
    this.onClick = options.onClick || null;
    
    this.element = this.createButtonElement();
  }
  
  /**
   * Create the button element
   * 
   * @returns {HTMLElement} Button element
   */
  createButtonElement() {
    const button = document.createElement('button');
    
    // Add classes
    button.classList.add('btn');
    button.classList.add(`btn-${this.variant}`);
    button.classList.add(`btn-${this.size}`);
    
    if (this.disabled) {
      button.classList.add('btn-disabled');
      button.disabled = true;
    }
    
    // Add icon if provided
    if (this.icon) {
      const iconElement = document.createElement('i');
      iconElement.className = this.icon;
      button.appendChild(iconElement);
      
      if (this.text) {
        // Add space between icon and text
        button.appendChild(document.createTextNode(' '));
      }
    }
    
    // Add text if provided
    if (this.text) {
      const textNode = document.createTextNode(this.text);
      button.appendChild(textNode);
    }
    
    // Add click event handler
    if (this.onClick && !this.disabled) {
      button.addEventListener('click', this.onClick);
    }
    
    return button;
  }
  
  /**
   * Render the button to a container
   * 
   * @param {HTMLElement} container - Container element
   */
  render(container) {
    if (container) {
      container.appendChild(this.element);
    }
  }
  
  /**
   * Update button text
   * 
   * @param {string} text - New button text
   */
  setText(text) {
    this.text = text;
    
    // Clear button content
    this.element.innerHTML = '';
    
    // Re-add icon if provided
    if (this.icon) {
      const iconElement = document.createElement('i');
      iconElement.className = this.icon;
      this.element.appendChild(iconElement);
      
      if (this.text) {
        // Add space between icon and text
        this.element.appendChild(document.createTextNode(' '));
      }
    }
    
    // Add new text
    if (this.text) {
      const textNode = document.createTextNode(this.text);
      this.element.appendChild(textNode);
    }
  }
  
  /**
   * Enable or disable the button
   * 
   * @param {boolean} disabled - Whether the button should be disabled
   */
  setDisabled(disabled) {
    this.disabled = disabled;
    
    if (disabled) {
      this.element.classList.add('btn-disabled');
      this.element.disabled = true;
      
      // Remove click event handler
      if (this.onClick) {
        this.element.removeEventListener('click', this.onClick);
      }
    } else {
      this.element.classList.remove('btn-disabled');
      this.element.disabled = false;
      
      // Add click event handler
      if (this.onClick) {
        this.element.addEventListener('click', this.onClick);
      }
    }
  }
  
  /**
   * Change button variant
   * 
   * @param {string} variant - New button variant
   */
  setVariant(variant) {
    // Remove current variant class
    this.element.classList.remove(`btn-${this.variant}`);
    
    // Update variant
    this.variant = variant;
    
    // Add new variant class
    this.element.classList.add(`btn-${this.variant}`);
  }
  
  /**
   * Change button size
   * 
   * @param {string} size - New button size
   */
  setSize(size) {
    // Remove current size class
    this.element.classList.remove(`btn-${this.size}`);
    
    // Update size
    this.size = size;
    
    // Add new size class
    this.element.classList.add(`btn-${this.size}`);
  }
  
  /**
   * Change button icon
   * 
   * @param {string} icon - New button icon class
   */
  setIcon(icon) {
    this.icon = icon;
    
    // Clear button content
    this.element.innerHTML = '';
    
    // Re-add icon if provided
    if (this.icon) {
      const iconElement = document.createElement('i');
      iconElement.className = this.icon;
      this.element.appendChild(iconElement);
      
      if (this.text) {
        // Add space between icon and text
        this.element.appendChild(document.createTextNode(' '));
      }
    }
    
    // Re-add text
    if (this.text) {
      const textNode = document.createTextNode(this.text);
      this.element.appendChild(textNode);
    }
  }
}

// Export the Button class
if (typeof module !== 'undefined' && module.exports) {
  module.exports = Button;
}
