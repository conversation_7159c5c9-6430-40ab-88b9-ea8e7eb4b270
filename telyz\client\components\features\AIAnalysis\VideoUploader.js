/**
 * Video Uploader Component
 * 
 * Component for uploading videos for AI analysis.
 */

class VideoUploader {
  /**
   * Create a video uploader component
   * 
   * @param {Object} options - Component options
   * @param {string} options.apiEndpoint - API endpoint for video upload
   * @param {string} options.sport - Sport type
   * @param {string} options.position - Player position
   * @param {Function} options.onUploadStart - Callback when upload starts
   * @param {Function} options.onUploadProgress - Callback for upload progress
   * @param {Function} options.onUploadComplete - Callback when upload completes
   * @param {Function} options.onUploadError - Callback when upload fails
   */
  constructor(options) {
    this.apiEndpoint = options.apiEndpoint || '/api/ai-analysis/upload';
    this.sport = options.sport || '';
    this.position = options.position || '';
    this.onUploadStart = options.onUploadStart || null;
    this.onUploadProgress = options.onUploadProgress || null;
    this.onUploadComplete = options.onUploadComplete || null;
    this.onUploadError = options.onUploadError || null;
    
    this.element = this.createUploaderElement();
    this.fileInput = this.element.querySelector('#video-file-input');
    this.dropArea = this.element.querySelector('.upload-drop-area');
    this.browseButton = this.element.querySelector('.upload-browse-button');
    this.fileInfo = this.element.querySelector('.upload-file-info');
    this.uploadButton = this.element.querySelector('.upload-button');
    this.progressBar = this.element.querySelector('.upload-progress-bar');
    this.progressText = this.element.querySelector('.upload-progress-text');
    
    this.selectedFile = null;
    
    this.initEventListeners();
  }
  
  /**
   * Create the uploader element
   * 
   * @returns {HTMLElement} Uploader element
   */
  createUploaderElement() {
    const uploader = document.createElement('div');
    uploader.classList.add('video-uploader');
    
    uploader.innerHTML = `
      <div class="upload-container">
        <div class="upload-drop-area">
          <i class="fas fa-cloud-upload-alt"></i>
          <p>Drag and drop your video file here</p>
          <p>or</p>
          <button class="upload-browse-button">Browse Files</button>
          <input type="file" id="video-file-input" accept="video/*" hidden>
        </div>
        
        <div class="upload-file-info" style="display: none;">
          <div class="file-preview">
            <i class="fas fa-file-video"></i>
          </div>
          <div class="file-details">
            <h4 class="file-name"></h4>
            <p class="file-size"></p>
            <p class="file-type"></p>
          </div>
          <button class="file-remove-button">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <div class="upload-form">
          <div class="form-group">
            <label for="upload-title">Title</label>
            <input type="text" id="upload-title" placeholder="Enter a title for your analysis">
          </div>
          
          <div class="form-group">
            <label for="upload-description">Description</label>
            <textarea id="upload-description" placeholder="Describe what you want to analyze"></textarea>
          </div>
          
          <div class="form-group">
            <label for="upload-sport">Sport</label>
            <select id="upload-sport">
              <option value="">Select a sport</option>
              <option value="football">Football</option>
              <option value="basketball">Basketball</option>
              <option value="volleyball">Volleyball</option>
              <option value="baseball">Baseball</option>
              <option value="cricket">Cricket</option>
              <option value="hockey">Hockey</option>
            </select>
          </div>
          
          <div class="form-group">
            <label for="upload-position">Position</label>
            <select id="upload-position">
              <option value="">Select a position</option>
              <!-- Positions will be populated based on selected sport -->
            </select>
          </div>
          
          <div class="upload-actions">
            <button class="upload-button" disabled>Upload Video</button>
          </div>
        </div>
        
        <div class="upload-progress" style="display: none;">
          <div class="upload-progress-bar">
            <div class="upload-progress-fill" style="width: 0%"></div>
          </div>
          <div class="upload-progress-text">0%</div>
        </div>
      </div>
      
      <div class="upload-info">
        <h3>Supported Formats</h3>
        <p>MP4, MOV, AVI (max 2GB)</p>
        
        <h3>What You'll Get</h3>
        <ul>
          <li>Detailed performance metrics</li>
          <li>Technical analysis of your play</li>
          <li>Comparison with professional standards</li>
          <li>Personalized improvement recommendations</li>
          <li>Downloadable PDF report</li>
        </ul>
        
        <div class="ai-coach-link">
          <h3>Need Personalized Advice?</h3>
          <p>Chat with our AI Coach for personalized guidance and feedback.</p>
          <a href="../../../ai-analysis/interface/chat.html" class="coach-chat-button">
            <i class="fas fa-comment-dots"></i>
            Chat with AI Coach
          </a>
        </div>
      </div>
    `;
    
    return uploader;
  }
  
  /**
   * Initialize event listeners
   */
  initEventListeners() {
    // File input change
    this.fileInput.addEventListener('change', (e) => {
      if (e.target.files.length > 0) {
        this.handleFileSelection(e.target.files[0]);
      }
    });
    
    // Browse button click
    this.browseButton.addEventListener('click', () => {
      this.fileInput.click();
    });
    
    // Drop area events
    this.dropArea.addEventListener('dragover', (e) => {
      e.preventDefault();
      this.dropArea.classList.add('drag-over');
    });
    
    this.dropArea.addEventListener('dragleave', () => {
      this.dropArea.classList.remove('drag-over');
    });
    
    this.dropArea.addEventListener('drop', (e) => {
      e.preventDefault();
      this.dropArea.classList.remove('drag-over');
      
      if (e.dataTransfer.files.length > 0) {
        this.handleFileSelection(e.dataTransfer.files[0]);
      }
    });
    
    // Remove file button
    const removeButton = this.element.querySelector('.file-remove-button');
    removeButton.addEventListener('click', () => {
      this.clearFileSelection();
    });
    
    // Sport selection change
    const sportSelect = this.element.querySelector('#upload-sport');
    sportSelect.addEventListener('change', () => {
      this.updatePositionOptions(sportSelect.value);
      this.validateForm();
    });
    
    // Set initial sport value if provided
    if (this.sport) {
      sportSelect.value = this.sport;
      this.updatePositionOptions(this.sport);
    }
    
    // Set initial position value if provided
    if (this.position) {
      const positionSelect = this.element.querySelector('#upload-position');
      positionSelect.value = this.position;
    }
    
    // Form validation
    const titleInput = this.element.querySelector('#upload-title');
    const descriptionInput = this.element.querySelector('#upload-description');
    const positionSelect = this.element.querySelector('#upload-position');
    
    [titleInput, descriptionInput, positionSelect].forEach(input => {
      input.addEventListener('input', () => {
        this.validateForm();
      });
    });
    
    // Upload button click
    this.uploadButton.addEventListener('click', () => {
      this.uploadVideo();
    });
  }
  
  /**
   * Handle file selection
   * 
   * @param {File} file - Selected file
   */
  handleFileSelection(file) {
    // Check if file is a video
    if (!file.type.startsWith('video/')) {
      alert('Please select a video file.');
      return;
    }
    
    // Check file size (max 2GB)
    if (file.size > 2 * 1024 * 1024 * 1024) {
      alert('File size exceeds the maximum limit of 2GB.');
      return;
    }
    
    this.selectedFile = file;
    
    // Update file info
    this.fileInfo.style.display = 'flex';
    this.dropArea.style.display = 'none';
    
    const fileName = this.fileInfo.querySelector('.file-name');
    const fileSize = this.fileInfo.querySelector('.file-size');
    const fileType = this.fileInfo.querySelector('.file-type');
    
    fileName.textContent = file.name;
    fileSize.textContent = this.formatFileSize(file.size);
    fileType.textContent = file.type;
    
    this.validateForm();
  }
  
  /**
   * Clear file selection
   */
  clearFileSelection() {
    this.selectedFile = null;
    this.fileInput.value = '';
    
    this.fileInfo.style.display = 'none';
    this.dropArea.style.display = 'flex';
    
    this.validateForm();
  }
  
  /**
   * Update position options based on selected sport
   * 
   * @param {string} sport - Selected sport
   */
  updatePositionOptions(sport) {
    const positionSelect = this.element.querySelector('#upload-position');
    positionSelect.innerHTML = '<option value="">Select a position</option>';
    
    let positions = [];
    
    switch (sport) {
      case 'football':
        positions = [
          { value: 'goalkeeper', label: 'Goalkeeper' },
          { value: 'defender', label: 'Defender' },
          { value: 'midfielder', label: 'Midfielder' },
          { value: 'forward', label: 'Forward' },
        ];
        break;
      case 'basketball':
        positions = [
          { value: 'point_guard', label: 'Point Guard' },
          { value: 'shooting_guard', label: 'Shooting Guard' },
          { value: 'small_forward', label: 'Small Forward' },
          { value: 'power_forward', label: 'Power Forward' },
          { value: 'center', label: 'Center' },
        ];
        break;
      case 'volleyball':
        positions = [
          { value: 'setter', label: 'Setter' },
          { value: 'outside_hitter', label: 'Outside Hitter' },
          { value: 'opposite_hitter', label: 'Opposite Hitter' },
          { value: 'middle_blocker', label: 'Middle Blocker' },
          { value: 'libero', label: 'Libero' },
        ];
        break;
      // Add more sports as needed
    }
    
    positions.forEach(position => {
      const option = document.createElement('option');
      option.value = position.value;
      option.textContent = position.label;
      positionSelect.appendChild(option);
    });
  }
  
  /**
   * Validate form inputs
   */
  validateForm() {
    const titleInput = this.element.querySelector('#upload-title');
    const sportSelect = this.element.querySelector('#upload-sport');
    const positionSelect = this.element.querySelector('#upload-position');
    
    const isValid = this.selectedFile &&
                   titleInput.value.trim() !== '' &&
                   sportSelect.value !== '' &&
                   positionSelect.value !== '';
    
    this.uploadButton.disabled = !isValid;
  }
  
  /**
   * Upload video to server
   */
  uploadVideo() {
    if (!this.selectedFile) {
      return;
    }
    
    const titleInput = this.element.querySelector('#upload-title');
    const descriptionInput = this.element.querySelector('#upload-description');
    const sportSelect = this.element.querySelector('#upload-sport');
    const positionSelect = this.element.querySelector('#upload-position');
    
    const formData = new FormData();
    formData.append('video', this.selectedFile);
    formData.append('title', titleInput.value);
    formData.append('description', descriptionInput.value);
    formData.append('sport', sportSelect.value);
    formData.append('position', positionSelect.value);
    
    // Show progress bar
    const progressContainer = this.element.querySelector('.upload-progress');
    progressContainer.style.display = 'block';
    
    // Disable upload button
    this.uploadButton.disabled = true;
    this.uploadButton.textContent = 'Uploading...';
    
    // Call onUploadStart callback
    if (this.onUploadStart) {
      this.onUploadStart();
    }
    
    // Create XMLHttpRequest
    const xhr = new XMLHttpRequest();
    
    // Upload progress event
    xhr.upload.addEventListener('progress', (e) => {
      if (e.lengthComputable) {
        const percentComplete = Math.round((e.loaded / e.total) * 100);
        
        // Update progress bar
        const progressFill = this.element.querySelector('.upload-progress-fill');
        progressFill.style.width = `${percentComplete}%`;
        
        // Update progress text
        this.progressText.textContent = `${percentComplete}%`;
        
        // Call onUploadProgress callback
        if (this.onUploadProgress) {
          this.onUploadProgress(percentComplete);
        }
      }
    });
    
    // Upload complete event
    xhr.addEventListener('load', () => {
      if (xhr.status >= 200 && xhr.status < 300) {
        // Upload successful
        const response = JSON.parse(xhr.responseText);
        
        // Call onUploadComplete callback
        if (this.onUploadComplete) {
          this.onUploadComplete(response);
        }
      } else {
        // Upload failed
        let errorMessage = 'Upload failed';
        
        try {
          const response = JSON.parse(xhr.responseText);
          errorMessage = response.message || errorMessage;
        } catch (e) {
          // Ignore parsing error
        }
        
        // Call onUploadError callback
        if (this.onUploadError) {
          this.onUploadError(errorMessage);
        }
      }
      
      // Reset form
      this.resetForm();
    });
    
    // Upload error event
    xhr.addEventListener('error', () => {
      // Call onUploadError callback
      if (this.onUploadError) {
        this.onUploadError('Network error occurred during upload');
      }
      
      // Reset form
      this.resetForm();
    });
    
    // Open and send request
    xhr.open('POST', this.apiEndpoint, true);
    xhr.send(formData);
  }
  
  /**
   * Reset form after upload
   */
  resetForm() {
    // Hide progress bar
    const progressContainer = this.element.querySelector('.upload-progress');
    progressContainer.style.display = 'none';
    
    // Reset progress bar
    const progressFill = this.element.querySelector('.upload-progress-fill');
    progressFill.style.width = '0%';
    
    // Reset progress text
    this.progressText.textContent = '0%';
    
    // Reset upload button
    this.uploadButton.disabled = false;
    this.uploadButton.textContent = 'Upload Video';
    
    // Clear file selection
    this.clearFileSelection();
    
    // Reset form inputs
    const titleInput = this.element.querySelector('#upload-title');
    const descriptionInput = this.element.querySelector('#upload-description');
    
    titleInput.value = '';
    descriptionInput.value = '';
  }
  
  /**
   * Format file size in bytes to human-readable format
   * 
   * @param {number} bytes - File size in bytes
   * @returns {string} Formatted file size
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
  
  /**
   * Render the uploader to a container
   * 
   * @param {HTMLElement} container - Container element
   */
  render(container) {
    if (container) {
      container.appendChild(this.element);
    }
  }
}

// Export the VideoUploader class
if (typeof module !== 'undefined' && module.exports) {
  module.exports = VideoUploader;
}
