/**
 * Validators Utility
 * 
 * Provides utility functions for validating data in the Telyz platform.
 */

/**
 * Validate an email address
 * 
 * @param {string} email - The email address to validate
 * @returns {boolean} Whether the email is valid
 */
const validateEmail = (email) => {
  if (!email) return false;
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validate a password
 * 
 * @param {string} password - The password to validate
 * @param {Object} options - Validation options
 * @param {number} options.minLength - Minimum length (default: 8)
 * @param {boolean} options.requireUppercase - Require uppercase letter (default: true)
 * @param {boolean} options.requireLowercase - Require lowercase letter (default: true)
 * @param {boolean} options.requireNumber - Require number (default: true)
 * @param {boolean} options.requireSpecial - Require special character (default: true)
 * @returns {Object} Validation result with isValid and errors
 */
const validatePassword = (password, options = {}) => {
  const {
    minLength = 8,
    requireUppercase = true,
    requireLowercase = true,
    requireNumber = true,
    requireSpecial = true,
  } = options;
  
  const errors = [];
  
  if (!password) {
    errors.push('Password is required');
    return { isValid: false, errors };
  }
  
  if (password.length < minLength) {
    errors.push(`Password must be at least ${minLength} characters long`);
  }
  
  if (requireUppercase && !/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (requireLowercase && !/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (requireNumber && !/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  if (requireSpecial && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Validate a URL
 * 
 * @param {string} url - The URL to validate
 * @returns {boolean} Whether the URL is valid
 */
const validateUrl = (url) => {
  if (!url) return false;
  
  try {
    new URL(url);
    return true;
  } catch (error) {
    return false;
  }
};

/**
 * Validate a date
 * 
 * @param {Date|string|number} date - The date to validate
 * @param {Object} options - Validation options
 * @param {Date|string|number} options.minDate - Minimum date
 * @param {Date|string|number} options.maxDate - Maximum date
 * @returns {boolean} Whether the date is valid
 */
const validateDate = (date, options = {}) => {
  const { minDate, maxDate } = options;
  
  const d = new Date(date);
  
  if (isNaN(d.getTime())) {
    return false;
  }
  
  if (minDate && d < new Date(minDate)) {
    return false;
  }
  
  if (maxDate && d > new Date(maxDate)) {
    return false;
  }
  
  return true;
};

/**
 * Validate a phone number
 * 
 * @param {string} phoneNumber - The phone number to validate
 * @returns {boolean} Whether the phone number is valid
 */
const validatePhoneNumber = (phoneNumber) => {
  if (!phoneNumber) return false;
  
  // Remove all non-digit characters
  const digits = phoneNumber.replace(/\D/g, '');
  
  // Check if the number of digits is valid (most countries have 7-15 digits)
  return digits.length >= 7 && digits.length <= 15;
};

/**
 * Validate a file type
 * 
 * @param {string} fileName - The file name
 * @param {string[]} allowedTypes - Array of allowed file extensions
 * @returns {boolean} Whether the file type is valid
 */
const validateFileType = (fileName, allowedTypes) => {
  if (!fileName || !allowedTypes || !allowedTypes.length) return false;
  
  const extension = fileName.split('.').pop().toLowerCase();
  return allowedTypes.includes(extension);
};

/**
 * Validate a file size
 * 
 * @param {number} fileSize - The file size in bytes
 * @param {number} maxSize - Maximum allowed size in bytes
 * @returns {boolean} Whether the file size is valid
 */
const validateFileSize = (fileSize, maxSize) => {
  if (typeof fileSize !== 'number' || typeof maxSize !== 'number') return false;
  
  return fileSize <= maxSize;
};

/**
 * Validate a name (first name, last name, etc.)
 * 
 * @param {string} name - The name to validate
 * @param {Object} options - Validation options
 * @param {number} options.minLength - Minimum length (default: 2)
 * @param {number} options.maxLength - Maximum length (default: 50)
 * @returns {boolean} Whether the name is valid
 */
const validateName = (name, options = {}) => {
  const { minLength = 2, maxLength = 50 } = options;
  
  if (!name) return false;
  
  return name.length >= minLength && name.length <= maxLength;
};

/**
 * Validate an age
 * 
 * @param {number} age - The age to validate
 * @param {Object} options - Validation options
 * @param {number} options.minAge - Minimum age (default: 0)
 * @param {number} options.maxAge - Maximum age (default: 120)
 * @returns {boolean} Whether the age is valid
 */
const validateAge = (age, options = {}) => {
  const { minAge = 0, maxAge = 120 } = options;
  
  if (typeof age !== 'number') return false;
  
  return age >= minAge && age <= maxAge;
};

/**
 * Validate a username
 * 
 * @param {string} username - The username to validate
 * @param {Object} options - Validation options
 * @param {number} options.minLength - Minimum length (default: 3)
 * @param {number} options.maxLength - Maximum length (default: 30)
 * @returns {boolean} Whether the username is valid
 */
const validateUsername = (username, options = {}) => {
  const { minLength = 3, maxLength = 30 } = options;
  
  if (!username) return false;
  
  // Username should only contain alphanumeric characters, underscores, and hyphens
  const usernameRegex = /^[a-zA-Z0-9_-]+$/;
  
  return (
    username.length >= minLength &&
    username.length <= maxLength &&
    usernameRegex.test(username)
  );
};

module.exports = {
  validateEmail,
  validatePassword,
  validateUrl,
  validateDate,
  validatePhoneNumber,
  validateFileType,
  validateFileSize,
  validateName,
  validateAge,
  validateUsername,
};
