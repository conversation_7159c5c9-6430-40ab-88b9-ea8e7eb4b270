/* Base styles from template3.css */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: '<PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: #f0f2f5;
    color: #333;
}

.container {
    display: flex;
    min-height: 100vh;
    max-width: 1155px;
    margin: 0 auto;
    background-color: #f0f2f5;
}

/* Sidebar styles (copied exactly from template3.css) */
.sidebar-left {
    background-color: #f0f2f5;
    padding: 15px 0;
    position: sticky;
    top: 0;
    height: 100vh;
    width: 297px;
    overflow-y: auto;
}

.logo {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

.logo-link {
    text-decoration: none;
    cursor: pointer;
    transition: transform 0.2s ease;
    display: block;
}

.logo-link:hover {
    transform: scale(1.05);
}

.logo-container {
    width: 40px;
    height: 40px;
    background-color: #6a0dad;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(106, 13, 173, 0.3);
}

.sidebar-menu {
    padding: 0 15px;
    max-height: calc(100vh - 100px);
    width: 100%;
}

.menu-title {
    font-size: 12px;
    color: #666;
    margin: 20px 0 8px;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.sidebar-menu ul {
    list-style: none;
}

.sidebar-menu > ul > li {
    margin: 4px 0;
    padding: 8px 12px;
    border-radius: 10px;
    transition: all 0.3s;
}

.sidebar-menu li:hover {
    background-color: rgba(106, 13, 173, 0.05);
}

/* Submenu de deportes */
.has-submenu {
    position: relative;
}

.submenu {
    display: none;
    list-style: none;
    padding-left: 0;
    margin-top: 5px;
    margin-bottom: 5px;
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 8px;
    overflow: visible;
    max-height: 0;
    transition: max-height 0.3s ease-in-out;
    width: 100%;
    min-width: 260px;
}

.has-submenu:hover .submenu {
    display: block;
    max-height: 400px;
}

.submenu li {
    margin: 0;
    padding: 8px 12px 8px 5px;
    min-width: 240px;
    position: relative;
}

.submenu li:hover {
    background-color: rgba(106, 13, 173, 0.1);
    transform: translateX(5px);
}

.submenu a {
    font-size: 14px;
    padding: 4px 0;
    white-space: normal;
    overflow: visible;
    text-overflow: clip;
    display: flex;
    align-items: center;
    width: 100%;
    line-height: 1.4;
}

.submenu a span {
    display: inline-block;
    white-space: normal;
    overflow: visible;
    width: auto;
}

.submenu i {
    font-size: 16px;
    margin-right: 10px;
    width: 20px;
    text-align: center;
    display: inline-flex;
    justify-content: center;
    align-items: center;
}

.sidebar-menu a {
    text-decoration: none;
    color: #4a0080;
    display: flex;
    align-items: center;
    font-size: 16px;
    padding: 6px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 500;
    transition: all 0.3s;
    position: relative;
}

.sidebar-menu a:hover {
    background-color: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
    transform: translateX(2px);
}

.sidebar-menu a.active {
    color: #6a0dad;
    font-weight: 600;
}

.sidebar-menu i {
    margin-right: 15px;
    font-size: 20px;
    width: 25px;
    text-align: center;
    color: #6a0dad;
}

/* Ajustes específicos para el menú AI Analysis */
.ai-submenu {
    overflow: visible !important;
    width: 260px;
    padding: 8px 0;
}

.ai-submenu li {
    overflow: visible;
    padding: 12px 15px 12px 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.03);
}

.ai-submenu li:last-child {
    border-bottom: none;
}

.ai-submenu a {
    align-items: flex-start;
}

.ai-submenu a span {
    padding-top: 2px;
    font-weight: 500;
}

.ai-submenu i {
    font-size: 18px;
    margin-right: 12px;
    margin-left: 0;
}

/* Badge Styles */
.badge {
    background: #ff4757;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 8px;
    margin-left: auto;
    font-weight: 500;
}

.new-badge {
    background: linear-gradient(45deg, #ff6b6b, #ff4757);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Submenu Styles */
.submenu {
    max-height: 0;
    overflow: hidden;
    background: rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.has-submenu.active .submenu {
    max-height: 400px;
}

.submenu a {
    padding: 10px 24px 10px 56px;
    font-size: 14px;
    border-left: 3px solid transparent;
}

.submenu a:hover {
    background: rgba(255, 255, 255, 0.1);
    border-left-color: rgba(255, 255, 255, 0.5);
}

/* Colores específicos para cada opción en el menú de AI Analysis */
.ai-submenu li:nth-child(1) i { color: #FF5722; } /* Video Analysis */
.ai-submenu li:nth-child(2) i { color: #4CAF50; } /* Performance Stats */
.ai-submenu li:nth-child(3) i { color: #2196F3; } /* Player Comparison */
.ai-submenu li:nth-child(4) i { color: #FFC107; } /* Training Recommendations */
.ai-submenu li:nth-child(5) i { color: #9C27B0; } /* Talent Benchmarks */

/* Colores específicos para cada deporte en el menú - Variaciones de púrpura */
.submenu li:nth-child(1) i { color: #8e44ad; } /* Football - Púrpura medio */
.submenu li:nth-child(2) i { color: #9b59b6; } /* Basketball - Púrpura claro */
.submenu li:nth-child(3) i { color: #6a0dad; } /* Volleyball - Púrpura principal */
.submenu li:nth-child(4) i { color: #5d3fd3; } /* Baseball - Púrpura azulado */
.submenu li:nth-child(5) i { color: #7d3c98; } /* Cricket - Púrpura oscuro */
.submenu li:nth-child(6) i { color: #a569bd; } /* Field Hockey - Púrpura rosado */

/* User profile sidebar */
.user-profile-sidebar {
    display: flex;
    align-items: center;
    margin-top: 23px;
    padding: 12px 18px;
    border-radius: 10px;
    background-color: rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
}

.user-profile-sidebar:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.profile-pic-small {
    width: 46px;
    height: 46px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 12px;
    border: 2px solid #6a0dad;
}

.profile-pic-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-info-sidebar {
    flex: 1;
}

.profile-info-sidebar span:first-child {
    display: block;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.teams-clubs {
    font-size: 12px;
    color: #666;
    margin-top: 2px;
}

.profile-dropdown-toggle {
    margin-left: auto;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    border-radius: 50%;
    transition: all 0.2s;
    background-color: rgba(0, 0, 0, 0.05);
    cursor: pointer;
}

.profile-dropdown-toggle:hover {
    background-color: rgba(0, 0, 0, 0.1);
    color: #0066cc;
    transform: scale(1.1);
}

.profile-dropdown-toggle i {
    font-size: 20px;
    color: #444;
}

/* Profile dropdown menu */
.profile-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.2);
    width: 220px;
    z-index: 1000;
    overflow: hidden;
    display: none;
    border: 1px solid #eee;
    margin-top: 6px;
    max-height: 0;
    opacity: 0;
    transition: max-height 0.3s ease, opacity 0.3s ease;
    visibility: hidden;
    text-align: left;
}

.profile-dropdown-menu.show {
    display: block;
    max-height: 400px;
    opacity: 1;
    overflow-y: auto;
    z-index: 1001;
    visibility: visible;
}

.profile-dropdown-item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 14px 25px;
    text-decoration: none;
    color: #333;
    transition: all 0.2s ease;
    font-size: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.03);
    position: relative;
}

.profile-dropdown-item:last-child {
    border-bottom: none;
}

.profile-dropdown-item:hover {
    background-color: #f5f5f5;
    color: #6a0dad;
}

.profile-dropdown-item i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
    color: #6a0dad;
    font-size: 17px;
    display: inline-block;
}

.dropdown-divider {
    height: 1px;
    background-color: #e0e0e0;
    margin: 8px 0;
}

.logout-item {
    color: #e74c3c;
}

.logout-item:hover {
    background-color: rgba(231, 76, 60, 0.05);
    color: #e74c3c;
}

/* Estilos para los tooltips en el menú AI Analysis */
.js-tooltip {
    position: fixed;
    width: 220px;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px;
    border-radius: 6px;
    font-size: 12px;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
    z-index: 1001;
    line-height: 1.4;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    pointer-events: none;
    text-align: left;
}

.js-tooltip::before {
    content: "";
    position: absolute;
    top: 50%;
    left: -10px;
    transform: translateY(-50%);
    border-width: 5px;
    border-style: solid;
    border-color: transparent var(--tooltip-color, rgba(0, 0, 0, 0.8)) transparent transparent;
}

.js-tooltip.visible {
    opacity: 1;
    visibility: visible;
}

/* ================ MAIN CONTENT ================ */
.main-content {
    flex: 1;
    padding: 20px;
    background-color: white;
    border-radius: 15px;
    margin: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
    max-height: calc(100vh - 40px);
}

/* ================ NOTIFICATIONS HEADER ================ */
.notifications-header {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #f0f2f5;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-title h1 {
    color: #6a0dad;
    font-size: 28px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-title h1 i {
    color: #6a0dad;
    margin-right: 10px;
}

.header-title p {
    color: #666;
    font-size: 16px;
    margin: 0;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

.mark-all-read-btn {
    background: linear-gradient(135deg, #6a0dad, #9b4dca);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 10px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.mark-all-read-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(106, 13, 173, 0.3);
}

.notification-counter {
    background: #f8f9fa;
    padding: 12px 16px;
    border-radius: 10px;
    font-weight: 500;
    color: #2c3e50;
}

.unread-count {
    background: #ff4757;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    margin-right: 8px;
}

/* ================ NOTIFICATION FILTERS ================ */
.notification-filters {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 25px;
    border: 1px solid #e9ecef;
}

.filter-tabs {
    display: flex;
    gap: 8px;
    margin-bottom: 20px;
    overflow-x: auto;
    padding-bottom: 4px;
}

.filter-tab {
    background: #f8f9fa;
    border: none;
    padding: 12px 16px;
    border-radius: 10px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #7f8c8d;
}

.filter-tab:hover {
    background: #e9ecef;
    color: #6a0dad;
}

.filter-tab.active {
    background: linear-gradient(135deg, #6a0dad, #9b4dca);
    color: white;
}

.filter-status {
    display: flex;
    gap: 16px;
}

.status-filter, .priority-filter {
    padding: 10px 16px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    background: white;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    outline: none;
}

.status-filter:focus, .priority-filter:focus {
    border-color: #6a0dad;
    box-shadow: 0 0 0 3px rgba(106, 13, 173, 0.1);
}

/* ================ NOTIFICATIONS LIST ================ */
.notifications-container {
    background: #f8f9fa;
    border-radius: 10px;
    border: 1px solid #e9ecef;
    overflow: hidden;
}

.notifications-list {
    max-height: 800px;
    overflow-y: auto;
}

/* ================ NOTIFICATION ITEM ================ */
.notification-item {
    display: flex;
    padding: 20px;
    background: white;
    border-radius: 8px;
    margin-bottom: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
}

.notification-item:last-child {
    margin-bottom: 0;
}

.notification-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.notification-item.unread {
    border-left: 4px solid #6a0dad;
    background: #fefffe;
}

.notification-item.high-priority {
    border-left-color: #ff4757;
    background: #fff9f9;
}

.notification-item.read {
    opacity: 0.8;
}

/* Notification Icon */
.notification-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    flex-shrink: 0;
}

.notification-icon.messages {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.notification-icon.opportunities {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
}

.notification-icon.connections {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
}

.notification-icon.training {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
    color: white;
}

.notification-icon.achievements {
    background: linear-gradient(135deg, #f1c40f, #f39c12);
    color: white;
}

.notification-icon.events {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

/* Notification Content */
.notification-content {
    flex: 1;
    min-width: 0;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
}

.notification-title {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.sender {
    font-weight: 600;
    color: #2c3e50;
    font-size: 15px;
}

.priority-badge {
    background: #ff4757;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 600;
    text-transform: uppercase;
}

.priority-badge.high {
    background: #ff4757;
    animation: pulse 2s infinite;
}

.unread-dot {
    width: 8px;
    height: 8px;
    background: #6a0dad;
    border-radius: 50%;
}

.notification-time {
    color: #7f8c8d;
    font-size: 13px;
    font-weight: 500;
    white-space: nowrap;
}

.notification-message {
    color: #34495e;
    line-height: 1.5;
    margin-bottom: 12px;
    font-size: 14px;
}

/* Notification Actions */
.notification-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.action-btn {
    border: none;
    padding: 8px 16px;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 13px;
}

.action-btn.primary {
    background: linear-gradient(135deg, #6a0dad, #9b4dca);
    color: white;
}

.action-btn.primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(106, 13, 173, 0.3);
}

.action-btn.secondary {
    background: #f8f9fa;
    color: #6a0dad;
    border: 1px solid #e9ecef;
}

.action-btn.secondary:hover {
    background: #e9ecef;
    border-color: #6a0dad;
}

/* Notification Controls */
.notification-controls {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-left: 16px;
}

.control-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: #f8f9fa;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #7f8c8d;
    font-size: 12px;
}

.control-btn:hover {
    background: #e9ecef;
    color: #6a0dad;
}

.control-btn.mark-read:hover {
    background: #d4edda;
    color: #155724;
}

.control-btn.mark-unread:hover {
    background: #cce5ff;
    color: #0056b3;
}

.control-btn.delete:hover {
    background: #f8d7da;
    color: #721c24;
}

/* ================ EMPTY STATE ================ */
.empty-state {
    text-align: center;
    padding: 60px 24px;
    color: #7f8c8d;
}

.empty-icon {
    font-size: 48px;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-state h3 {
    margin-bottom: 12px;
    color: #2c3e50;
}

.empty-state p {
    font-size: 14px;
}

/* ================ LOAD MORE ================ */
.load-more-container {
    padding: 24px;
    text-align: center;
    border-top: 1px solid #f1f3f4;
}

.load-more-btn {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    padding: 12px 24px;
    border-radius: 10px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #6a0dad;
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 auto;
}

.load-more-btn:hover {
    background: #e9ecef;
    border-color: #6a0dad;
    transform: translateY(-1px);
}

/* ================ RESPONSIVE DESIGN ================ */
@media (max-width: 1024px) {
    .sidebar-left {
        width: 260px;
    }
    
    .main-content {
        margin-left: 260px;
    }
}

@media (max-width: 768px) {
    .sidebar-left {
        width: 100%;
        position: fixed;
        left: -100%;
        z-index: 1050;
    }
    
    .sidebar-left.active {
        left: 0;
    }
    
    .main-content {
        margin-left: 0;
        padding: 16px;
    }
    
    .header-content {
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;
    }
    
    .header-actions {
        flex-direction: column;
        width: 100%;
    }
    
    .filter-tabs {
        flex-wrap: wrap;
    }
    
    .filter-status {
        flex-direction: column;
    }
    
    .notification-item {
        padding: 16px;
    }
    
    .notification-controls {
        flex-direction: row;
        margin-left: 8px;
        margin-top: 8px;
    }
    
    .notification-actions {
        margin-top: 8px;
    }
}

@media (max-width: 480px) {
    .notifications-header {
        padding: 20px;
    }
    
    .notification-filters {
        padding: 16px;
    }
    
    .notification-item {
        flex-direction: column;
        gap: 12px;
    }
    
    .notification-icon {
        align-self: flex-start;
    }
    
    .notification-header {
        flex-direction: column;
        gap: 4px;
    }
    
    .notification-controls {
        justify-content: flex-end;
        margin: 0;
    }
}

/* ================ ANIMATIONS ================ */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.notification-item {
    animation: slideIn 0.3s ease;
}

/* ================ SCROLLBAR STYLING ================ */
.notifications-list::-webkit-scrollbar {
    width: 6px;
}

.notifications-list::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.notifications-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.notifications-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.sidebar-left::-webkit-scrollbar {
    width: 4px;
}

.sidebar-left::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.sidebar-left::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
}

.sidebar-left::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}