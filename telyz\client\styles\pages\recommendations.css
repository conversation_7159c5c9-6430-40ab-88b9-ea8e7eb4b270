/* Styles for the Sports Experts Recommendations page */

/* General styles */
body {
    font-family: 'Roboto', sans-serif;
}

.recommendations-content {
    padding: 30px;
    max-width: 800px;
    margin: 0 auto;
}

/* Encabezado de la página */
.recommendations-header {
    margin-bottom: 30px;
    text-align: center;
}

.recommendations-header h1 {
    font-size: 28px;
    color: #6a0dad;
    margin-bottom: 10px;
    font-weight: 700;
}

.recommendations-subtitle {
    color: #666;
    font-size: 16px;
    margin-bottom: 25px;
}

/* Botones de acción */
.recommendations-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 25px;
}

.primary-button, .secondary-button {
    padding: 10px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    border: none;
}

.primary-button {
    background-color: #6a0dad;
    color: white;
}

.primary-button:hover {
    background-color: #5a0b8d;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(106, 13, 173, 0.2);
}

.secondary-button {
    background-color: #f0f0f0;
    color: #333;
}

.secondary-button:hover {
    background-color: #e0e0e0;
    transform: translateY(-2px);
}

.primary-button i, .secondary-button i {
    margin-left: 8px;
    font-size: 14px;
}

/* Estadísticas */
.recommendations-stats {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background-color: white;
    border-radius: 10px;
    padding: 15px 25px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    min-width: 120px;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #6a0dad;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 14px;
    color: #666;
}

.star-rating {
    color: #ffc107;
    font-size: 14px;
    margin-top: 5px;
}

/* Lista de recomendaciones */
.recommendations-list {
    margin-bottom: 40px;
}

.recommendations-list h2 {
    font-size: 20px;
    color: #333;
    margin-bottom: 20px;
    font-weight: 600;
}

.recommendation-card {
    background-color: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.recommendation-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.recommendation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.recommender-info {
    display: flex;
    align-items: center;
}

.recommender-pic {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    margin-left: 15px;
    border: 2px solid #6a0dad;
}

.recommender-details h3 {
    font-size: 16px;
    margin-bottom: 3px;
    color: #333;
}

.recommender-details p {
    font-size: 14px;
    color: #666;
    margin: 0;
}

.recommendation-date {
    font-size: 14px;
    color: #888;
}

.recommendation-rating {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.stars {
    color: #ffc107;
    font-size: 16px;
    margin-left: 10px;
}

.rating-value {
    font-weight: 600;
    color: #333;
}

.recommendation-content {
    margin-bottom: 20px;
}

.recommendation-text {
    font-size: 15px;
    line-height: 1.6;
    color: #444;
    margin-bottom: 15px;
}

.highlighted-skills {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.skill-tag {
    background-color: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 500;
}

.recommendation-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.view-full-btn {
    background: none;
    border: none;
    color: #6a0dad;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    padding: 5px 0;
}

.view-full-btn:hover {
    text-decoration: underline;
}

.verification-badge {
    display: flex;
    align-items: center;
    color: #4caf50;
    font-size: 14px;
}

.verification-badge i {
    margin-left: 5px;
}

.load-more-btn {
    display: block;
    width: 100%;
    padding: 12px;
    background-color: #f5f5f5;
    border: none;
    border-radius: 8px;
    color: #555;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s;
    margin-top: 20px;
}

.load-more-btn:hover {
    background-color: #e0e0e0;
}

/* Resumen de habilidades */
.skills-summary {
    background-color: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
}

.skills-summary h2 {
    font-size: 20px;
    color: #333;
    margin-bottom: 20px;
    font-weight: 600;
}

.skills-placeholder {
    padding: 10px;
}

.skill-bar {
    height: 35px;
    background-color: rgba(106, 13, 173, 0.2);
    border-radius: 5px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
    color: #6a0dad;
    font-weight: 500;
    font-size: 14px;
}

.skill-name {
    flex: 1;
}

.skill-count {
    background-color: #6a0dad;
    color: white;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

/* Responsive */
@media (max-width: 768px) {
    .recommendations-stats {
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }

    .stat-card {
        width: 100%;
        max-width: 250px;
    }

    .recommendation-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .recommendation-date {
        margin-top: 10px;
    }
}
