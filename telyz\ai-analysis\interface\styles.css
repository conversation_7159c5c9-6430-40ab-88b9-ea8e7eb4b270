/* Estilos generales */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: '<PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: #6a0dad; /* Fondo púrpura */
    color: white;
    min-height: 100vh;
}

.ai-container {
    display: flex;
    min-height: 100vh;
}

/* Barra lateral */
.ai-sidebar {
    width: 80px;
    background-color: rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 0;
}

.back-button {
    text-decoration: none;
    margin-bottom: 20px;
}

.logo-container {
    width: 50px;
    height: 50px;
    background-color: #0066cc;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    transition: transform 0.2s;
}

.logo-container:hover {
    transform: scale(1.05);
}

/* Contenido principal */
.ai-main-content {
    flex: 1;
    padding: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(135deg, #6a0dad 0%, #4a0080 100%);
}

/* Pantalla de bienvenida */
.ai-welcome-screen {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    max-width: 1200px;
    width: 100%;
}

.ai-header {
    margin-bottom: 40px;
}

.ai-header h1 {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.1;
}

.ai-header h1 span {
    display: block;
}

.ai-subtitle {
    font-size: 1.5rem;
    font-weight: 300;
    max-width: 600px;
    line-height: 1.5;
}

.ai-illustration {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    margin-bottom: 40px;
}

.ai-illustration img {
    max-width: 400px;
    height: auto;
}

.ai-actions {
    display: flex;
    gap: 20px;
}

.ai-button {
    padding: 12px 30px;
    border-radius: 30px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    border: none;
    outline: none;
}

.learn-more {
    background-color: #ff3366;
    color: white;
}

.upload-video {
    background-color: #00bcd4;
    color: white;
}

.ai-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Pantalla de carga de video */
.ai-upload-screen {
    width: 100%;
    max-width: 1200px;
}

.ai-upload-area {
    border: 2px dashed #00bcd4;
    border-radius: 10px;
    padding: 60px 20px;
    text-align: center;
    margin-bottom: 40px;
    background-color: rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s;
}

.ai-upload-area:hover {
    background-color: rgba(0, 0, 0, 0.2);
}

.ai-upload-area i {
    font-size: 3rem;
    margin-bottom: 20px;
    color: #00bcd4;
}

.ai-upload-area p {
    font-size: 1.2rem;
    margin-bottom: 5px;
}

.ai-upload-subtitle {
    font-size: 0.9rem !important;
    opacity: 0.7;
}

.ai-analysis-options {
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    padding: 30px;
}

.ai-option-row {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.ai-option {
    flex: 1;
    min-width: 200px;
}

.ai-option label {
    display: block;
    margin-bottom: 10px;
    font-size: 0.9rem;
    opacity: 0.8;
}

.ai-option select {
    width: 100%;
    padding: 12px 15px;
    border-radius: 5px;
    background-color: #333;
    color: white;
    border: none;
    outline: none;
    font-size: 1rem;
}

.ai-playback-speed {
    width: 100%;
}

.ai-speed-options {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.ai-speed-button {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #333;
    color: white;
    border: none;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.ai-speed-button.active {
    background-color: #00bcd4;
}

.ai-action-buttons {
    justify-content: center;
    gap: 15px;
}

.start-analysis {
    background-color: #00bcd4;
    color: white;
}

.reset {
    background-color: #ff3366;
    color: white;
}

.advanced-options {
    background-color: white;
    color: #333;
}

/* Pantalla de resultados */
.ai-results-screen {
    width: 100%;
    max-width: 1200px;
}

.ai-results-container {
    display: flex;
    gap: 30px;
    flex-wrap: wrap;
}

.ai-result-card {
    flex: 1;
    min-width: 300px;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    padding: 30px;
    border-left: 4px solid #00bcd4;
}

.ai-result-card h2 {
    font-size: 1.5rem;
    margin-bottom: 15px;
}

.ai-result-description {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 25px;
    line-height: 1.5;
}

.ai-metrics {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    background-color: rgba(0, 0, 0, 0.2);
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #00bcd4;
}

.ai-metric {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.ai-metric-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: #00bcd4;
}

.ai-metric-label {
    font-size: 0.8rem;
    opacity: 0.7;
}

.ai-result-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.view-report {
    background-color: #00bcd4;
    color: white;
}

.download-json {
    background-color: #ff3366;
    color: white;
}

.share {
    background-color: white;
    color: #333;
}

.ai-share-options {
    margin-top: 20px;
}

.ai-share-buttons {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.ai-share-button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #00bcd4;
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
}

.ai-share-button:hover {
    transform: translateY(-2px);
}

.ai-video-preview {
    margin: 20px 0;
    height: 200px;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ai-video-placeholder {
    text-align: center;
}

.ai-video-placeholder i {
    font-size: 3rem;
    margin-bottom: 10px;
    opacity: 0.5;
}

.download-video {
    background-color: #00bcd4;
    color: white;
    width: 100%;
}
