/**
 * API Service
 *
 * Service for making API requests to the server.
 */

class ApiService {
  /**
   * Create an API service
   *
   * @param {Object} options - Service options
   * @param {string} options.baseUrl - API base URL
   * @param {Function} options.onUnauthorized - Callback for unauthorized requests
   */
  constructor(options = {}) {
    this.baseUrl = options.baseUrl || '/api';
    this.onUnauthorized = options.onUnauthorized || null;

    // Bind methods
    this.get = this.get.bind(this);
    this.post = this.post.bind(this);
    this.put = this.put.bind(this);
    this.delete = this.delete.bind(this);
    this.upload = this.upload.bind(this);
  }

  /**
   * Get the authorization header
   *
   * @returns {Object} Headers object
   */
  getHeaders() {
    const headers = {
      'Content-Type': 'application/json',
    };

    const token = localStorage.getItem('token');
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return headers;
  }

  /**
   * Handle API response
   *
   * @param {Response} response - Fetch response
   * @returns {Promise} Promise with response data
   */
  async handleResponse(response) {
    const contentType = response.headers.get('content-type');

    if (response.status === 401) {
      // Unauthorized
      if (this.onUnauthorized) {
        this.onUnauthorized();
      }

      throw new Error('Unauthorized');
    }

    if (response.status >= 200 && response.status < 300) {
      // Success
      if (contentType && contentType.includes('application/json')) {
        return await response.json();
      }

      return await response.text();
    }

    // Error
    let error;

    try {
      if (contentType && contentType.includes('application/json')) {
        const data = await response.json();
        error = new Error(data.message || 'API Error');
        error.data = data;
      } else {
        error = new Error(await response.text() || 'API Error');
      }
    } catch (e) {
      error = new Error(`API Error: ${response.status}`);
    }

    error.status = response.status;
    throw error;
  }

  /**
   * Make a GET request
   *
   * @param {string} endpoint - API endpoint
   * @param {Object} params - Query parameters
   * @returns {Promise} Promise with response data
   */
  async get(endpoint, params = {}) {
    const url = new URL(`${this.baseUrl}${endpoint}`);

    // Add query parameters
    Object.keys(params).forEach(key => {
      if (params[key] !== undefined && params[key] !== null) {
        url.searchParams.append(key, params[key]);
      }
    });

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: this.getHeaders(),
    });

    return this.handleResponse(response);
  }

  /**
   * Make a POST request
   *
   * @param {string} endpoint - API endpoint
   * @param {Object} data - Request data
   * @returns {Promise} Promise with response data
   */
  async post(endpoint, data = {}) {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify(data),
    });

    return this.handleResponse(response);
  }

  /**
   * Make a PUT request
   *
   * @param {string} endpoint - API endpoint
   * @param {Object} data - Request data
   * @returns {Promise} Promise with response data
   */
  async put(endpoint, data = {}) {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'PUT',
      headers: this.getHeaders(),
      body: JSON.stringify(data),
    });

    return this.handleResponse(response);
  }

  /**
   * Make a DELETE request
   *
   * @param {string} endpoint - API endpoint
   * @param {Object} data - Request data
   * @returns {Promise} Promise with response data
   */
  async delete(endpoint, data = {}) {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'DELETE',
      headers: this.getHeaders(),
      body: Object.keys(data).length ? JSON.stringify(data) : undefined,
    });

    return this.handleResponse(response);
  }

  /**
   * Upload a file
   *
   * @param {string} endpoint - API endpoint
   * @param {FormData} formData - Form data with file
   * @param {Function} onProgress - Progress callback
   * @returns {Promise} Promise with response data
   */
  async upload(endpoint, formData, onProgress = null) {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();

      // Progress event
      if (onProgress) {
        xhr.upload.addEventListener('progress', (e) => {
          if (e.lengthComputable) {
            const percentComplete = Math.round((e.loaded / e.total) * 100);
            onProgress(percentComplete);
          }
        });
      }

      // Load event
      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          let response;

          try {
            response = JSON.parse(xhr.responseText);
          } catch (e) {
            response = xhr.responseText;
          }

          resolve(response);
        } else {
          let error;

          try {
            const data = JSON.parse(xhr.responseText);
            error = new Error(data.message || 'Upload failed');
            error.data = data;
          } catch (e) {
            error = new Error(xhr.responseText || 'Upload failed');
          }

          error.status = xhr.status;
          reject(error);
        }
      });

      // Error event
      xhr.addEventListener('error', () => {
        reject(new Error('Network error occurred during upload'));
      });

      // Abort event
      xhr.addEventListener('abort', () => {
        reject(new Error('Upload aborted'));
      });

      // Open and send request
      xhr.open('POST', `${this.baseUrl}${endpoint}`, true);

      // Add authorization header
      const token = localStorage.getItem('token');
      if (token) {
        xhr.setRequestHeader('Authorization', `Bearer ${token}`);
      }

      xhr.send(formData);
    });
  }
}

// Create API service instance
const api = new ApiService({
  baseUrl: '/api',
  onUnauthorized: () => {
    // Clear token
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');

    // Redirect to login page
    window.location.href = '/login';
  },
});

// Export API service
if (typeof module !== 'undefined' && module.exports) {
  module.exports = api;
} else {
  window.api = api;
}
