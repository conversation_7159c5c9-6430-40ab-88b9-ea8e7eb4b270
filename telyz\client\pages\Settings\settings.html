<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - Telyz</title>
    <link rel="stylesheet" href="settings.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Agregamos fuentes de Google -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Barra lateral izquierda -->
        <div class="sidebar-left">
            <div class="logo">
                <a href="../Home/index.html" class="logo-link">
                    <div class="logo-container">T</div>
                </a>
            </div>
            <div class="sidebar-menu">
                <ul>
                    <li class="has-submenu" id="aiMenuItem">
    <a href="#"><i class="fas fa-robot"></i> AI Analysis <span class="badge new-badge">NEW</span></a>
    <ul class="submenu ai-submenu" id="aiDropdownMenu">
        <li>
            <a href="../../ai-analysis/interface/index.html" data-description="Upload game footage for AI breakdown of techniques and plays" data-color="#FF5722">
                <i class="fas fa-video"></i> <span>Video Analysis</span>
            </a>
        </li>
        <li>
            <a href="#" data-description="View weekly stats of star players and compare with your performance" data-color="#4CAF50">
                <i class="fas fa-chart-line"></i> <span>Performance Stats</span>
            </a>
        </li>
        <li>
            <a href="#" data-description="Compare your metrics with other players in your position" data-color="#2196F3">
                <i class="fas fa-users"></i> <span>Player Comparison</span>
            </a>
        </li>
        <li>
            <a href="#" data-description="Get personalized training plans based on your performance data" data-color="#FFC107">
                <i class="fas fa-dumbbell"></i> <span>Training Recommendations</span>
            </a>
        </li>
        <li>
            <a href="#" data-description="See how you measure against professional standards in your sport" data-color="#9C27B0">
                <i class="fas fa-trophy"></i> <span>Talent Benchmarks</span>
            </a>
        </li>
    </ul>
</li>
                    <li><a href="#"><i class="fas fa-hashtag"></i> Explore</a></li>
                    <li><a href="#"><i class="fas fa-bullhorn"></i> Announcements</a></li>
                    <li><a href="../Messages/index.html"><i class="fas fa-envelope"></i> Messages</a></li>
                    <li class="has-submenu" id="sportsMenuItem">
                        <a href="#"><i class="fas fa-running"></i> Sports</a>
                        <ul class="submenu sports-submenu" id="sportsSubmenu">
                            <li><a href="#"><i class="fas fa-futbol"></i> Football</a></li>
                            <li><a href="#"><i class="fas fa-basketball-ball"></i> Basketball</a></li>
                            <li><a href="#"><i class="fas fa-volleyball-ball"></i> Volleyball</a></li>
                            <li><a href="#"><i class="fas fa-baseball-ball"></i> Baseball</a></li>
                            <li><a href="#"><i class="fas fa-table-tennis"></i> Cricket</a></li>
                            <li><a href="#"><i class="fas fa-hockey-puck"></i> Field Hockey</a></li>
                        </ul>
                    </li>
                    <li><a href="../Opportunities/opportunities.html"><i class="fas fa-briefcase"></i> Opportunities</a></li>
                    <li><a href="#"><i class="fas fa-trophy"></i> Achievements</a></li>
                    <li><a href="#"><i class="fas fa-dumbbell"></i> Training</a></li>
                    <li><a href="settings.html" class="active"><i class="fas fa-cog"></i> Settings</a></li>
                </ul>
                <div class="user-profile-sidebar">
                    <div class="profile-pic-small">
                        <img src="https://via.placeholder.com/40" alt="Profile" class="profile-pic-img">
                    </div>
                    <div class="profile-info-sidebar">
                        <span>John Doe</span>
                        <span class="teams-clubs">Teams & Clubs</span>
                    </div>
                    <div class="profile-dropdown-toggle" id="profileDropdownToggle">
                        <i class="fas fa-ellipsis-v"></i>
                    </div>
                    <div class="profile-dropdown-menu" id="profileDropdownMenu">
                        <a href="../Profile/athletic_profile.html" class="profile-dropdown-item" data-tooltip="View your complete athletic profile and career history"><i class="fas fa-user"></i> View Athletic Profile</a>
                        <a href="#" class="profile-dropdown-item" data-tooltip="Edit your athletic profile information, skills and achievements"><i class="fas fa-cog"></i> Edit Athletic Profile</a>
                        <a href="#" class="profile-dropdown-item" data-tooltip="View and manage your sports achievements, trophies and awards"><i class="fas fa-medal"></i> Achievements</a>
                        <a href="../SportsRecommendations/sports_recommendations.html" class="profile-dropdown-item" data-tooltip="View and manage recommendations from coaches, scouts and sports experts"><i class="fas fa-star"></i> Expert Recommendations</a>
                        <a href="#" class="profile-dropdown-item" data-tooltip="Control your athletic profile privacy and who can view it"><i class="fas fa-shield-alt"></i> Privacy Settings</a>
                        <div class="dropdown-divider"></div>
                        <a href="#" class="profile-dropdown-item logout-item" data-tooltip="Sign out from your account"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </div>
                </div>

                <script>
                    // JavaScript para controlar el menú desplegable
                    document.addEventListener('DOMContentLoaded', function() {
                        const toggleButton = document.getElementById('profileDropdownToggle');
                        const dropdownMenu = document.getElementById('profileDropdownMenu');
                        const userProfile = document.querySelector('.user-profile-sidebar');

                        let isMenuOpen = false;

                        // Función para mostrar/ocultar el menú
                        toggleButton.addEventListener('click', function(e) {
                            e.stopPropagation();
                            isMenuOpen = !isMenuOpen;
                            dropdownMenu.classList.toggle('show', isMenuOpen);

                            // Asegurarse de que los iconos sean visibles
                            if (isMenuOpen) {
                                setTimeout(() => {
                                    const icons = dropdownMenu.querySelectorAll('i');
                                    icons.forEach(icon => {
                                        icon.style.display = 'inline-block';
                                    });
                                }, 50);
                            }
                        });

                        // Cerrar el menú al hacer clic fuera de él
                        document.addEventListener('click', function(e) {
                            if (!dropdownMenu.contains(e.target) && !toggleButton.contains(e.target) && !userProfile.contains(e.target)) {
                                isMenuOpen = false;
                                dropdownMenu.classList.remove('show');
                            }
                        });

                        // Evitar que el menú se cierre al hacer clic dentro de él
                        dropdownMenu.addEventListener('click', function(e) {
                            e.stopPropagation();
                        });

                        // Evitar que el menú se cierre al pasar el ratón sobre él
                        dropdownMenu.addEventListener('mouseenter', function() {
                            if (isMenuOpen) {
                                dropdownMenu.classList.add('show');
                            }
                        });

                        // Configurar tooltips para el menú AI
                        const featureTooltip = document.getElementById('feature-tooltip');
                        const aiMenuItems = document.querySelectorAll('.ai-submenu a');

                        aiMenuItems.forEach((item) => {
                            // Mostrar tooltip al pasar el ratón
                            item.addEventListener('mouseenter', function(event) {
                                const description = this.getAttribute('data-description');
                                const color = this.getAttribute('data-color');
                                const rect = this.getBoundingClientRect();

                                // Configurar el tooltip
                                featureTooltip.textContent = description;
                                featureTooltip.style.backgroundColor = color;
                                featureTooltip.style.left = (rect.right + 15) + 'px';
                                featureTooltip.style.top = (rect.top + rect.height/2 - 20) + 'px';

                                // Configurar el color de la flecha
                                featureTooltip.style.setProperty('--tooltip-color', color);

                                // Mostrar el tooltip
                                featureTooltip.classList.add('visible');
                            });

                            // Ocultar tooltip al quitar el ratón
                            item.addEventListener('mouseleave', function() {
                                featureTooltip.classList.remove('visible');
                            });
                        });

                        // Configurar tooltips para el menú del perfil
                        const profileTooltip = document.getElementById('profile-tooltip');
                        const profileMenuItems = dropdownMenu.querySelectorAll('.profile-dropdown-item');

                        profileMenuItems.forEach((item) => {
                            // Mostrar tooltip al pasar el ratón
                            item.addEventListener('mouseenter', function(event) {
                                const description = this.getAttribute('data-tooltip');
                                const rect = this.getBoundingClientRect();

                                // Configurar el tooltip
                                profileTooltip.textContent = description;
                                profileTooltip.style.backgroundColor = 'rgba(106, 13, 173, 0.9)';
                                profileTooltip.style.left = (rect.right + 15) + 'px';
                                profileTooltip.style.top = (rect.top + rect.height/2 - 20) + 'px';

                                // Configurar el color de la flecha
                                profileTooltip.style.setProperty('--tooltip-color', 'rgba(106, 13, 173, 0.9)');

                                // Mostrar el tooltip
                                profileTooltip.classList.add('visible');
                            });

                            // Ocultar tooltip al quitar el ratón
                            item.addEventListener('mouseleave', function() {
                                profileTooltip.classList.remove('visible');
                            });
                        });
                    });
                </script>
            </div>
        </div>

        <!-- Contenido central -->
        <div class="main-content">
            <!-- Settings Header -->
            <div class="settings-header">
                <div class="header-content">
                    <div class="header-title">
                        <h1><i class="fas fa-cog"></i> Settings</h1>
                        <p>Manage your account, privacy, and preferences</p>
                    </div>
                    <div class="header-actions">
                        <button class="btn-secondary" id="resetAllBtn">
                            <i class="fas fa-undo"></i> Reset All
                        </button>
                        <button class="btn-primary" id="saveAllBtn">
                            <i class="fas fa-save"></i> Save Changes
                        </button>
                    </div>
                </div>
            </div>

            <!-- Settings Navigation -->
            <div class="settings-navigation">
                <div class="nav-tabs">
                    <button class="nav-tab active" data-section="profile">
                        <i class="fas fa-user"></i>
                        <span>Profile</span>
                    </button>
                    <button class="nav-tab" data-section="privacy">
                        <i class="fas fa-shield-alt"></i>
                        <span>Privacy & Security</span>
                    </button>
                    <button class="nav-tab" data-section="notifications">
                        <i class="fas fa-bell"></i>
                        <span>Notifications</span>
                    </button>
                    <button class="nav-tab" data-section="account">
                        <i class="fas fa-user-cog"></i>
                        <span>Account</span>
                    </button>
                    <button class="nav-tab" data-section="preferences">
                        <i class="fas fa-sliders-h"></i>
                        <span>Preferences</span>
                    </button>
                    <button class="nav-tab" data-section="data">
                        <i class="fas fa-database"></i>
                        <span>Data & Storage</span>
                    </button>
                </div>
            </div>

            <!-- Settings Content -->
            <div class="settings-content">
                
                <!-- Profile Settings Section -->
                <div class="settings-section active" id="profile-section">
                    <div class="section-header">
                        <h2><i class="fas fa-user"></i> Profile Settings</h2>
                        <p>Update your personal information and profile details</p>
                    </div>

                    <div class="settings-grid">
                        <!-- Profile Picture -->
                        <div class="setting-card profile-picture-card">
                            <div class="card-header">
                                <h3><i class="fas fa-camera"></i> Profile Picture</h3>
                                <p>Update your profile photo</p>
                            </div>
                            <div class="card-content">
                                <div class="profile-picture-section">
                                    <div class="current-picture">
                                        <img src="https://i.pravatar.cc/120?img=1" alt="Current Profile" id="currentProfilePic">
                                        <div class="picture-overlay">
                                            <i class="fas fa-camera"></i>
                                        </div>
                                    </div>
                                    <div class="picture-actions">
                                        <input type="file" id="profilePicInput" accept="image/*" style="display: none;">
                                        <button class="btn-outline" onclick="document.getElementById('profilePicInput').click()">
                                            <i class="fas fa-upload"></i> Upload New
                                        </button>
                                        <button class="btn-outline btn-danger">
                                            <i class="fas fa-trash"></i> Remove
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Personal Information -->
                        <div class="setting-card">
                            <div class="card-header">
                                <h3><i class="fas fa-id-card"></i> Personal Information</h3>
                                <p>Your basic profile information</p>
                            </div>
                            <div class="card-content">
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="firstName">First Name</label>
                                        <input type="text" id="firstName" value="John" class="form-input">
                                    </div>
                                    <div class="form-group">
                                        <label for="lastName">Last Name</label>
                                        <input type="text" id="lastName" value="Doe" class="form-input">
                                    </div>
                                    <div class="form-group">
                                        <label for="email">Email Address</label>
                                        <input type="email" id="email" value="<EMAIL>" class="form-input">
                                    </div>
                                    <div class="form-group">
                                        <label for="phone">Phone Number</label>
                                        <input type="tel" id="phone" value="+****************" class="form-input">
                                    </div>
                                    <div class="form-group">
                                        <label for="dateOfBirth">Date of Birth</label>
                                        <input type="date" id="dateOfBirth" value="1995-08-15" class="form-input">
                                    </div>
                                    <div class="form-group">
                                        <label for="gender">Gender</label>
                                        <select id="gender" class="form-select">
                                            <option value="male" selected>Male</option>
                                            <option value="female">Female</option>
                                            <option value="other">Other</option>
                                            <option value="prefer-not-to-say">Prefer not to say</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Athletic Information -->
                        <div class="setting-card">
                            <div class="card-header">
                                <h3><i class="fas fa-running"></i> Athletic Information</h3>
                                <p>Your sports profile and performance details</p>
                            </div>
                            <div class="card-content">
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="primarySport">Primary Sport</label>
                                        <select id="primarySport" class="form-select">
                                            <option value="football" selected>Football</option>
                                            <option value="basketball">Basketball</option>
                                            <option value="volleyball">Volleyball</option>
                                            <option value="baseball">Baseball</option>
                                            <option value="cricket">Cricket</option>
                                            <option value="hockey">Field Hockey</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="position">Position</label>
                                        <input type="text" id="position" value="Midfielder" class="form-input">
                                    </div>
                                    <div class="form-group">
                                        <label for="skillLevel">Skill Level</label>
                                        <select id="skillLevel" class="form-select">
                                            <option value="amateur">Amateur</option>
                                            <option value="semi-professional" selected>Semi-Professional</option>
                                            <option value="professional">Professional</option>
                                            <option value="elite">Elite</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="team">Current Team/Club</label>
                                        <input type="text" id="team" value="Barcelona Youth FC" class="form-input">
                                    </div>
                                    <div class="form-group">
                                        <label for="height">Height (cm)</label>
                                        <input type="number" id="height" value="178" class="form-input">
                                    </div>
                                    <div class="form-group">
                                        <label for="weight">Weight (kg)</label>
                                        <input type="number" id="weight" value="75" class="form-input">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Bio and Description -->
                        <div class="setting-card full-width">
                            <div class="card-header">
                                <h3><i class="fas fa-edit"></i> Bio & Description</h3>
                                <p>Tell others about yourself and your athletic journey</p>
                            </div>
                            <div class="card-content">
                                <div class="form-group">
                                    <label for="bio">Bio</label>
                                    <textarea id="bio" rows="4" class="form-textarea" placeholder="Write about yourself, your achievements, and goals...">Passionate footballer with 8+ years of experience. Currently playing as a midfielder for Barcelona Youth FC. Aspiring to play professionally and represent my country.</textarea>
                                </div>
                                <div class="char-counter">
                                    <span id="bioCounter">127</span> / 500 characters
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Privacy & Security Section -->
                <div class="settings-section" id="privacy-section">
                    <div class="section-header">
                        <h2><i class="fas fa-shield-alt"></i> Privacy & Security</h2>
                        <p>Control your privacy settings and account security</p>
                    </div>

                    <div class="settings-grid">
                        <!-- Profile Visibility -->
                        <div class="setting-card">
                            <div class="card-header">
                                <h3><i class="fas fa-eye"></i> Profile Visibility</h3>
                                <p>Control who can see your profile</p>
                            </div>
                            <div class="card-content">
                                <div class="toggle-group">
                                    <div class="toggle-item">
                                        <div class="toggle-info">
                                            <label>Public Profile</label>
                                            <span>Allow anyone to view your profile</span>
                                        </div>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="publicProfile" checked>
                                            <label for="publicProfile" class="toggle-label"></label>
                                        </div>
                                    </div>
                                    <div class="toggle-item">
                                        <div class="toggle-info">
                                            <label>Show Athletic Stats</label>
                                            <span>Display your performance statistics</span>
                                        </div>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="showStats" checked>
                                            <label for="showStats" class="toggle-label"></label>
                                        </div>
                                    </div>
                                    <div class="toggle-item">
                                        <div class="toggle-info">
                                            <label>Show Contact Information</label>
                                            <span>Allow others to see your contact details</span>
                                        </div>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="showContact">
                                            <label for="showContact" class="toggle-label"></label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Password & Security -->
                        <div class="setting-card">
                            <div class="card-header">
                                <h3><i class="fas fa-lock"></i> Password & Security</h3>
                                <p>Manage your account security settings</p>
                            </div>
                            <div class="card-content">
                                <div class="security-actions">
                                    <button class="btn-outline full-width">
                                        <i class="fas fa-key"></i>
                                        <div class="button-content">
                                            <span>Change Password</span>
                                            <small>Last changed 2 months ago</small>
                                        </div>
                                    </button>
                                    <button class="btn-outline full-width">
                                        <i class="fas fa-mobile-alt"></i>
                                        <div class="button-content">
                                            <span>Two-Factor Authentication</span>
                                            <small>Not enabled</small>
                                        </div>
                                    </button>
                                    <button class="btn-outline full-width">
                                        <i class="fas fa-history"></i>
                                        <div class="button-content">
                                            <span>Login Activity</span>
                                            <small>View recent logins</small>
                                        </div>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Data Privacy -->
                        <div class="setting-card">
                            <div class="card-header">
                                <h3><i class="fas fa-user-shield"></i> Data Privacy</h3>
                                <p>Control how your data is used</p>
                            </div>
                            <div class="card-content">
                                <div class="toggle-group">
                                    <div class="toggle-item">
                                        <div class="toggle-info">
                                            <label>Analytics & Performance</label>
                                            <span>Help improve our services with usage data</span>
                                        </div>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="analytics" checked>
                                            <label for="analytics" class="toggle-label"></label>
                                        </div>
                                    </div>
                                    <div class="toggle-item">
                                        <div class="toggle-info">
                                            <label>Personalized Recommendations</label>
                                            <span>Receive tailored content and suggestions</span>
                                        </div>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="recommendations" checked>
                                            <label for="recommendations" class="toggle-label"></label>
                                        </div>
                                    </div>
                                    <div class="toggle-item">
                                        <div class="toggle-info">
                                            <label>Third-party Data Sharing</label>
                                            <span>Allow sharing with partner organizations</span>
                                        </div>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="dataSharing">
                                            <label for="dataSharing" class="toggle-label"></label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notifications Section -->
                <div class="settings-section" id="notifications-section">
                    <div class="section-header">
                        <h2><i class="fas fa-bell"></i> Notification Settings</h2>
                        <p>Customize how and when you receive notifications</p>
                    </div>

                    <div class="settings-grid">
                        <!-- Email Notifications -->
                        <div class="setting-card">
                            <div class="card-header">
                                <h3><i class="fas fa-envelope"></i> Email Notifications</h3>
                                <p>Choose what emails you'd like to receive</p>
                            </div>
                            <div class="card-content">
                                <div class="toggle-group">
                                    <div class="toggle-item">
                                        <div class="toggle-info">
                                            <label>New Messages</label>
                                            <span>When someone sends you a message</span>
                                        </div>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="emailMessages" checked>
                                            <label for="emailMessages" class="toggle-label"></label>
                                        </div>
                                    </div>
                                    <div class="toggle-item">
                                        <div class="toggle-info">
                                            <label>Opportunities</label>
                                            <span>New job and training opportunities</span>
                                        </div>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="emailOpportunities" checked>
                                            <label for="emailOpportunities" class="toggle-label"></label>
                                        </div>
                                    </div>
                                    <div class="toggle-item">
                                        <div class="toggle-info">
                                            <label>Weekly Summary</label>
                                            <span>Weekly digest of platform activity</span>
                                        </div>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="emailSummary" checked>
                                            <label for="emailSummary" class="toggle-label"></label>
                                        </div>
                                    </div>
                                    <div class="toggle-item">
                                        <div class="toggle-info">
                                            <label>Marketing Updates</label>
                                            <span>Product updates and promotional content</span>
                                        </div>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="emailMarketing">
                                            <label for="emailMarketing" class="toggle-label"></label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Push Notifications -->
                        <div class="setting-card">
                            <div class="card-header">
                                <h3><i class="fas fa-mobile-alt"></i> Push Notifications</h3>
                                <p>Mobile and browser notifications</p>
                            </div>
                            <div class="card-content">
                                <div class="toggle-group">
                                    <div class="toggle-item">
                                        <div class="toggle-info">
                                            <label>Instant Messages</label>
                                            <span>Real-time message notifications</span>
                                        </div>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="pushMessages" checked>
                                            <label for="pushMessages" class="toggle-label"></label>
                                        </div>
                                    </div>
                                    <div class="toggle-item">
                                        <div class="toggle-info">
                                            <label>Scout Interests</label>
                                            <span>When a scout shows interest in your profile</span>
                                        </div>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="pushScouts" checked>
                                            <label for="pushScouts" class="toggle-label"></label>
                                        </div>
                                    </div>
                                    <div class="toggle-item">
                                        <div class="toggle-info">
                                            <label>Event Reminders</label>
                                            <span>Upcoming events and deadlines</span>
                                        </div>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="pushEvents" checked>
                                            <label for="pushEvents" class="toggle-label"></label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Notification Schedule -->
                        <div class="setting-card">
                            <div class="card-header">
                                <h3><i class="fas fa-clock"></i> Notification Schedule</h3>
                                <p>Control when you receive notifications</p>
                            </div>
                            <div class="card-content">
                                <div class="schedule-settings">
                                    <div class="form-group">
                                        <label>Quiet Hours</label>
                                        <div class="time-range">
                                            <input type="time" value="22:00" class="form-input">
                                            <span>to</span>
                                            <input type="time" value="08:00" class="form-input">
                                        </div>
                                        <small>No notifications during these hours</small>
                                    </div>
                                    <div class="form-group">
                                        <label>Time Zone</label>
                                        <select class="form-select">
                                            <option>GMT+1 (Madrid, Spain)</option>
                                            <option>GMT+0 (London, UK)</option>
                                            <option>GMT-5 (New York, USA)</option>
                                            <option>GMT-8 (Los Angeles, USA)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Account Section -->
                <div class="settings-section" id="account-section">
                    <div class="section-header">
                        <h2><i class="fas fa-user-cog"></i> Account Management</h2>
                        <p>Manage your account settings and subscription</p>
                    </div>

                    <div class="settings-grid">
                        <!-- Subscription Plan -->
                        <div class="setting-card">
                            <div class="card-header">
                                <h3><i class="fas fa-crown"></i> Subscription Plan</h3>
                                <p>Your current plan and billing information</p>
                            </div>
                            <div class="card-content">
                                <div class="subscription-info">
                                    <div class="current-plan">
                                        <div class="plan-badge pro">PRO</div>
                                        <div class="plan-details">
                                            <h4>Professional Plan</h4>
                                            <p>$29.99/month • Renews on March 15, 2024</p>
                                        </div>
                                    </div>
                                    <div class="plan-features">
                                        <div class="feature-item">
                                            <i class="fas fa-check"></i> AI Video Analysis
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-check"></i> Advanced Statistics
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-check"></i> Priority Support
                                        </div>
                                    </div>
                                    <div class="plan-actions">
                                        <button class="btn-outline">Change Plan</button>
                                        <button class="btn-outline btn-danger">Cancel Subscription</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Account Information -->
                        <div class="setting-card">
                            <div class="card-header">
                                <h3><i class="fas fa-info-circle"></i> Account Information</h3>
                                <p>Basic account details and statistics</p>
                            </div>
                            <div class="card-content">
                                <div class="account-stats">
                                    <div class="stat-item">
                                        <div class="stat-icon">
                                            <i class="fas fa-calendar-alt"></i>
                                        </div>
                                        <div class="stat-info">
                                            <label>Member Since</label>
                                            <span>January 15, 2023</span>
                                        </div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-icon">
                                            <i class="fas fa-eye"></i>
                                        </div>
                                        <div class="stat-info">
                                            <label>Profile Views</label>
                                            <span>1,247</span>
                                        </div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-icon">
                                            <i class="fas fa-users"></i>
                                        </div>
                                        <div class="stat-info">
                                            <label>Connections</label>
                                            <span>89</span>
                                        </div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-icon">
                                            <i class="fas fa-star"></i>
                                        </div>
                                        <div class="stat-info">
                                            <label>Profile Rating</label>
                                            <span>4.8/5.0</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Connected Accounts -->
                        <div class="setting-card">
                            <div class="card-header">
                                <h3><i class="fas fa-link"></i> Connected Accounts</h3>
                                <p>Link your social media and sports accounts</p>
                            </div>
                            <div class="card-content">
                                <div class="connected-accounts">
                                    <div class="account-item connected">
                                        <div class="account-icon google">
                                            <i class="fab fa-google"></i>
                                        </div>
                                        <div class="account-info">
                                            <label>Google</label>
                                            <span><EMAIL></span>
                                        </div>
                                        <button class="btn-disconnect">Disconnect</button>
                                    </div>
                                    <div class="account-item">
                                        <div class="account-icon facebook">
                                            <i class="fab fa-facebook"></i>
                                        </div>
                                        <div class="account-info">
                                            <label>Facebook</label>
                                            <span>Not connected</span>
                                        </div>
                                        <button class="btn-connect">Connect</button>
                                    </div>
                                    <div class="account-item">
                                        <div class="account-icon instagram">
                                            <i class="fab fa-instagram"></i>
                                        </div>
                                        <div class="account-info">
                                            <label>Instagram</label>
                                            <span>Not connected</span>
                                        </div>
                                        <button class="btn-connect">Connect</button>
                                    </div>
                                    <div class="account-item">
                                        <div class="account-icon linkedin">
                                            <i class="fab fa-linkedin"></i>
                                        </div>
                                        <div class="account-info">
                                            <label>LinkedIn</label>
                                            <span>Not connected</span>
                                        </div>
                                        <button class="btn-connect">Connect</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Preferences Section -->
                <div class="settings-section" id="preferences-section">
                    <div class="section-header">
                        <h2><i class="fas fa-sliders-h"></i> Preferences</h2>
                        <p>Customize your experience and interface settings</p>
                    </div>

                    <div class="settings-grid">
                        <!-- Display Settings -->
                        <div class="setting-card">
                            <div class="card-header">
                                <h3><i class="fas fa-palette"></i> Display Settings</h3>
                                <p>Customize the look and feel</p>
                            </div>
                            <div class="card-content">
                                <div class="form-group">
                                    <label>Theme</label>
                                    <div class="theme-options">
                                        <div class="theme-option active">
                                            <input type="radio" name="theme" value="light" id="themeLight" checked>
                                            <label for="themeLight">
                                                <div class="theme-preview light">
                                                    <div class="preview-header"></div>
                                                    <div class="preview-content">
                                                        <div class="preview-sidebar"></div>
                                                        <div class="preview-main"></div>
                                                    </div>
                                                </div>
                                                <span>Light</span>
                                            </label>
                                        </div>
                                        <div class="theme-option">
                                            <input type="radio" name="theme" value="dark" id="themeDark">
                                            <label for="themeDark">
                                                <div class="theme-preview dark">
                                                    <div class="preview-header"></div>
                                                    <div class="preview-content">
                                                        <div class="preview-sidebar"></div>
                                                        <div class="preview-main"></div>
                                                    </div>
                                                </div>
                                                <span>Dark</span>
                                            </label>
                                        </div>
                                        <div class="theme-option">
                                            <input type="radio" name="theme" value="auto" id="themeAuto">
                                            <label for="themeAuto">
                                                <div class="theme-preview auto">
                                                    <div class="preview-header"></div>
                                                    <div class="preview-content">
                                                        <div class="preview-sidebar"></div>
                                                        <div class="preview-main"></div>
                                                    </div>
                                                </div>
                                                <span>Auto</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="fontSize">Font Size</label>
                                    <select id="fontSize" class="form-select">
                                        <option value="small">Small</option>
                                        <option value="medium" selected>Medium</option>
                                        <option value="large">Large</option>
                                    </select>
                                </div>
                                <div class="toggle-item">
                                    <div class="toggle-info">
                                        <label>Animations</label>
                                        <span>Enable smooth animations and transitions</span>
                                    </div>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="animations" checked>
                                        <label for="animations" class="toggle-label"></label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Language & Region -->
                        <div class="setting-card">
                            <div class="card-header">
                                <h3><i class="fas fa-globe"></i> Language & Region</h3>
                                <p>Set your language and regional preferences</p>
                            </div>
                            <div class="card-content">
                                <div class="form-group">
                                    <label for="language">Language</label>
                                    <select id="language" class="form-select">
                                        <option value="en" selected>English</option>
                                        <option value="es">Español</option>
                                        <option value="fr">Français</option>
                                        <option value="de">Deutsch</option>
                                        <option value="it">Italiano</option>
                                        <option value="pt">Português</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="country">Country/Region</label>
                                    <select id="country" class="form-select">
                                        <option value="es" selected>Spain</option>
                                        <option value="us">United States</option>
                                        <option value="uk">United Kingdom</option>
                                        <option value="fr">France</option>
                                        <option value="de">Germany</option>
                                        <option value="it">Italy</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="currency">Currency</label>
                                    <select id="currency" class="form-select">
                                        <option value="eur" selected>Euro (€)</option>
                                        <option value="usd">US Dollar ($)</option>
                                        <option value="gbp">British Pound (£)</option>
                                        <option value="jpy">Japanese Yen (¥)</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Content Preferences -->
                        <div class="setting-card">
                            <div class="card-header">
                                <h3><i class="fas fa-filter"></i> Content Preferences</h3>
                                <p>Customize what content you see</p>
                            </div>
                            <div class="card-content">
                                <div class="toggle-group">
                                    <div class="toggle-item">
                                        <div class="toggle-info">
                                            <label>Show Beginner Content</label>
                                            <span>Display content suitable for beginners</span>
                                        </div>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="beginnerContent" checked>
                                            <label for="beginnerContent" class="toggle-label"></label>
                                        </div>
                                    </div>
                                    <div class="toggle-item">
                                        <div class="toggle-info">
                                            <label>Professional Opportunities</label>
                                            <span>Show professional-level opportunities</span>
                                        </div>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="proOpportunities" checked>
                                            <label for="proOpportunities" class="toggle-label"></label>
                                        </div>
                                    </div>
                                    <div class="toggle-item">
                                        <div class="toggle-info">
                                            <label>International Content</label>
                                            <span>Include international sports content</span>
                                        </div>
                                        <div class="toggle-switch">
                                            <input type="checkbox" id="internationalContent" checked>
                                            <label for="internationalContent" class="toggle-label"></label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Data & Storage Section -->
                <div class="settings-section" id="data-section">
                    <div class="section-header">
                        <h2><i class="fas fa-database"></i> Data & Storage</h2>
                        <p>Manage your data, downloads, and storage usage</p>
                    </div>

                    <div class="settings-grid">
                        <!-- Storage Usage -->
                        <div class="setting-card">
                            <div class="card-header">
                                <h3><i class="fas fa-hdd"></i> Storage Usage</h3>
                                <p>See how much storage you're using</p>
                            </div>
                            <div class="card-content">
                                <div class="storage-overview">
                                    <div class="storage-stat">
                                        <div class="storage-circle">
                                            <svg viewBox="0 0 36 36" class="circular-chart">
                                                <path class="circle-bg" d="M18 2.0845
                                                    a 15.9155 15.9155 0 0 1 0 31.831
                                                    a 15.9155 15.9155 0 0 1 0 -31.831" />
                                                <path class="circle" stroke-dasharray="65, 100" d="M18 2.0845
                                                    a 15.9155 15.9155 0 0 1 0 31.831
                                                    a 15.9155 15.9155 0 0 1 0 -31.831" />
                                                <text x="18" y="20.35" class="percentage">65%</text>
                                            </svg>
                                        </div>
                                        <div class="storage-info">
                                            <h4>6.5 GB used</h4>
                                            <p>of 10 GB total</p>
                                        </div>
                                    </div>
                                    <div class="storage-breakdown">
                                        <div class="breakdown-item">
                                            <div class="breakdown-color videos"></div>
                                            <span>Videos: 4.2 GB</span>
                                        </div>
                                        <div class="breakdown-item">
                                            <div class="breakdown-color images"></div>
                                            <span>Images: 1.8 GB</span>
                                        </div>
                                        <div class="breakdown-item">
                                            <div class="breakdown-color documents"></div>
                                            <span>Documents: 0.5 GB</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Data Export -->
                        <div class="setting-card">
                            <div class="card-header">
                                <h3><i class="fas fa-download"></i> Data Export</h3>
                                <p>Download your data and information</p>
                            </div>
                            <div class="card-content">
                                <div class="export-options">
                                    <div class="export-item">
                                        <div class="export-icon">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div class="export-info">
                                            <h4>Profile Data</h4>
                                            <p>Your profile information, stats, and achievements</p>
                                        </div>
                                        <button class="btn-outline">Export</button>
                                    </div>
                                    <div class="export-item">
                                        <div class="export-icon">
                                            <i class="fas fa-video"></i>
                                        </div>
                                        <div class="export-info">
                                            <h4>Media Files</h4>
                                            <p>Videos, images, and analysis reports</p>
                                        </div>
                                        <button class="btn-outline">Export</button>
                                    </div>
                                    <div class="export-item">
                                        <div class="export-icon">
                                            <i class="fas fa-envelope"></i>
                                        </div>
                                        <div class="export-info">
                                            <h4>Messages</h4>
                                            <p>All your conversations and message history</p>
                                        </div>
                                        <button class="btn-outline">Export</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Account Actions -->
                        <div class="setting-card danger-zone">
                            <div class="card-header">
                                <h3><i class="fas fa-exclamation-triangle"></i> Danger Zone</h3>
                                <p>Irreversible actions - proceed with caution</p>
                            </div>
                            <div class="card-content">
                                <div class="danger-actions">
                                    <div class="danger-item">
                                        <div class="danger-info">
                                            <h4>Clear All Data</h4>
                                            <p>Remove all your uploaded files and analysis data</p>
                                        </div>
                                        <button class="btn-danger">Clear Data</button>
                                    </div>
                                    <div class="danger-item">
                                        <div class="danger-info">
                                            <h4>Deactivate Account</h4>
                                            <p>Temporarily disable your account (reversible)</p>
                                        </div>
                                        <button class="btn-danger">Deactivate</button>
                                    </div>
                                    <div class="danger-item">
                                        <div class="danger-info">
                                            <h4>Delete Account</h4>
                                            <p>Permanently delete your account and all data</p>
                                        </div>
                                        <button class="btn-danger">Delete Account</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <!-- Floating Action Buttons -->
    <div class="floating-buttons">
        <button class="fab help-btn" title="Help & Support">
            <i class="fas fa-question"></i>
        </button>
        <button class="fab top-btn" title="Back to Top">
            <i class="fas fa-arrow-up"></i>
        </button>
    </div>

    <!-- Tooltips -->
    <div id="feature-tooltip" class="js-tooltip"></div>
    <div id="profile-tooltip" class="js-tooltip"></div>

    <!-- Settings Script -->
    <script src="settings.js"></script>
</body>
</html>