﻿﻿/* Messages Page Styles */

/* Main content */
.main-content {
    flex: 1;
    padding: 0;
    overflow-y: auto;
}

/* Messages page */
.messages-page {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: #f8f9fa;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Page header */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: linear-gradient(135deg, #6a0dad 0%, #8b5cf6 100%);
    color: white;
}

.header-left h1 {
    margin: 0;
    font-size: 22px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.page-description {
    margin: 3px 0 0;
    font-size: 13px;
    opacity: 0.8;
}

.header-actions {
    display: flex;
    gap: 10px;
}

.btn-primary, .btn-secondary {
    padding: 8px 16px;
    border-radius: 8px;
    border: none;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
}

.btn-secondary {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}

.btn-primary:hover, .btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Messages content */
.messages-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* Conversations section */
.conversations-section {
    width: 300px;
    border-right: 1px solid #e5e7eb;
    display: flex;
    flex-direction: column;
    background-color: white;
}

.section-header {
    padding: 12px;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.section-header h3 {
    margin: 0;
    font-size: 15px;
    color: #374151;
}

.search-container {
    position: relative;
}

.search-container i {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
}

.search-container input {
    width: 100%;
    padding: 8px 10px 8px 35px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    background-color: #f9fafb;
}

.search-container input:focus {
    outline: none;
    border-color: #6a0dad;
    box-shadow: 0 0 0 2px rgba(106, 13, 173, 0.1);
}

/* Filter tabs */
.filter-tabs {
    display: flex;
    padding: 0 12px;
    border-bottom: 1px solid #e5e7eb;
    background-color: #f9fafb;
}

.tab-btn {
    padding: 8px 12px;
    background: none;
    border: none;
    font-size: 13px;
    color: #6b7280;
    cursor: pointer;
    position: relative;
    transition: color 0.2s ease;
}

.tab-btn.active {
    color: #6a0dad;
    font-weight: 500;
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 15%;
    width: 70%;
    height: 2px;
    background-color: #6a0dad;
}

.tab-btn:hover {
    color: #6a0dad;
}

/* Conversations list */
.conversations-list {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.conversation-card {
    display: flex;
    padding: 10px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    background-color: #f9fafb;
    border: 1px solid transparent;
}

.conversation-card:hover {
    background-color: #f3f4f6;
    border-color: #e5e7eb;
}

.conversation-card.active {
    background-color: #ede9fe;
    border-color: #c4b5fd;
}

.conversation-avatar {
    position: relative;
    margin-right: 12px;
}

.conversation-avatar img {
    width: 42px;
    height: 42px;
    border-radius: 50%;
    object-fit: cover;
}

.status-dot {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
}

.status-dot.online {
    background-color: #10b981;
}

.status-dot.offline {
    background-color: #9ca3af;
}

.conversation-details {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.conversation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.conversation-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: #111827;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.time {
    font-size: 12px;
    color: #6b7280;
}

.conversation-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.role {
    font-size: 13px;
    color: #6a0dad;
}

.unread-count {
    background-color: #6a0dad;
    color: white;
    font-size: 12px;
    font-weight: 500;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 20px;
    text-align: center;
}

.last-message {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Chat section */
.chat-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: white;
}

.chat-header {
    padding: 12px 16px;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f9fafb;
}

.contact-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.contact-avatar {
    position: relative;
}

.contact-avatar img {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    object-fit: cover;
}

.contact-details {
    display: flex;
    flex-direction: column;
}

.contact-details h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: #111827;
}

.contact-role {
    font-size: 13px;
    color: #6a0dad;
    margin: 2px 0;
}

.contact-status {
    font-size: 12px;
    color: #10b981;
}

.chat-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border: 1px solid #e5e7eb;
    background-color: white;
    color: #6b7280;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background-color: #f3f4f6;
    color: #6a0dad;
    border-color: #6a0dad;
}

/* Chat messages */
.chat-messages {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    background-color: #f9fafb;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.message-group {
    display: flex;
    gap: 10px;
    max-width: 70%;
}

.message-group.sent {
    align-self: flex-end;
    flex-direction: row-reverse;
}

.message-group.received {
    align-self: flex-start;
}

.message-avatar {
    align-self: flex-end;
}

.message-avatar img {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    object-fit: cover;
}

.message-content {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.message-bubble {
    padding: 12px 16px;
    border-radius: 18px;
    position: relative;
}

.message-group.sent .message-bubble {
    background: linear-gradient(135deg, #6a0dad 0%, #8b5cf6 100%);
    color: white;
    border-bottom-right-radius: 4px;
}

.message-group.received .message-bubble {
    background-color: white;
    color: #111827;
    border: 1px solid #e5e7eb;
    border-bottom-left-radius: 4px;
}

.message-bubble p {
    margin: 0;
    font-size: 14px;
    line-height: 1.5;
}

.message-time {
    font-size: 12px;
    color: #9ca3af;
    align-self: flex-end;
}

.message-group.sent .message-time {
    text-align: right;
}

/* Typing indicator */
.typing-indicator {
    display: flex;
    align-items: flex-end;
    gap: 10px;
    max-width: 70%;
    align-self: flex-start;
}

.typing-avatar {
    align-self: flex-end;
}

.typing-avatar img {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    object-fit: cover;
}

.typing-animation {
    background-color: white;
    padding: 12px 16px;
    border-radius: 18px;
    border-bottom-left-radius: 4px;
    border: 1px solid #e5e7eb;
    display: flex;
    gap: 4px;
}

.typing-animation span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #9ca3af;
    display: inline-block;
    animation: typing 1.4s infinite ease-in-out both;
}

.typing-animation span:nth-child(1) {
    animation-delay: -0.32s;
}

.typing-animation span:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes typing {
    0%, 80%, 100% { 
        transform: scale(0.6);
    }
    40% { 
        transform: scale(1);
    }
}

/* Message input */
.message-input {
    padding: 12px 16px;
    border-top: 1px solid #e5e7eb;
    background-color: white;
}

.input-container {
    display: flex;
    align-items: center;
    gap: 10px;
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 24px;
    padding: 5px 15px;
}

.input-btn {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.input-btn:hover {
    color: #6a0dad;
    background-color: #ede9fe;
}

.text-input {
    flex: 1;
}

.text-input textarea {
    width: 100%;
    border: none;
    background: none;
    resize: none;
    padding: 10px 0;
    font-size: 14px;
    max-height: 100px;
    outline: none;
    font-family: inherit;
}

.send-btn {
    background-color: #6a0dad;
    color: white;
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.send-btn:hover {
    background-color: #5b21b6;
    transform: scale(1.05);
}

/* Responsive design */
@media (max-width: 1024px) {
    .conversations-section {
        width: 260px;
    }
}

@media (max-width: 768px) {
    .messages-content {
        flex-direction: column;
    }
    
    .conversations-section {
        width: 100%;
        height: 250px;
        border-right: none;
        border-bottom: 1px solid #e5e7eb;
    }
    
    .message-group {
        max-width: 85%;
    }
}

@media (max-width: 480px) {
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .header-actions {
        width: 100%;
        justify-content: space-between;
    }
    
    .message-group {
        max-width: 90%;
    }
}