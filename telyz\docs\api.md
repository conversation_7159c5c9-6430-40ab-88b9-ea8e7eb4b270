# Telyz API Documentation

This document provides information about the Telyz API endpoints, request/response formats, and authentication.

## Base URL

```
https://api.telyz.com/v1
```

## Authentication

The Telyz API uses JWT (JSON Web Token) for authentication. To access protected endpoints, you need to include the J<PERSON><PERSON> token in the Authorization header of your requests.

```
Authorization: Bearer <token>
```

### Authentication Endpoints

#### Login

```
POST /auth/login
```

Request body:

```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

Response:

```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "60d21b4667d0d8992e610c85",
    "firstName": "<PERSON>",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "role": "athlete",
    "sport": "football",
    "isVerified": true,
    "isPremium": false
  }
}
```

#### Refresh Token

```
POST /auth/refresh
```

Request body:

```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

Response:

```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

#### Logout

```
POST /auth/logout
```

Response:

```json
{
  "message": "Logout successful"
}
```

#### Get Current User

```
GET /auth/me
```

Response:

```json
{
  "id": "60d21b4667d0d8992e610c85",
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "role": "athlete",
  "sport": "football",
  "isVerified": true,
  "isPremium": false
}
```

## User Endpoints

### Get User

```
GET /users/:id
```

Response:

```json
{
  "id": "60d21b4667d0d8992e610c85",
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "role": "athlete",
  "sport": "football",
  "position": "midfielder",
  "profilePicture": "https://example.com/profile.jpg",
  "isVerified": true,
  "isPremium": false,
  "isActive": true,
  "createdAt": "2023-01-01T00:00:00.000Z",
  "updatedAt": "2023-01-02T00:00:00.000Z"
}
```

### Create User

```
POST /users
```

Request body:

```json
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "password": "password123",
  "role": "athlete",
  "sport": "football",
  "position": "midfielder"
}
```

Response:

```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "60d21b4667d0d8992e610c85",
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "role": "athlete",
    "sport": "football"
  }
}
```

### Update User

```
PUT /users/:id
```

Request body:

```json
{
  "firstName": "John",
  "lastName": "Smith",
  "position": "forward"
}
```

Response:

```json
{
  "id": "60d21b4667d0d8992e610c85",
  "firstName": "John",
  "lastName": "Smith",
  "email": "<EMAIL>",
  "role": "athlete",
  "sport": "football",
  "position": "forward",
  "isVerified": true,
  "isPremium": false,
  "isActive": true,
  "updatedAt": "2023-01-03T00:00:00.000Z"
}
```

### Delete User

```
DELETE /users/:id
```

Response:

```json
{
  "message": "User deleted successfully"
}
```

### Get User Profile

```
GET /users/:id/profile
```

Response:

```json
{
  "user": {
    "id": "60d21b4667d0d8992e610c85",
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "role": "athlete",
    "sport": "football",
    "position": "midfielder"
  },
  "profile": {
    "id": "60d21b4667d0d8992e610c86",
    "user": "60d21b4667d0d8992e610c85",
    "dateOfBirth": "1995-01-01T00:00:00.000Z",
    "nationality": "Spain",
    "height": 180,
    "weight": 75,
    "preferredFoot": "right",
    "jerseyNumber": 10,
    "specialties": ["passing", "dribbling"],
    "attributes": {
      "speed": 85,
      "strength": 70,
      "intelligence": 90,
      "accuracy": 85
    },
    "stats": {
      "totalMatches": 50,
      "totalGoals": 15,
      "totalAssists": 20,
      "averageRating": 8.5
    }
  }
}
```

### Update User Profile

```
PUT /users/:id/profile
```

Request body:

```json
{
  "dateOfBirth": "1995-01-01T00:00:00.000Z",
  "nationality": "Spain",
  "height": 180,
  "weight": 75,
  "preferredFoot": "right",
  "jerseyNumber": 10,
  "specialties": ["passing", "dribbling"],
  "attributes": {
    "speed": 85,
    "strength": 70,
    "intelligence": 90,
    "accuracy": 85
  }
}
```

Response:

```json
{
  "id": "60d21b4667d0d8992e610c86",
  "user": "60d21b4667d0d8992e610c85",
  "dateOfBirth": "1995-01-01T00:00:00.000Z",
  "nationality": "Spain",
  "height": 180,
  "weight": 75,
  "preferredFoot": "right",
  "jerseyNumber": 10,
  "specialties": ["passing", "dribbling"],
  "attributes": {
    "speed": 85,
    "strength": 70,
    "intelligence": 90,
    "accuracy": 85
  },
  "updatedAt": "2023-01-03T00:00:00.000Z"
}
```

## Opportunity Endpoints

### Get All Opportunities

```
GET /opportunities
```

Query parameters:

- `page`: Page number (default: 1)
- `limit`: Number of items per page (default: 10)
- `sport`: Filter by sport
- `type`: Filter by opportunity type
- `location`: Filter by location
- `search`: Search term

Response:

```json
{
  "opportunities": [
    {
      "id": "60d21b4667d0d8992e610c87",
      "title": "Football Tryout",
      "description": "Tryout for FC Barcelona youth team",
      "type": "tryout",
      "sport": "football",
      "positions": ["midfielder", "forward"],
      "organization": {
        "name": "FC Barcelona",
        "logo": "https://example.com/fcb.jpg",
        "verified": true
      },
      "location": {
        "city": "Barcelona",
        "country": "Spain"
      },
      "dates": {
        "published": "2023-01-01T00:00:00.000Z",
        "applicationDeadline": "2023-02-01T00:00:00.000Z",
        "startDate": "2023-03-01T00:00:00.000Z"
      },
      "status": "published",
      "stats": {
        "applications": 50
      }
    }
  ],
  "totalPages": 5,
  "currentPage": 1,
  "totalOpportunities": 45
}
```

### Get Opportunity by ID

```
GET /opportunities/:id
```

Response:

```json
{
  "id": "60d21b4667d0d8992e610c87",
  "title": "Football Tryout",
  "description": "Tryout for FC Barcelona youth team",
  "type": "tryout",
  "sport": "football",
  "positions": ["midfielder", "forward"],
  "organization": {
    "name": "FC Barcelona",
    "logo": "https://example.com/fcb.jpg",
    "verified": true
  },
  "location": {
    "city": "Barcelona",
    "country": "Spain",
    "address": "Camp Nou, C. d'Arístides Maillol, 12, 08028 Barcelona, Spain",
    "coordinates": {
      "latitude": 41.380898,
      "longitude": 2.122799
    }
  },
  "dates": {
    "published": "2023-01-01T00:00:00.000Z",
    "applicationDeadline": "2023-02-01T00:00:00.000Z",
    "startDate": "2023-03-01T00:00:00.000Z",
    "endDate": "2023-03-02T00:00:00.000Z"
  },
  "requirements": {
    "ageRange": {
      "min": 16,
      "max": 18
    },
    "experience": "intermediate",
    "skills": ["passing", "dribbling", "shooting"],
    "additionalRequirements": ["Must be available for both days"]
  },
  "compensation": {
    "type": "unpaid",
    "benefits": ["Potential to join academy"]
  },
  "application": {
    "process": "direct",
    "instructions": "Bring your own equipment",
    "requiredDocuments": ["ID", "Medical clearance"]
  },
  "status": "published",
  "visibility": "public",
  "featured": true,
  "stats": {
    "views": 500,
    "applications": 50,
    "shares": 20,
    "saves": 30
  },
  "hasApplied": false,
  "hasSaved": false,
  "creator": {
    "id": "60d21b4667d0d8992e610c88",
    "name": "FC Barcelona Scout",
    "role": "scout"
  },
  "createdAt": "2023-01-01T00:00:00.000Z",
  "updatedAt": "2023-01-02T00:00:00.000Z"
}
```

## AI Analysis Endpoints

### Upload Video for Analysis

```
POST /ai-analysis/upload
```

Request body (multipart/form-data):

- `video`: Video file
- `title`: Analysis title
- `description`: Analysis description
- `sport`: Sport type
- `position`: Player position

Response:

```json
{
  "id": "60d21b4667d0d8992e610c89",
  "title": "Match Analysis",
  "description": "Analysis of my performance in the match against Team B",
  "sport": "football",
  "position": "midfielder",
  "status": "pending",
  "progress": 0,
  "estimatedTime": 300,
  "createdAt": "2023-01-01T00:00:00.000Z"
}
```

### Get Analysis Status

```
GET /ai-analysis/:id/status
```

Response:

```json
{
  "id": "60d21b4667d0d8992e610c89",
  "status": "processing",
  "progress": 50,
  "estimatedTimeRemaining": 150
}
```

### Get Analysis Report

```
GET /ai-analysis/:id/report
```

Response:

```json
{
  "id": "60d21b4667d0d8992e610c89",
  "title": "Match Analysis",
  "description": "Analysis of my performance in the match against Team B",
  "sport": "football",
  "position": "midfielder",
  "results": {
    "metrics": {
      "passAccuracy": 87,
      "distance": 12.4,
      "keyActions": 8
    },
    "technicalAnalysis": [
      {
        "category": "Passing",
        "score": 87,
        "strengths": ["Short passing accuracy", "Through ball vision"],
        "weaknesses": ["Long range passing"],
        "recommendations": ["Practice long-range passing drills"]
      }
    ],
    "positionalAnalysis": {
      "heatmap": "https://example.com/heatmap.jpg",
      "positioning": 85,
      "movement": 78,
      "spatialAwareness": 82
    },
    "keyMoments": [
      {
        "timestamp": 120,
        "title": "Great through ball",
        "description": "Excellent vision to spot the run",
        "type": "highlight",
        "thumbnailUrl": "https://example.com/moment1.jpg"
      }
    ],
    "overallAssessment": {
      "rating": 7.8,
      "summary": "Good overall performance with excellent passing skills and spatial awareness.",
      "potential": 85,
      "recommendations": [
        "Focus on improving long-range passing accuracy",
        "Work on defensive positioning during transitions"
      ]
    }
  },
  "report": {
    "pdfUrl": "https://example.com/report.pdf",
    "jsonUrl": "https://example.com/report.json"
  },
  "status": "completed",
  "completedAt": "2023-01-01T00:30:00.000Z"
}
```

## Error Responses

All endpoints return appropriate HTTP status codes:

- `200 OK`: Request successful
- `201 Created`: Resource created successfully
- `400 Bad Request`: Invalid request parameters
- `401 Unauthorized`: Authentication required or failed
- `403 Forbidden`: Permission denied
- `404 Not Found`: Resource not found
- `500 Internal Server Error`: Server error

Error response format:

```json
{
  "message": "Error message",
  "errors": [
    {
      "param": "email",
      "msg": "Invalid email format"
    }
  ]
}
```
