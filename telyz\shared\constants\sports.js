/**
 * Sports Constants
 * 
 * Defines constants related to sports in the Telyz platform.
 */

/**
 * Sport types supported by the platform
 */
const SPORT_TYPES = {
  FOOTBALL: 'football',
  BASKETBALL: 'basketball',
  VOLLEYBALL: 'volleyball',
  BASEBALL: 'baseball',
  CRICKET: 'cricket',
  HOCKEY: 'hockey',
};

/**
 * Football (Soccer) positions
 */
const FOOTBALL_POSITIONS = {
  GOALKEEPER: 'goalkeeper',
  DEFENDER: {
    CENTER_BACK: 'center_back',
    LEFT_BACK: 'left_back',
    RIGHT_BACK: 'right_back',
    SWEEPER: 'sweeper',
    WING_BACK: 'wing_back',
  },
  MIDFIELDER: {
    DEFENSIVE_MIDFIELDER: 'defensive_midfielder',
    CENTRAL_MIDFIELDER: 'central_midfielder',
    ATTACKING_MIDFIELDER: 'attacking_midfielder',
    LEFT_MIDFIELDER: 'left_midfielder',
    RIGHT_MIDFIELDER: 'right_midfielder',
  },
  FORWARD: {
    CENTER_FORWARD: 'center_forward',
    SECOND_STRIKER: 'second_striker',
    WING<PERSON>: 'winger',
  },
};

/**
 * Basketball positions
 */
const BASKETBALL_POSITIONS = {
  POINT_GUARD: 'point_guard',
  SHOOTING_GUARD: 'shooting_guard',
  SMALL_FORWARD: 'small_forward',
  POWER_FORWARD: 'power_forward',
  CENTER: 'center',
};

/**
 * Volleyball positions
 */
const VOLLEYBALL_POSITIONS = {
  SETTER: 'setter',
  OUTSIDE_HITTER: 'outside_hitter',
  OPPOSITE_HITTER: 'opposite_hitter',
  MIDDLE_BLOCKER: 'middle_blocker',
  LIBERO: 'libero',
};

/**
 * Baseball positions
 */
const BASEBALL_POSITIONS = {
  PITCHER: 'pitcher',
  CATCHER: 'catcher',
  FIRST_BASE: 'first_base',
  SECOND_BASE: 'second_base',
  THIRD_BASE: 'third_base',
  SHORTSTOP: 'shortstop',
  LEFT_FIELD: 'left_field',
  CENTER_FIELD: 'center_field',
  RIGHT_FIELD: 'right_field',
  DESIGNATED_HITTER: 'designated_hitter',
};

/**
 * Cricket positions
 */
const CRICKET_POSITIONS = {
  BATSMAN: 'batsman',
  BOWLER: {
    FAST_BOWLER: 'fast_bowler',
    MEDIUM_PACE_BOWLER: 'medium_pace_bowler',
    SPIN_BOWLER: 'spin_bowler',
  },
  ALL_ROUNDER: 'all_rounder',
  WICKET_KEEPER: 'wicket_keeper',
  FIELDER: {
    SLIP: 'slip',
    GULLY: 'gully',
    POINT: 'point',
    COVER: 'cover',
    MID_OFF: 'mid_off',
    MID_ON: 'mid_on',
    MIDWICKET: 'midwicket',
    SQUARE_LEG: 'square_leg',
    FINE_LEG: 'fine_leg',
    THIRD_MAN: 'third_man',
    LONG_OFF: 'long_off',
    LONG_ON: 'long_on',
  },
};

/**
 * Field Hockey positions
 */
const HOCKEY_POSITIONS = {
  GOALKEEPER: 'goalkeeper',
  DEFENDER: {
    SWEEPER: 'sweeper',
    FULL_BACK: 'full_back',
    HALF_BACK: 'half_back',
  },
  MIDFIELDER: {
    CENTER_MIDFIELDER: 'center_midfielder',
    RIGHT_MIDFIELDER: 'right_midfielder',
    LEFT_MIDFIELDER: 'left_midfielder',
  },
  FORWARD: {
    CENTER_FORWARD: 'center_forward',
    RIGHT_FORWARD: 'right_forward',
    LEFT_FORWARD: 'left_forward',
  },
};

/**
 * Map of sport types to their positions
 */
const SPORT_POSITIONS = {
  [SPORT_TYPES.FOOTBALL]: FOOTBALL_POSITIONS,
  [SPORT_TYPES.BASKETBALL]: BASKETBALL_POSITIONS,
  [SPORT_TYPES.VOLLEYBALL]: VOLLEYBALL_POSITIONS,
  [SPORT_TYPES.BASEBALL]: BASEBALL_POSITIONS,
  [SPORT_TYPES.CRICKET]: CRICKET_POSITIONS,
  [SPORT_TYPES.HOCKEY]: HOCKEY_POSITIONS,
};

/**
 * Sport-specific metrics for analysis
 */
const SPORT_METRICS = {
  [SPORT_TYPES.FOOTBALL]: {
    PASS_ACCURACY: 'pass_accuracy',
    DISTANCE_COVERED: 'distance_covered',
    SPRINT_SPEED: 'sprint_speed',
    SHOTS_ON_TARGET: 'shots_on_target',
    TACKLES_WON: 'tackles_won',
    AERIAL_DUELS_WON: 'aerial_duels_won',
    DRIBBLES_COMPLETED: 'dribbles_completed',
    INTERCEPTIONS: 'interceptions',
    GOALS: 'goals',
    ASSISTS: 'assists',
  },
  [SPORT_TYPES.BASKETBALL]: {
    POINTS: 'points',
    REBOUNDS: 'rebounds',
    ASSISTS: 'assists',
    STEALS: 'steals',
    BLOCKS: 'blocks',
    FIELD_GOAL_PERCENTAGE: 'field_goal_percentage',
    THREE_POINT_PERCENTAGE: 'three_point_percentage',
    FREE_THROW_PERCENTAGE: 'free_throw_percentage',
    TURNOVERS: 'turnovers',
    PLUS_MINUS: 'plus_minus',
  },
  // Add metrics for other sports as needed
};

module.exports = {
  SPORT_TYPES,
  FOOTBALL_POSITIONS,
  BASKETBALL_POSITIONS,
  VOLLEYBALL_POSITIONS,
  BASEBALL_POSITIONS,
  CRICKET_POSITIONS,
  HOCKEY_POSITIONS,
  SPORT_POSITIONS,
  SPORT_METRICS,
};
