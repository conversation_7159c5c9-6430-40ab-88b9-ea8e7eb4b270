/* Button Component Styles */

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius-md);
  font-family: var(--font-family);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-normal);
  border: none;
  outline: none;
  text-decoration: none;
  white-space: nowrap;
  text-align: center;
}

/* <PERSON><PERSON> Variants */

.btn-primary {
  background-color: var(--primary-color);
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.btn-primary:active {
  background-color: var(--primary-dark);
  box-shadow: var(--shadow-sm);
  transform: translateY(0);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
  background-color: #27ae60;
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.btn-secondary:active {
  background-color: #27ae60;
  box-shadow: var(--shadow-sm);
  transform: translateY(0);
}

.btn-outline {
  background-color: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.btn-outline:hover {
  background-color: rgba(106, 13, 173, 0.05);
  transform: translateY(-2px);
}

.btn-outline:active {
  background-color: rgba(106, 13, 173, 0.1);
  transform: translateY(0);
}

.btn-text {
  background-color: transparent;
  color: var(--primary-color);
  box-shadow: none;
}

.btn-text:hover {
  background-color: rgba(106, 13, 173, 0.05);
  transform: translateY(-2px);
}

.btn-text:active {
  background-color: rgba(106, 13, 173, 0.1);
  transform: translateY(0);
}

/* Button Sizes */

.btn-small {
  padding: 6px 12px;
  font-size: var(--font-size-xs);
  height: 32px;
}

.btn-medium {
  padding: 8px 16px;
  font-size: var(--font-size-sm);
  height: 40px;
}

.btn-large {
  padding: 12px 24px;
  font-size: var(--font-size-md);
  height: 48px;
}

/* Disabled State */

.btn-disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* Icon Styles */

.btn i {
  font-size: 1.2em;
  display: inline-block;
  vertical-align: middle;
}

/* Button with icon only */
.btn.icon-only {
  padding: 0;
  width: 40px;
  justify-content: center;
}

.btn-small.icon-only {
  width: 32px;
}

.btn-large.icon-only {
  width: 48px;
}

/* Button Group */
.btn-group {
  display: inline-flex;
}

.btn-group .btn {
  border-radius: 0;
}

.btn-group .btn:first-child {
  border-top-left-radius: var(--border-radius-md);
  border-bottom-left-radius: var(--border-radius-md);
}

.btn-group .btn:last-child {
  border-top-right-radius: var(--border-radius-md);
  border-bottom-right-radius: var(--border-radius-md);
}

/* Button with loading state */
.btn-loading {
  position: relative;
  color: transparent !important;
}

.btn-loading::after {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  top: 50%;
  left: 50%;
  margin-top: -8px;
  margin-left: -8px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: white;
  animation: btn-spinner 0.8s linear infinite;
}

@keyframes btn-spinner {
  to {
    transform: rotate(360deg);
  }
}
