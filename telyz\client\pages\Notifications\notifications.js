// ================ NOTIFICATIONS FUNCTIONALITY ================

document.addEventListener('DOMContentLoaded', function() {
    
    // ================ VARIABLES ================
    const filterTabs = document.querySelectorAll('.filter-tab');
    const statusFilter = document.getElementById('statusFilter');
    const priorityFilter = document.getElementById('priorityFilter');
    const notificationsList = document.getElementById('notificationsList');
    const emptyState = document.getElementById('emptyState');
    const unreadCountElement = document.getElementById('unreadCount');
    const markAllReadBtn = document.getElementById('markAllReadBtn');
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    
    let currentFilter = 'all';
    let currentStatus = 'all';
    let currentPriority = 'all';

    // ================ FILTER FUNCTIONALITY ================
    
    // Tab Filters
    filterTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Remove active class from all tabs
            filterTabs.forEach(t => t.classList.remove('active'));
            // Add active class to clicked tab
            this.classList.add('active');
            
            currentFilter = this.getAttribute('data-filter');
            filterNotifications();
        });
    });

    // Status Filter
    statusFilter.addEventListener('change', function() {
        currentStatus = this.value;
        filterNotifications();
    });

    // Priority Filter
    priorityFilter.addEventListener('change', function() {
        currentPriority = this.value;
        filterNotifications();
    });

    // ================ NOTIFICATION ACTIONS ================
    
    // Mark All Read
    markAllReadBtn.addEventListener('click', function() {
        const unreadNotifications = document.querySelectorAll('.notification-item.unread');
        
        unreadNotifications.forEach(notification => {
            markAsRead(notification);
        });
        
        updateUnreadCount();
        
        // Show success message
        showToast('All notifications marked as read!', 'success');
    });

    // Individual notification controls
    document.addEventListener('click', function(e) {
        const notification = e.target.closest('.notification-item');
        
        if (e.target.closest('.mark-read')) {
            markAsRead(notification);
            updateUnreadCount();
            showToast('Notification marked as read', 'success');
        }
        
        if (e.target.closest('.mark-unread')) {
            markAsUnread(notification);
            updateUnreadCount();
            showToast('Notification marked as unread', 'info');
        }
        
        if (e.target.closest('.delete')) {
            deleteNotification(notification);
            updateUnreadCount();
            showToast('Notification deleted', 'error');
        }
        
        // Action buttons
        if (e.target.closest('.action-btn')) {
            const actionText = e.target.textContent.trim();
            handleActionClick(actionText, notification);
        }
    });

    // ================ UTILITY FUNCTIONS ================
    
    function filterNotifications() {
        const notifications = document.querySelectorAll('.notification-item');
        let visibleCount = 0;
        
        notifications.forEach(notification => {
            const type = notification.getAttribute('data-type');
            const status = notification.getAttribute('data-status');
            const priority = notification.getAttribute('data-priority');
            
            let showNotification = true;
            
            // Filter by type
            if (currentFilter !== 'all' && type !== currentFilter) {
                showNotification = false;
            }
            
            // Filter by status
            if (currentStatus !== 'all' && status !== currentStatus) {
                showNotification = false;
            }
            
            // Filter by priority
            if (currentPriority !== 'all' && priority !== currentPriority) {
                showNotification = false;
            }
            
            if (showNotification) {
                notification.style.display = 'flex';
                visibleCount++;
            } else {
                notification.style.display = 'none';
            }
        });
        
        // Show/hide empty state
        if (visibleCount === 0) {
            emptyState.style.display = 'block';
        } else {
            emptyState.style.display = 'none';
        }
    }
    
    function markAsRead(notification) {
        notification.classList.remove('unread');
        notification.classList.add('read');
        notification.setAttribute('data-status', 'read');
        
        // Change control button
        const markReadBtn = notification.querySelector('.mark-read');
        const markUnreadBtn = notification.querySelector('.mark-unread');
        
        if (markReadBtn) {
            markReadBtn.classList.remove('mark-read');
            markReadBtn.classList.add('mark-unread');
            markReadBtn.innerHTML = '<i class="fas fa-envelope"></i>';
            markReadBtn.setAttribute('data-tooltip', 'Mark as unread');
        }
        
        // Remove unread dot
        const unreadDot = notification.querySelector('.unread-dot');
        if (unreadDot) {
            unreadDot.remove();
        }
    }
    
    function markAsUnread(notification) {
        notification.classList.remove('read');
        notification.classList.add('unread');
        notification.setAttribute('data-status', 'unread');
        
        // Change control button
        const markUnreadBtn = notification.querySelector('.mark-unread');
        
        if (markUnreadBtn) {
            markUnreadBtn.classList.remove('mark-unread');
            markUnreadBtn.classList.add('mark-read');
            markUnreadBtn.innerHTML = '<i class="fas fa-check"></i>';
            markUnreadBtn.setAttribute('data-tooltip', 'Mark as read');
        }
        
        // Add unread dot
        const notificationTitle = notification.querySelector('.notification-title');
        if (notificationTitle && !notificationTitle.querySelector('.unread-dot')) {
            const unreadDot = document.createElement('span');
            unreadDot.className = 'unread-dot';
            notificationTitle.appendChild(unreadDot);
        }
    }
    
    function deleteNotification(notification) {
        // Add fade out animation
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(-20px)';
        
        setTimeout(() => {
            notification.remove();
            filterNotifications(); // Re-check empty state
        }, 300);
    }
    
    function updateUnreadCount() {
        const unreadNotifications = document.querySelectorAll('.notification-item.unread');
        const count = unreadNotifications.length;
        
        unreadCountElement.textContent = count;
        
        if (count === 0) {
            markAllReadBtn.style.opacity = '0.5';
            markAllReadBtn.style.pointerEvents = 'none';
        } else {
            markAllReadBtn.style.opacity = '1';
            markAllReadBtn.style.pointerEvents = 'auto';
        }
    }
    
    function handleActionClick(actionText, notification) {
        const notificationType = notification.getAttribute('data-type');
        
        switch(actionText) {
            case 'View Opportunity':
            case 'Apply Now':
                showToast('Redirecting to opportunities page...', 'info');
                // window.location.href = '../Opportunities/opportunities.html';
                break;
                
            case 'Reply':
            case 'View Message':
                showToast('Opening message...', 'info');
                // window.location.href = '../Messages/index.html';
                break;
                
            case 'View Analysis':
                showToast('Opening AI analysis...', 'info');
                // window.location.href = '../AI-Analysis/analysis.html';
                break;
                
            case 'Accept':
                showToast('Connection request accepted!', 'success');
                markAsRead(notification);
                break;
                
            case 'View Profile':
                showToast('Opening profile...', 'info');
                break;
                
            case 'View Badge':
            case 'Share Achievement':
                showToast('Opening achievements...', 'info');
                break;
                
            case 'Register Now':
            case 'Learn More':
                showToast('Opening event details...', 'info');
                break;
                
            case 'Save for Later':
                showToast('Opportunity saved to favorites!', 'success');
                break;
                
            case 'Download Report':
                showToast('Downloading analysis report...', 'info');
                break;
                
            case 'View Schedule':
                showToast('Opening training schedule...', 'info');
                break;
                
            case 'View Details':
                showToast('Opening detailed view...', 'info');
                break;
                
            default:
                showToast('Action completed!', 'info');
        }
    }
    
    // ================ TOAST NOTIFICATIONS ================
    
    function showToast(message, type = 'info') {
        // Remove existing toast
        const existingToast = document.querySelector('.toast-notification');
        if (existingToast) {
            existingToast.remove();
        }
        
        // Create toast
        const toast = document.createElement('div');
        toast.className = `toast-notification toast-${type}`;
        
        const icon = getToastIcon(type);
        toast.innerHTML = `
            <i class="${icon}"></i>
            <span>${message}</span>
        `;
        
        // Add to page
        document.body.appendChild(toast);
        
        // Animate in
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);
        
        // Remove after delay
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 300);
        }, 3000);
    }
    
    function getToastIcon(type) {
        switch(type) {
            case 'success':
                return 'fas fa-check-circle';
            case 'error':
                return 'fas fa-exclamation-circle';
            case 'warning':
                return 'fas fa-exclamation-triangle';
            default:
                return 'fas fa-info-circle';
        }
    }
    
    // ================ LOAD MORE FUNCTIONALITY ================
    
    loadMoreBtn.addEventListener('click', function() {
        // Simulate loading more notifications
        const loadingHTML = `
            <div class="loading-indicator">
                <i class="fas fa-spinner fa-spin"></i>
                Loading more notifications...
            </div>
        `;
        
        loadMoreBtn.innerHTML = loadingHTML;
        loadMoreBtn.disabled = true;
        
        setTimeout(() => {
            // Simulate adding new notifications
            showToast('No more notifications to load', 'info');
            loadMoreBtn.innerHTML = '<i class="fas fa-chevron-down"></i> Load More Notifications';
            loadMoreBtn.disabled = false;
        }, 2000);
    });
    
    // ================ KEYBOARD SHORTCUTS ================
    
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + A: Mark all as read
        if ((e.ctrlKey || e.metaKey) && e.key === 'a') {
            e.preventDefault();
            markAllReadBtn.click();
        }
        
        // Escape: Clear all filters
        if (e.key === 'Escape') {
            // Reset filters
            filterTabs.forEach(tab => tab.classList.remove('active'));
            document.querySelector('[data-filter="all"]').classList.add('active');
            statusFilter.value = 'all';
            priorityFilter.value = 'all';
            
            currentFilter = 'all';
            currentStatus = 'all';
            currentPriority = 'all';
            
            filterNotifications();
            showToast('Filters cleared', 'info');
        }
    });
    
    // ================ TOOLTIPS FOR CONTROL BUTTONS ================
    
    document.addEventListener('mouseenter', function(e) {
        if (e.target.closest('.control-btn')) {
            const btn = e.target.closest('.control-btn');
            const tooltip = btn.getAttribute('data-tooltip');
            
            if (tooltip) {
                showControlTooltip(btn, tooltip);
            }
        }
    }, true);
    
    document.addEventListener('mouseleave', function(e) {
        if (e.target.closest('.control-btn')) {
            hideControlTooltip();
        }
    }, true);
    
    function showControlTooltip(element, text) {
        let tooltip = document.getElementById('control-tooltip');
        
        if (!tooltip) {
            tooltip = document.createElement('div');
            tooltip.id = 'control-tooltip';
            tooltip.className = 'control-tooltip';
            document.body.appendChild(tooltip);
        }
        
        tooltip.textContent = text;
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = (rect.left + rect.width/2) + 'px';
        tooltip.style.top = (rect.top - 8) + 'px';
        tooltip.style.transform = 'translateX(-50%) translateY(-100%)';
        
        tooltip.classList.add('visible');
    }
    
    function hideControlTooltip() {
        const tooltip = document.getElementById('control-tooltip');
        if (tooltip) {
            tooltip.classList.remove('visible');
        }
    }
    
    // ================ SUBMENU FUNCTIONALITY ================
    
    // AI Menu
    const aiMenuItem = document.getElementById('aiMenuItem');
    const aiDropdownMenu = document.getElementById('aiDropdownMenu');
    
    if (aiMenuItem && aiDropdownMenu) {
        aiMenuItem.addEventListener('click', function(e) {
            e.preventDefault();
            this.classList.toggle('active');
        });
    }
    
    // Sports Menu
    const sportsMenuItem = document.getElementById('sportsMenuItem');
    const sportsSubmenu = document.getElementById('sportsSubmenu');
    
    if (sportsMenuItem && sportsSubmenu) {
        sportsMenuItem.addEventListener('click', function(e) {
            e.preventDefault();
            this.classList.toggle('active');
        });
    }
    
    // ================ INITIALIZATION ================
    
    // Initial setup
    updateUnreadCount();
    filterNotifications();
    
    // Add smooth scroll behavior
    document.documentElement.style.scrollBehavior = 'smooth';
    
    console.log('Notifications page initialized successfully!');
});

// ================ ADDITIONAL CSS FOR TOAST ================

// Add toast styles dynamically
const toastStyles = `
<style>
.toast-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    padding: 16px 20px;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 12px;
    z-index: 10000;
    transform: translateX(400px);
    opacity: 0;
    transition: all 0.3s ease;
    border-left: 4px solid #6a0dad;
    max-width: 350px;
}

.toast-notification.show {
    transform: translateX(0);
    opacity: 1;
}

.toast-notification.toast-success {
    border-left-color: #27ae60;
    color: #27ae60;
}

.toast-notification.toast-error {
    border-left-color: #e74c3c;
    color: #e74c3c;
}

.toast-notification.toast-warning {
    border-left-color: #f39c12;
    color: #f39c12;
}

.toast-notification.toast-info {
    border-left-color: #3498db;
    color: #3498db;
}

.toast-notification i {
    font-size: 18px;
}

.toast-notification span {
    color: #2c3e50;
    font-weight: 500;
}

.control-tooltip {
    position: fixed;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 6px 10px;
    border-radius: 6px;
    font-size: 12px;
    pointer-events: none;
    z-index: 10001;
    opacity: 0;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.control-tooltip.visible {
    opacity: 1;
}

.control-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.9);
}

.loading-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6a0dad;
}

.loading-indicator i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}
</style>
`;

document.head.insertAdjacentHTML('beforeend', toastStyles);