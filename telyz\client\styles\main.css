/* Estilos generales */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: '<PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: #f0f2f5;
    color: #333;
}

.container {
    display: flex;
    min-height: 100vh;
    max-width: 1050px; /* Reducido de 1110px a 1050px para una mejor distribución */
    margin: 0 auto;
    background-color: #f0f2f5;
}

/* Barra lateral izquierda */
.sidebar-left {
    background-color: #f0f2f5;
    padding: 15px 0;
    position: sticky;
    top: 0;
    height: 100vh;
    width: 270px; /* Ajustado a 270px para mantener una buena proporción */
    overflow-y: auto;
}

.logo {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

.logo-link {
    text-decoration: none;
    cursor: pointer;
    transition: transform 0.2s ease;
    display: block;
}

.logo-link:hover {
    transform: scale(1.05);
}

.logo-container {
    width: 40px;
    height: 40px;
    background-color: #6a0dad; /* Cambiado a color púrpura */
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(106, 13, 173, 0.3); /* Sombra púrpura */
}

.sidebar-menu {
    padding: 0 10px; /* Reducido el padding horizontal para acercar más los elementos al borde */
    max-height: calc(100vh - 100px);
    width: 100%;
}

.menu-title {
    font-size: 12px;
    color: #666;
    margin: 20px 0 8px;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.sidebar-menu ul {
    list-style: none;
}

.sidebar-menu > ul > li {
    margin: 4px 0;
    padding: 8px 5px; /* Reducido significativamente el padding horizontal para acercar más al borde */
    border-radius: 10px;
    transition: all 0.3s;
}

.sidebar-menu li:hover {
    background-color: rgba(106, 13, 173, 0.05); /* Fondo púrpura muy suave al pasar el ratón */
}

/* Submenu de deportes */
.has-submenu {
    position: relative;
}

.submenu {
    display: none;
    list-style: none;
    padding-left: 0; /* Eliminado el padding izquierdo */
    margin-top: 5px;
    margin-bottom: 5px;
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 8px;
    overflow: visible; /* Cambiado a visible para evitar recortes */
    max-height: 0;
    transition: max-height 0.3s ease-in-out;
    width: 100%; /* Ancho completo */
    min-width: 260px; /* Ancho mínimo para asegurar que el texto completo sea visible */
}

.has-submenu:hover .submenu {
    display: block;
    max-height: 400px; /* Aumentado para mostrar más contenido */
}

/* Estilos para los tooltips en el menú AI Analysis */
.js-tooltip {
    position: fixed;
    width: 220px;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px;
    border-radius: 6px;
    font-size: 12px;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
    z-index: 1001;
    line-height: 1.4;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    pointer-events: none;
    text-align: left;
}

.js-tooltip::before {
    content: "";
    position: absolute;
    top: 50%;
    left: -10px;
    transform: translateY(-50%);
    border-width: 5px;
    border-style: solid;
    border-color: transparent var(--tooltip-color, rgba(0, 0, 0, 0.8)) transparent transparent;
}

.js-tooltip.visible {
    opacity: 1;
    visibility: visible;
}

/* Colores específicos para cada opción en el menú de AI Analysis */
.ai-submenu li:nth-child(1) i { color: #FF5722; } /* Video Analysis */
.ai-submenu li:nth-child(2) i { color: #4CAF50; } /* Performance Stats */
.ai-submenu li:nth-child(3) i { color: #2196F3; } /* Player Comparison */
.ai-submenu li:nth-child(4) i { color: #FFC107; } /* Training Recommendations */
.ai-submenu li:nth-child(5) i { color: #9C27B0; } /* Talent Benchmarks */

.coming-soon-text {
    font-style: italic;
    color: #6a0dad; /* Cambiado a color púrpura */
    margin-top: 5px;
    font-size: 11px;
}

.submenu li {
    margin: 0;
    padding: 8px 12px 8px 5px; /* Reducido significativamente el padding izquierdo para acercar más al borde */
    min-width: 240px; /* Ancho mínimo para asegurar que el texto completo sea visible */
    position: relative;
}

.submenu a {
    font-size: 14px;
    padding: 4px 0;
    white-space: normal; /* Permitir que el texto se divida en varias líneas si es necesario */
    overflow: visible; /* Cambiado a visible para mostrar todo el texto */
    text-overflow: clip; /* Eliminado ellipsis */
    display: flex; /* Añadido display flex para mejor alineación */
    align-items: center; /* Centrado vertical */
    width: 100%; /* Ancho completo */
    line-height: 1.4; /* Mejorar la legibilidad */
}

.submenu a span {
    display: inline-block;
    white-space: normal; /* Permitir que el texto se divida en varias líneas si es necesario */
    overflow: visible;
    width: auto;
}

.submenu i {
    font-size: 16px;
    margin-right: 10px;
    width: 20px; /* Ancho ajustado */
    text-align: center;
    display: inline-flex; /* Cambiado a inline-flex para mejor alineación */
    justify-content: center; /* Centrado horizontal */
    align-items: center; /* Centrado vertical */
}

/* Ajuste específico para los iconos del menú AI Analysis */
.ai-submenu i {
    margin-left: 0; /* Eliminar cualquier margen izquierdo */
}

/* Ajustes específicos para el menú AI Analysis */
.ai-submenu {
    overflow: visible !important;
    width: 260px; /* Reducido de 300px a 260px para adaptarse al nuevo ancho del sidebar */
    padding: 8px 0; /* Añadir padding vertical */
}

.ai-submenu li {
    overflow: visible;
    padding: 12px 15px 12px 10px; /* Aumentar el padding vertical y horizontal */
    border-bottom: 1px solid rgba(0, 0, 0, 0.03); /* Añadir separador sutil */
}

.ai-submenu li:last-child {
    border-bottom: none; /* Eliminar borde del último elemento */
}

.ai-submenu a {
    align-items: flex-start; /* Alinear al inicio para mejor visualización con múltiples líneas */
}

.ai-submenu a span {
    padding-top: 2px; /* Pequeño ajuste para alinear mejor con el icono */
    font-weight: 500; /* Hacer el texto un poco más visible */
}

.ai-submenu i {
    font-size: 18px; /* Iconos ligeramente más grandes */
    margin-right: 12px; /* Más espacio entre el icono y el texto */
}

/* Colores específicos para cada deporte en el menú - Variaciones de púrpura */
.submenu li:nth-child(1) i { color: #8e44ad; } /* Football - Púrpura medio */
.submenu li:nth-child(2) i { color: #9b59b6; } /* Basketball - Púrpura claro */
.submenu li:nth-child(3) i { color: #6a0dad; } /* Volleyball - Púrpura principal */
.submenu li:nth-child(4) i { color: #5d3fd3; } /* Baseball - Púrpura azulado */
.submenu li:nth-child(5) i { color: #7d3c98; } /* Cricket - Púrpura oscuro */
.submenu li:nth-child(6) i { color: #a569bd; } /* Field Hockey - Púrpura rosado */

/* Estilo específico para el menú de AI Analysis - Ahora usando .ai-dropdown-item */

.sidebar-menu li.active {
    background-color: rgba(106, 13, 173, 0.08); /* Fondo púrpura suave */
}

.sidebar-menu li.active a {
    color: #6a0dad; /* Púrpura más intenso para elementos activos */
    font-weight: 600;
}

.sidebar-menu li.active i {
    color: #6a0dad; /* Mismo color púrpura para iconos activos */
}

.sidebar-menu a {
    text-decoration: none;
    color: #4a0080; /* Cambiado a un tono de púrpura más oscuro para mejor legibilidad */
    display: flex;
    align-items: center;
    font-size: 16px; /* Reducido de 17px a 16px */
    padding: 6px 0;
    white-space: nowrap; /* Evita que el texto se divida en varias líneas */
    overflow: hidden; /* Oculta el texto que excede el ancho */
    text-overflow: ellipsis; /* Muestra puntos suspensivos si el texto es demasiado largo */
    font-weight: 500; /* Ligeramente más grueso para mejor legibilidad con el nuevo color */
}

.sidebar-menu i {
    margin-right: 10px; /* Reducido para mejor alineación */
    font-size: 19px; /* Reducido de 21px a 19px */
    width: 22px; /* Reducido para mejor alineación */
    text-align: center;
    color: #6a0dad; /* Cambiado a color púrpura moderno */
}

/* Badge para elementos nuevos */
.badge {
    display: inline-block;
    padding: 2px 6px;
    font-size: 10px;
    font-weight: bold;
    border-radius: 10px;
    margin-left: 5px;
}

.new-badge {
    background-color: #ff3366;
    color: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    animation: pulse 2s infinite;
    transition: opacity 0.5s ease;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}
