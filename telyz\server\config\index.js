/**
 * Server Configuration
 * 
 * Configuration settings for the Telyz server.
 */

const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

module.exports = {
  // Server configuration
  server: {
    port: process.env.PORT || 5000,
    env: process.env.NODE_ENV || 'development',
  },
  
  // Database configuration
  database: {
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/telyz',
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    },
  },
  
  // JWT configuration
  jwt: {
    secret: process.env.JWT_SECRET || 'telyz-secret-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '1d',
  },
  
  // CORS configuration
  cors: {
    origin: process.env.CORS_ORIGIN || '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE'],
    allowedHeaders: ['Content-Type', 'Authorization'],
  },
  
  // File upload configuration
  upload: {
    directory: process.env.UPLOAD_DIR || 'uploads',
    maxSize: process.env.UPLOAD_MAX_SIZE || 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/png', 'video/mp4', 'video/quicktime'],
  },
  
  // AI Analysis configuration
  aiAnalysis: {
    modelPath: process.env.AI_MODEL_PATH || '../ai-analysis/models',
    tempDir: process.env.AI_TEMP_DIR || 'temp',
    maxVideoLength: process.env.AI_MAX_VIDEO_LENGTH || 600, // 10 minutes
  },
};
