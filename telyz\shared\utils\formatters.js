/**
 * Formatters Utility
 * 
 * Provides utility functions for formatting data in the Telyz platform.
 */

/**
 * Format a date to a string
 * 
 * @param {Date|string|number} date - The date to format
 * @param {string} format - The format string (default: 'YYYY-MM-DD')
 * @returns {string} Formatted date string
 */
const formatDate = (date, format = 'YYYY-MM-DD') => {
  const d = new Date(date);
  
  if (isNaN(d.getTime())) {
    return '';
  }
  
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
};

/**
 * Format a date to a relative time string (e.g., "2 hours ago")
 * 
 * @param {Date|string|number} date - The date to format
 * @returns {string} Relative time string
 */
const formatRelativeTime = (date) => {
  const d = new Date(date);
  
  if (isNaN(d.getTime())) {
    return '';
  }
  
  const now = new Date();
  const diffMs = now - d;
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);
  const diffMonth = Math.floor(diffDay / 30);
  const diffYear = Math.floor(diffDay / 365);
  
  if (diffSec < 60) {
    return diffSec === 1 ? '1 second ago' : `${diffSec} seconds ago`;
  } else if (diffMin < 60) {
    return diffMin === 1 ? '1 minute ago' : `${diffMin} minutes ago`;
  } else if (diffHour < 24) {
    return diffHour === 1 ? '1 hour ago' : `${diffHour} hours ago`;
  } else if (diffDay < 30) {
    return diffDay === 1 ? '1 day ago' : `${diffDay} days ago`;
  } else if (diffMonth < 12) {
    return diffMonth === 1 ? '1 month ago' : `${diffMonth} months ago`;
  } else {
    return diffYear === 1 ? '1 year ago' : `${diffYear} years ago`;
  }
};

/**
 * Format a number with commas as thousands separators
 * 
 * @param {number} number - The number to format
 * @returns {string} Formatted number string
 */
const formatNumber = (number) => {
  return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

/**
 * Format a number as currency
 * 
 * @param {number} amount - The amount to format
 * @param {string} currency - The currency code (default: 'USD')
 * @param {string} locale - The locale (default: 'en-US')
 * @returns {string} Formatted currency string
 */
const formatCurrency = (amount, currency = 'USD', locale = 'en-US') => {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency,
  }).format(amount);
};

/**
 * Format a number as a percentage
 * 
 * @param {number} value - The value to format (0-1)
 * @param {number} decimals - Number of decimal places (default: 0)
 * @returns {string} Formatted percentage string
 */
const formatPercentage = (value, decimals = 0) => {
  return (value * 100).toFixed(decimals) + '%';
};

/**
 * Format a duration in seconds to a time string (e.g., "2:30")
 * 
 * @param {number} seconds - The duration in seconds
 * @param {boolean} includeHours - Whether to include hours (default: false)
 * @returns {string} Formatted time string
 */
const formatDuration = (seconds, includeHours = false) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (includeHours || hours > 0) {
    return `${hours}:${String(minutes).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
  } else {
    return `${minutes}:${String(secs).padStart(2, '0')}`;
  }
};

/**
 * Format a file size in bytes to a human-readable string
 * 
 * @param {number} bytes - The file size in bytes
 * @param {number} decimals - Number of decimal places (default: 2)
 * @returns {string} Formatted file size string
 */
const formatFileSize = (bytes, decimals = 2) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(decimals)) + ' ' + sizes[i];
};

/**
 * Format a name (capitalize first letter of each word)
 * 
 * @param {string} name - The name to format
 * @returns {string} Formatted name
 */
const formatName = (name) => {
  if (!name) return '';
  
  return name
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
};

/**
 * Format a phone number
 * 
 * @param {string} phoneNumber - The phone number to format
 * @param {string} format - The format string (default: '(xxx) xxx-xxxx')
 * @returns {string} Formatted phone number
 */
const formatPhoneNumber = (phoneNumber, format = '(xxx) xxx-xxxx') => {
  if (!phoneNumber) return '';
  
  // Remove all non-digit characters
  const digits = phoneNumber.replace(/\D/g, '');
  
  // Replace x with digits
  let formattedNumber = format;
  for (let i = 0; i < digits.length && formattedNumber.includes('x'); i++) {
    formattedNumber = formattedNumber.replace('x', digits[i]);
  }
  
  // Remove any remaining x placeholders
  formattedNumber = formattedNumber.replace(/x/g, '');
  
  return formattedNumber;
};

module.exports = {
  formatDate,
  formatRelativeTime,
  formatNumber,
  formatCurrency,
  formatPercentage,
  formatDuration,
  formatFileSize,
  formatName,
  formatPhoneNumber,
};
