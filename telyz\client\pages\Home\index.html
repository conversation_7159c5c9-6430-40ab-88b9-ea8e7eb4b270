<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Telez - Plataforma para Profesionales Deportivos</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Agregamos fuentes de Google -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Barra lateral izquierda -->
        <div class="sidebar-left">
            <div class="logo">
                <a href="index.html" class="logo-link">
                    <div class="logo-container">T</div>
                </a>
            </div>
            <div class="sidebar-menu">
                <ul>
                    <li class="has-submenu" id="aiMenuItem">
    <a href="#"><i class="fas fa-robot"></i> AI Analysis <span class="badge new-badge">NEW</span></a>
    <ul class="submenu ai-submenu" id="aiDropdownMenu">
        <li>
            <a href="../../ai-analysis/interface/index.html" data-description="Upload game footage for AI breakdown of techniques and plays" data-color="#FF5722">
                <i class="fas fa-video"></i> <span>Video Analysis</span>
            </a>
        </li>
        <li>
            <a href="#" data-description="View weekly stats of star players and compare with your performance" data-color="#4CAF50">
                <i class="fas fa-chart-line"></i> <span>Performance Stats</span>
            </a>
        </li>
        <li>
            <a href="#" data-description="Compare your metrics with other players in your position" data-color="#2196F3">
                <i class="fas fa-users"></i> <span>Player Comparison</span>
            </a>
        </li>
        <li>
            <a href="#" data-description="Get personalized training plans based on your performance data" data-color="#FFC107">
                <i class="fas fa-dumbbell"></i> <span>Training Recommendations</span>
            </a>
        </li>
        <li>
            <a href="#" data-description="See how you measure against professional standards in your sport" data-color="#9C27B0">
                <i class="fas fa-trophy"></i> <span>Talent Benchmarks</span>
            </a>
        </li>
    </ul>
</li>
                    <li class="has-submenu" id="categoriesMenuItem">
                        <a href="#"><i class="fas fa-th-large"></i> Categories</a>
                        <ul class="submenu categories-submenu" id="categoriesSubmenu">
                            <li><a href="#" data-description="View all sports categories and levels" data-color="#607D8B">
                                <i class="fas fa-list-ul"></i> <span>All Categories</span>
                            </a></li>
                            <li><a href="#" data-description="Professional athletes and advanced players" data-color="#FF9800">
                                <i class="fas fa-medal"></i> <span>Professionals</span>
                            </a></li>
                            <li><a href="#" data-description="Semi-professional athletes and competitive players" data-color="#FFC107">
                                <i class="fas fa-award"></i> <span>Semi-Professionals</span>
                            </a></li>
                            <li><a href="#" data-description="Young athletes developing their skills in academy programs" data-color="#4CAF50">
                                <i class="fas fa-user-graduate"></i> <span>Youth Academy</span>
                            </a></li>
                            <li><a href="#" data-description="Amateur athletes and recreational players" data-color="#2196F3">
                                <i class="fas fa-heart"></i> <span>Amateurs</span>
                            </a></li>
                        </ul>
                    </li>
                    <li class="has-submenu" id="sportsMenuItem">
                        <a href="#"><i class="fas fa-running"></i> Sports</a>
                        <ul class="submenu sports-submenu" id="sportsSubmenu">
                            <li><a href="#"><i class="fas fa-futbol"></i> Football</a></li>
                            <li><a href="#"><i class="fas fa-basketball-ball"></i> Basketball</a></li>
                            <li><a href="#"><i class="fas fa-volleyball-ball"></i> Volleyball</a></li>
                            <li><a href="#"><i class="fas fa-baseball-ball"></i> Baseball</a></li>
                            <li><a href="#"><i class="fas fa-table-tennis"></i> Cricket</a></li>
                            <li><a href="#"><i class="fas fa-hockey-puck"></i> Field Hockey</a></li>
                        </ul>
                    </li>
                    <li class="has-submenu" id="exploreMenuItem">
                        <a href="#"><i class="fas fa-hashtag"></i> Explore</a>
                        <ul class="submenu explore-submenu" id="exploreSubmenu">
                            <li><a href="#" data-description="Read and write sports blogs, stories, and athletic experiences" data-color="#2196F3">
                                <i class="fas fa-blog"></i> <span>Blog</span>
                            </a></li>
                            <li><a href="#" data-description="Educational content and training resources for athletes" data-color="#4CAF50">
                                <i class="fas fa-graduation-cap"></i> <span>Education</span>
                            </a></li>
                            <li><a href="#" data-description="Discover new talents and promising athletes" data-color="#9C27B0">
                                <i class="fas fa-star"></i> <span>Talents</span>
                            </a></li>
                        </ul>
                    </li>
                    <li><a href="#"><i class="fas fa-briefcase"></i> Opportunities</a></li>
                    <li><a href="#"><i class="fas fa-bullhorn"></i> Announcements</a></li>
                    <li><a href="#"><i class="fas fa-envelope"></i> Messages</a></li>
                    <li><a href="../Marketplace/marketplace.html" data-description="Buy and sell sports equipment, gear, and athletic merchandise" data-color="#FF5722"><i class="fas fa-shopping-cart"></i> Marketplace</a></li>
                    <li><a href="#"><i class="fas fa-trophy"></i> Achievements</a></li>
                </ul>
                <div class="user-profile-sidebar">
                    <div class="profile-pic-small">
                        <img src="https://via.placeholder.com/40" alt="Profile" class="profile-pic-img">
                    </div>
                    <div class="profile-info-sidebar">
                        <span>John Doe</span>
                        <span class="teams-clubs">Teams & Clubs</span>
                    </div>
                    <div class="profile-dropdown-toggle" id="profileDropdownToggle">
                        <i class="fas fa-ellipsis-v"></i>
                    </div>
                    <div class="profile-dropdown-menu" id="profileDropdownMenu">
                        <a href="athletic_profile.html" class="profile-dropdown-item" data-tooltip="View your complete athletic profile and career history"><i class="fas fa-user"></i> View Athletic Profile</a>
                        <a href="#" class="profile-dropdown-item" data-tooltip="Edit your athletic profile information, skills and achievements"><i class="fas fa-cog"></i> Edit Athletic Profile</a>
                        <a href="#" class="profile-dropdown-item" data-tooltip="View and manage your sports achievements, trophies and awards"><i class="fas fa-medal"></i> Achievements</a>
                        <a href="sports_recommendations.html" class="profile-dropdown-item" data-tooltip="View and manage recommendations from coaches, scouts and sports experts"><i class="fas fa-star"></i> Expert Recommendations</a>
                        <a href="#" class="profile-dropdown-item" data-tooltip="Control your athletic profile privacy and who can view it"><i class="fas fa-shield-alt"></i> Privacy Settings</a>
                        <div class="dropdown-divider"></div>
                        <a href="#" class="profile-dropdown-item logout-item" data-tooltip="Sign out from your account"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </div>
                </div>

                <script>
                    // JavaScript para controlar el menú desplegable
                    document.addEventListener('DOMContentLoaded', function() {
                        const toggleButton = document.getElementById('profileDropdownToggle');
                        const dropdownMenu = document.getElementById('profileDropdownMenu');
                        const userProfile = document.querySelector('.user-profile-sidebar');

                        let isMenuOpen = false;

                        // Función para mostrar/ocultar el menú
                        toggleButton.addEventListener('click', function(e) {
                            e.stopPropagation();
                            isMenuOpen = !isMenuOpen;
                            dropdownMenu.classList.toggle('show', isMenuOpen);

                            // Asegurarse de que los iconos sean visibles
                            if (isMenuOpen) {
                                setTimeout(() => {
                                    const icons = dropdownMenu.querySelectorAll('i');
                                    icons.forEach(icon => {
                                        icon.style.display = 'inline-block';
                                    });
                                }, 50);
                            }
                        });

                        // Cerrar el menú al hacer clic fuera de él
                        document.addEventListener('click', function(e) {
                            if (!dropdownMenu.contains(e.target) && !toggleButton.contains(e.target) && !userProfile.contains(e.target)) {
                                isMenuOpen = false;
                                dropdownMenu.classList.remove('show');
                            }
                        });

                        // Evitar que el menú se cierre al hacer clic dentro de él
                        dropdownMenu.addEventListener('click', function(e) {
                            e.stopPropagation();
                        });

                        // Evitar que el menú se cierre al pasar el ratón sobre él
                        dropdownMenu.addEventListener('mouseenter', function() {
                            if (isMenuOpen) {
                                dropdownMenu.classList.add('show');
                            }
                        });

                        // Configurar tooltips para el menú del perfil
                        const profileTooltip = document.getElementById('profile-tooltip');
                        const profileMenuItems = dropdownMenu.querySelectorAll('.profile-dropdown-item');

                        profileMenuItems.forEach((item) => {
                            // Mostrar tooltip al pasar el ratón
                            item.addEventListener('mouseenter', function(event) {
                                const description = this.getAttribute('data-tooltip');
                                const rect = this.getBoundingClientRect();

                                // Configurar el tooltip
                                profileTooltip.textContent = description;
                                profileTooltip.style.backgroundColor = 'rgba(106, 13, 173, 0.9)';
                                profileTooltip.style.left = (rect.right + 15) + 'px';
                                profileTooltip.style.top = (rect.top + rect.height/2 - 20) + 'px';

                                // Configurar el color de la flecha
                                profileTooltip.style.setProperty('--tooltip-color', 'rgba(106, 13, 173, 0.9)');

                                // Mostrar el tooltip
                                profileTooltip.classList.add('visible');
                            });

                            // Ocultar tooltip al quitar el ratón
                            item.addEventListener('mouseleave', function() {
                                profileTooltip.classList.remove('visible');
                            });
                        });
                    });
                </script>
            </div>
        </div>

        <!-- Contenido central -->
        <div class="main-content">
            <!-- Área de creación de publicación -->
            <div class="post-creation">
                <div class="user-info-layout">
                    <div class="profile-pic-container">
                        <div class="profile-pic-circle">
                            <img src="https://i.pravatar.cc/150?img=12" alt="Profile Picture" class="profile-pic-img">
                        </div>
                    </div>
                    <div class="user-info-details">
                        <div class="user-name-container">
                            <h3>John Doe</h3>
                            <div class="eye-icon-container" id="watchingEyeIcon">
                                <i class="fas fa-eye"></i>
                                <span class="watchers-count">5</span>
                                <div class="eye-tooltip">5 scouts/clubs are actively monitoring your performance and potential</div>
                            </div>
                        </div>
                        <div class="team-info">
                            <img src="https://upload.wikimedia.org/wikipedia/en/4/47/FC_Barcelona_%28crest%29.svg" alt="FC Barcelona Logo" class="team-logo">
                            <span>FC Barcelona</span>
                        </div>
                        <span class="position">Professional Athlete</span>
                    </div>
                </div>
                <textarea class="simple-textarea" placeholder="Showcase your talents, share performance stats, or announce tryouts and opportunities..."></textarea>
                <div class="post-actions-new">
                    <div class="post-icons left-icons">
                        <button class="post-icon-btn" title="Showcase Skills & Highlights"><i class="fas fa-medal"></i></button>
                        <button class="post-icon-btn" title="Performance Statistics"><i class="fas fa-chart-bar"></i></button>
                    </div>
                    <button class="post-button">Showcase</button>
                    <div class="post-icons right-icons">
                        <button class="post-icon-btn" title="Talent Scout & Opportunities"><i class="fas fa-binoculars"></i></button>
                        <button class="post-icon-btn" title="Tryouts & Trials"><i class="fas fa-clipboard-check"></i></button>
                        <button class="post-icon-btn" title="Highlight Videos"><i class="fas fa-play-circle"></i></button>
                    </div>
                </div>
            </div>

            <!-- Publicaciones -->
            <div class="posts">
                

                <!-- Post 2: Basketball Video -->
                <div class="post">
                    <div class="post-header">
                        <div class="post-author">
                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face" alt="User Profile">
                            <div>
                                <h4>Stephen Curry</h4>
                                <p>Professional Basketball Player • Golden State Warriors</p>
                            </div>
                        </div>
                        <button class="follow-button">Follow</button>
                    </div>
                    <div class="post-content">
                        <div class="post-text">
                            <p>Three-point shooting practice session! 🎥🏀 Working on my range and accuracy. Ready for the playoffs! #Basketball #NBA #Warriors #Splash</p>
                        </div>
                        <div class="video-thumbnail">
                            <img src="https://images.unsplash.com/photo-1546519638-68e109498ffc?w=500&h=300&fit=crop" alt="Basketball shooting practice" class="post-image">
                            <div class="play-button-overlay">
                                <i class="fas fa-play"></i>
                            </div>
                            <div class="video-duration">2:48</div>
                        </div>
                    </div>
                    <div class="post-actions-bar">
                        <button class="action-button action-like"><i class="far fa-thumbs-up"></i> Like</button>
                        <button class="action-button action-medal"><i class="fas fa-medal"></i> Medal</button>
                        <button class="action-button action-comment"><i class="far fa-comment"></i> Comment</button>
                        <button class="action-button action-share"><i class="far fa-share-square"></i> Share</button>
                    </div>
                </div>

                

                <!-- Post 4: Swimming Video -->
                <div class="post">
                    <div class="post-header">
                        <div class="post-author">
                            <img src="https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=40&h=40&fit=crop&crop=face" alt="User Profile">
                            <div>
                                <h4>Adam Peaty</h4>
                                <p>Professional Swimmer • Team GB</p>
                            </div>
                        </div>
                        <button class="follow-button">Follow</button>
                    </div>
                    <div class="post-content">
                        <div class="post-text">
                            <p>Breaststroke technique session! 🎬🏊‍♂️ Working on my stroke efficiency and speed. Olympic preparation mode! #Swimming #Olympics #Breaststroke #Training</p>
                        </div>
                        <div class="video-thumbnail">
                            <img src="https://images.unsplash.com/photo-1530549387789-4c1017266635?w=500&h=300&fit=crop" alt="Swimming training session" class="post-image">
                            <div class="play-button-overlay">
                                <i class="fas fa-play"></i>
                            </div>
                            <div class="video-duration">3:05</div>
                        </div>
                    </div>
                    <div class="post-actions-bar">
                        <button class="action-button action-like"><i class="far fa-thumbs-up"></i> Like</button>
                        <button class="action-button action-medal"><i class="fas fa-medal"></i> Medal</button>
                        <button class="action-button action-comment"><i class="far fa-comment"></i> Comment</button>
                        <button class="action-button action-share"><i class="far fa-share-square"></i> Share</button>
                    </div>
                </div>

                

                <!-- Post 6: Weightlifting Video -->
                <div class="post">
                    <div class="post-header">
                        <div class="post-author">
                            <img src="https://images.unsplash.com/photo-1566492031773-4f4e44671d66?w=40&h=40&fit=crop&crop=face" alt="User Profile">
                            <div>
                                <h4>Lasha Talakhadze</h4>
                                <p>Olympic Weightlifter • Georgia</p>
                            </div>
                        </div>
                        <button class="follow-button">Follow</button>
                    </div>
                    <div class="post-content">
                        <div class="post-text">
                            <p>Heavy lifting session today! 🎬🏋️‍♂️ Working on my clean and jerk technique. Strength and precision! #Weightlifting #Olympics #CleanAndJerk #Strength</p>
                        </div>
                        <div class="video-thumbnail">
                            <img src="https://images.unsplash.com/photo-1581009146145-b5ef050c2e1e?w=500&h=300&fit=crop" alt="Weightlifting training session" class="post-image">
                            <div class="play-button-overlay">
                                <i class="fas fa-play"></i>
                            </div>
                            <div class="video-duration">2:35</div>
                        </div>
                    </div>
                    <div class="post-actions-bar">
                        <button class="action-button action-like"><i class="far fa-thumbs-up"></i> Like</button>
                        <button class="action-button action-medal"><i class="fas fa-medal"></i> Medal</button>
                        <button class="action-button action-comment"><i class="far fa-comment"></i> Comment</button>
                        <button class="action-button action-share"><i class="far fa-share-square"></i> Share</button>
                    </div>
                </div>

                <!-- Post 7: Boxing Video -->
                <div class="post">
                    <div class="post-header">
                        <div class="post-author">
                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face" alt="User Profile">
                            <div>
                                <h4>Anthony Joshua</h4>
                                <p>Professional Boxer • Heavyweight Champion</p>
                            </div>
                        </div>
                        <button class="follow-button">Follow</button>
                    </div>
                    <div class="post-content">
                        <div class="post-text">
                            <p>Intense boxing training camp! 🎬🥊 Working on my combinations and footwork. Ready for the next fight! #Boxing #Training #Heavyweight #Champion</p>
                        </div>
                        <div class="video-thumbnail">
                            <img src="https://images.unsplash.com/photo-1549719386-74dfcbf7dbed?w=500&h=300&fit=crop" alt="Boxing training session" class="post-image">
                            <div class="play-button-overlay">
                                <i class="fas fa-play"></i>
                            </div>
                            <div class="video-duration">3:45</div>
                        </div>
                    </div>
                    <div class="post-actions-bar">
                        <button class="action-button action-like"><i class="far fa-thumbs-up"></i> Like</button>
                        <button class="action-button action-medal"><i class="fas fa-medal"></i> Medal</button>
                        <button class="action-button action-comment"><i class="far fa-comment"></i> Comment</button>
                        <button class="action-button action-share"><i class="far fa-share-square"></i> Share</button>
                    </div>
                </div>

                <!-- Post 8: Cricket Video -->
                <div class="post">
                    <div class="post-header">
                        <div class="post-author">
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" alt="User Profile">
                            <div>
                                <h4>Virat Kohli</h4>
                                <p>Professional Cricketer • Team India</p>
                            </div>
                        </div>
                        <button class="follow-button">Follow</button>
                    </div>
                    <div class="post-content">
                        <div class="post-text">
                            <p>Batting practice session! 🎬🏏 Working on my cover drives and timing. Cricket is life! #Cricket #Batting #TeamIndia #Practice</p>
                        </div>
                        <div class="video-thumbnail">
                            <img src="https://images.unsplash.com/photo-1540747913346-19e32dc3e97e?w=500&h=300&fit=crop" alt="Cricket batting practice" class="post-image">
                            <div class="play-button-overlay">
                                <i class="fas fa-play"></i>
                            </div>
                            <div class="video-duration">4:08</div>
                        </div>
                    </div>
                    <div class="post-actions-bar">
                        <button class="action-button action-like"><i class="far fa-thumbs-up"></i> Like</button>
                        <button class="action-button action-medal"><i class="fas fa-medal"></i> Medal</button>
                        <button class="action-button action-comment"><i class="far fa-comment"></i> Comment</button>
                        <button class="action-button action-share"><i class="far fa-share-square"></i> Share</button>
                    </div>
                </div>

                

            </div>
        </div>

        <!-- Barra lateral derecha -->
        <div class="sidebar-right">
            <div class="search-bar">
                <i class="fas fa-search"></i>
                <input type="text" placeholder="Search Telez">
            </div>

            <div class="opportunities-section">
                <h3><i class="fas fa-briefcase"></i> Top Opportunities</h3>
                <p class="opportunities-subtitle">Discover the latest opportunities in sports</p>

                <!-- Oportunidad de Fútbol -->
                <div class="opportunity">
                    <div class="opportunity-header">
                        <span class="sport-tag tag-football"><i class="fas fa-futbol"></i> Football • Manchester, UK</span>
                    </div>
                    <h4>Midfielder Position at Manchester United FC</h4>
                    <div class="opportunity-details">
                        <div class="opportunity-date">
                            <i class="far fa-calendar"></i> Trial: June 15, 2023
                        </div>
                        <div class="opportunity-time">
                            <i class="far fa-clock"></i> Posted 2 days ago
                        </div>
                    </div>
                </div>

                <!-- Oportunidad de Baloncesto -->
                <div class="opportunity">
                    <div class="opportunity-header">
                        <span class="sport-tag tag-basketball"><i class="fas fa-basketball-ball"></i> Basketball • Chicago, USA</span>
                    </div>
                    <h4>Basketball Talent Camp by Chicago Bulls</h4>
                    <div class="opportunity-meta">
                        <span class="spots-available"><i class="fas fa-user-friends"></i> 20 spots available</span>
                    </div>
                    <div class="opportunity-details">
                        <div class="opportunity-time">
                            <i class="far fa-clock"></i> Posted 3 days ago
                        </div>
                    </div>
                </div>

                <!-- Oportunidad de Voleibol -->
                <div class="opportunity">
                    <div class="opportunity-header">
                        <span class="sport-tag tag-volleyball"><i class="fas fa-volleyball-ball"></i> Volleyball • Los Angeles, USA</span>
                    </div>
                    <h4>USA Volleyball National Team Tryouts</h4>
                    <div class="opportunity-details">
                        <div class="opportunity-date">
                            <i class="far fa-calendar"></i> Trial: July 10, 2023
                        </div>
                        <div class="opportunity-time">
                            <i class="far fa-clock"></i> Posted 2 days ago
                        </div>
                    </div>
                </div>

                <!-- Oportunidad de Béisbol -->
                <div class="opportunity">
                    <div class="opportunity-header">
                        <span class="sport-tag tag-baseball"><i class="fas fa-baseball-ball"></i> Baseball • New York, USA</span>
                    </div>
                    <h4>Pitcher Tryouts for NY Yankees</h4>
                </div>

                <!-- Oportunidad de Cricket -->
                <div class="opportunity">
                    <div class="opportunity-header">
                        <span class="sport-tag tag-cricket"><i class="fas fa-table-tennis"></i> Cricket • Mumbai, India</span>
                    </div>
                    <h4>Mumbai Cricket League Selections</h4>
                </div>

                <button class="view-more-button">View More Opportunities <i class="fas fa-arrow-right"></i></button>
            </div>

            <!-- Premium Section -->
            <a href="../Subscription/subscription.html" class="premium-card">
                <div class="premium-card-icon">
                    <i class="fas fa-crown"></i>
                </div>
                <div class="premium-card-content">
                    <span class="premium-card-title">Unlock Premium Features</span>
                    <span class="premium-card-subtitle">Access advanced analytics, exclusive opportunities, and more.</span>
                </div>
                <div class="premium-card-arrow">
                    <i class="fas fa-chevron-right"></i>
                </div>
            </a>

            <div class="follow-requests-section">
                <h3><i class="fas fa-user-plus"></i> Follow Requests</h3>
                <p class="follow-requests-subtitle">People who want to connect with you</p>

                <!-- Solicitud 1 -->
                <div class="follow-request">
                    <div class="request-user">
                        <img src="https://i.pravatar.cc/40?img=33" alt="User Profile" class="request-user-pic">
                        <div class="request-user-info">
                            <h4>David Rodriguez</h4>
                            <p>Football Scout • Real Madrid</p>
                        </div>
                    </div>
                    <div class="request-actions">
                        <button class="accept-button"><i class="fas fa-check"></i></button>
                        <button class="decline-button"><i class="fas fa-times"></i></button>
                    </div>
                </div>

                <!-- Solicitud 2 -->
                <div class="follow-request">
                    <div class="request-user">
                        <img src="https://i.pravatar.cc/40?img=44" alt="User Profile" class="request-user-pic">
                        <div class="request-user-info">
                            <h4>Sarah Johnson</h4>
                            <p>Basketball Coach • Chicago Bulls</p>
                        </div>
                    </div>
                    <div class="request-actions">
                        <button class="accept-button"><i class="fas fa-check"></i></button>
                        <button class="decline-button"><i class="fas fa-times"></i></button>
                    </div>
                </div>

                <button class="view-all-button">View All Requests <span class="request-count">5</span></button>
            </div>

            <!-- Discover Telyz Section -->
            <div class="discover-telyz-section">
                <h3><i class="fas fa-compass"></i> Discover Telyz</h3>
                <a href="../../ai-analysis/interface/index.html" class="discover-item discover-link">
                    <i class="fas fa-robot discover-icon"></i>
                    <span class="discover-text">Get your skills analyzed by our Guardiola AI.</span>
                </a>
                <a href="athletic_profile.html" class="discover-item discover-link">
                    <i class="fas fa-user-edit discover-icon"></i>
                    <span class="discover-text">Complete your athletic profile to attract scouts.</span>
                </a>
                <a href="opportunities.html" class="discover-item discover-link">
                    <i class="fas fa-briefcase discover-icon"></i>
                    <span class="discover-text">Find top opportunities in your sport.</span>
                </a>
            </div>

            <!-- Sponsors Section -->
            <div class="advertisements-section">
                <h3><i class="fas fa-star"></i> Sponsors</h3>
                <div class="ad-item">
                    <img src="https://images.unsplash.com/photo-1542291026-7eec264c27ab?w=80&h=60&fit=crop" alt="Running shoe advertisement" class="ad-image">
                    <p class="ad-text">New Performance Running Shoes - Feel the Speed!</p>
                </div>
                <div class="ad-item">
                    <img src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=80&h=60&fit=crop" alt="Professional sports training advertisement" class="ad-image">
                    <p class="ad-text">Elite Athletic Training Programs - Transform Your Performance!</p>
                </div>
                <div class="ad-item">
                    <img src="https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=80&h=60&fit=crop" alt="Sports nutrition advertisement" class="ad-image">
                    <p class="ad-text">Premium Sports Nutrition - Fuel Your Athletic Journey!</p>
                </div>
                <div class="ad-item">
                    <img src="https://images.unsplash.com/photo-1581009146145-b5ef050c2e1e?w=80&h=60&fit=crop" alt="Sports equipment advertisement" class="ad-image">
                    <p class="ad-text">Professional Sports Equipment - Gear Up for Victory!</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Botones flotantes -->
    <div class="floating-buttons">
        <button class="chat-button"><i class="fas fa-comment"></i></button>
        <button class="top-button"><i class="fas fa-arrow-up"></i></button>
    </div>

    <!-- Tooltips para los menús -->
    <div id="feature-tooltip" class="js-tooltip"></div>
    <div id="profile-tooltip" class="js-tooltip"></div>

    <!-- Script para la insignia NEW y el menú desplegable de AI Analysis -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Eye icon functionality
            const watchingEyeIcon = document.getElementById('watchingEyeIcon');
            if (watchingEyeIcon) {
                watchingEyeIcon.addEventListener('click', function() {
                    // Create notification for premium feature
                    showNotification('Talent Monitoring System', '5 scouts and clubs are actively monitoring your performance and career development. Complete an AI analysis and subscribe to premium to enhance your visibility to top clubs.');
                });
            }

            // Function to show notifications
            function showNotification(title, message) {
                // Create a notification element
                const notification = document.createElement('div');
                notification.className = 'feature-notification';
                notification.innerHTML = `
                    <div class="notification-content">
                        <i class="fas fa-eye"></i>
                        <div class="notification-text">
                            <strong>${title}</strong>
                            <p>${message}</p>
                        </div>
                        <button class="notification-close"><i class="fas fa-times"></i></button>
                    </div>
                `;

                // Add inline styles for the notification
                notification.style.position = 'fixed';
                notification.style.bottom = '20px';
                notification.style.right = '20px';
                notification.style.backgroundColor = 'white';
                notification.style.borderRadius = '8px';
                notification.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
                notification.style.zIndex = '9999';
                notification.style.overflow = 'hidden';
                notification.style.maxWidth = '350px';
                notification.style.animation = 'slideIn 0.3s forwards';

                // Styles for the notification content
                const notificationContent = notification.querySelector('.notification-content');
                notificationContent.style.display = 'flex';
                notificationContent.style.padding = '15px';
                notificationContent.style.alignItems = 'center';

                // Styles for the icon
                const icon = notification.querySelector('.fas.fa-eye');
                icon.style.color = '#2ecc71';
                icon.style.fontSize = '24px';
                icon.style.marginRight = '15px';
                icon.style.textShadow = '0 0 5px rgba(46, 204, 113, 0.5)';

                // Styles for the text
                const notificationText = notification.querySelector('.notification-text');
                notificationText.style.flex = '1';

                // Styles for the close button
                const closeButton = notification.querySelector('.notification-close');
                closeButton.style.background = 'none';
                closeButton.style.border = 'none';
                closeButton.style.cursor = 'pointer';
                closeButton.style.fontSize = '16px';
                closeButton.style.color = '#666';

                // Add the notification to the DOM
                document.body.appendChild(notification);

                // Add event for closing the notification
                closeButton.addEventListener('click', function() {
                    notification.style.animation = 'slideOut 0.3s forwards';
                    setTimeout(() => {
                        notification.remove();
                    }, 300);
                });

                // Auto-close after 5 seconds
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        notification.style.animation = 'slideOut 0.3s forwards';
                        setTimeout(() => {
                            notification.remove();
                        }, 300);
                    }
                }, 5000);
            }
        });
    </script>
</body>
</html>
