/**
 * Athlete Profile Model
 * 
 * Defines the schema for athlete profiles in the Telyz platform.
 */

const mongoose = require('mongoose');

const AthleteProfileSchema = new mongoose.Schema({
  // Reference to user
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  
  // Personal information
  dateOfBirth: {
    type: Date,
  },
  nationality: {
    type: String,
    trim: true,
  },
  height: {
    type: Number, // in cm
  },
  weight: {
    type: Number, // in kg
  },
  preferredFoot: {
    type: String,
    enum: ['right', 'left', 'both'],
  },
  jerseyNumber: {
    type: Number,
  },
  
  // Career information
  careerHistory: [{
    team: {
      type: String,
      required: true,
    },
    logo: {
      type: String,
    },
    position: {
      type: String,
    },
    startDate: {
      type: Date,
      required: true,
    },
    endDate: {
      type: Date,
    },
    achievements: [{
      title: {
        type: String,
        required: true,
      },
      description: {
        type: String,
      },
      date: {
        type: Date,
      },
    }],
    stats: {
      matches: {
        type: Number,
        default: 0,
      },
      goals: {
        type: Number,
        default: 0,
      },
      assists: {
        type: Number,
        default: 0,
      },
      rating: {
        type: Number,
        min: 0,
        max: 10,
        default: 0,
      },
    },
  }],
  
  // Skills and attributes
  specialties: [{
    type: String,
    trim: true,
  }],
  attributes: {
    speed: {
      type: Number,
      min: 0,
      max: 100,
      default: 0,
    },
    strength: {
      type: Number,
      min: 0,
      max: 100,
      default: 0,
    },
    intelligence: {
      type: Number,
      min: 0,
      max: 100,
      default: 0,
    },
    accuracy: {
      type: Number,
      min: 0,
      max: 100,
      default: 0,
    },
    // Additional attributes can be added based on sport
  },
  
  // Performance statistics
  stats: {
    totalMatches: {
      type: Number,
      default: 0,
    },
    totalGoals: {
      type: Number,
      default: 0,
    },
    totalAssists: {
      type: Number,
      default: 0,
    },
    averageRating: {
      type: Number,
      min: 0,
      max: 10,
      default: 0,
    },
  },
  
  // Recent matches
  recentMatches: [{
    date: {
      type: Date,
      required: true,
    },
    homeTeam: {
      name: {
        type: String,
        required: true,
      },
      logo: {
        type: String,
      },
      score: {
        type: Number,
        required: true,
      },
    },
    awayTeam: {
      name: {
        type: String,
        required: true,
      },
      logo: {
        type: String,
      },
      score: {
        type: Number,
        required: true,
      },
    },
    playerStats: {
      goals: {
        type: Number,
        default: 0,
      },
      assists: {
        type: Number,
        default: 0,
      },
      rating: {
        type: Number,
        min: 0,
        max: 10,
        default: 0,
      },
    },
  }],
  
  // Media and highlights
  media: [{
    type: {
      type: String,
      enum: ['video', 'photo', 'interview'],
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    description: {
      type: String,
    },
    url: {
      type: String,
      required: true,
    },
    thumbnail: {
      type: String,
    },
    date: {
      type: Date,
      default: Date.now,
    },
    views: {
      type: Number,
      default: 0,
    },
  }],
  
  // AI Analysis results
  aiAnalysis: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'AIAnalysis',
  }],
  
  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

// Pre-save middleware to update stats
AthleteProfileSchema.pre('save', function(next) {
  // Update total stats based on career history
  if (this.isModified('careerHistory')) {
    let totalMatches = 0;
    let totalGoals = 0;
    let totalAssists = 0;
    let totalRating = 0;
    let ratingCount = 0;
    
    this.careerHistory.forEach(career => {
      totalMatches += career.stats.matches || 0;
      totalGoals += career.stats.goals || 0;
      totalAssists += career.stats.assists || 0;
      
      if (career.stats.rating > 0) {
        totalRating += career.stats.rating;
        ratingCount++;
      }
    });
    
    this.stats.totalMatches = totalMatches;
    this.stats.totalGoals = totalGoals;
    this.stats.totalAssists = totalAssists;
    this.stats.averageRating = ratingCount > 0 ? (totalRating / ratingCount).toFixed(1) : 0;
  }
  
  // Update updatedAt
  this.updatedAt = Date.now();
  
  next();
});

module.exports = mongoose.model('AthleteProfile', AthleteProfileSchema);
