<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scout Dashboard - Telyz</title>
    <link rel="stylesheet" href="scout_dashboard.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="dashboard-container">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <div class="logo">
                    <div class="logo-container">T</div>
                    <span class="logo-text">Telyz Scout</span>
                </div>
            </div>
            <div class="header-center">
                <h1><i class="fas fa-binoculars"></i> Scout Dashboard</h1>
            </div>
            <div class="header-right">
                <div class="scout-profile">
                    <img src="https://i.pravatar.cc/40?img=15" alt="Scout Avatar" class="scout-avatar">
                    <span class="scout-name">Alex Martinez</span>
                    <span class="scout-role">Senior Scout</span>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="dashboard-main">
            <!-- Stats Cards -->
            <section class="stats-section">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-eye"></i>
                        </div>
                        <div class="stat-content">
                            <h3>24</h3>
                            <p>Players Watched</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="stat-content">
                            <h3>8</h3>
                            <p>High Priority</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-handshake"></i>
                        </div>
                        <div class="stat-content">
                            <h3>3</h3>
                            <p>Contacted</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-content">
                            <h3>+12%</h3>
                            <p>This Week</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Control Panel -->
            <section class="control-panel">
                <div class="panel-header">
                    <h2><i class="fas fa-sliders-h"></i> Control Center</h2>
                    <div class="panel-actions">
                        <button class="btn-action" id="addPlayerBtn">
                            <i class="fas fa-plus"></i> Add Player
                        </button>
                        <button class="btn-action" id="exportBtn">
                            <i class="fas fa-download"></i> Export List
                        </button>
                    </div>
                </div>

                <!-- Filters -->
                <div class="filters-row">
                    <div class="filter-group">
                        <label>Sport:</label>
                        <select id="sportFilter">
                            <option value="">All Sports</option>
                            <option value="football">Football</option>
                            <option value="basketball">Basketball</option>
                            <option value="volleyball">Volleyball</option>
                            <option value="baseball">Baseball</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>Priority:</label>
                        <select id="priorityFilter">
                            <option value="">All Priorities</option>
                            <option value="high">High Priority</option>
                            <option value="medium">Medium Priority</option>
                            <option value="low">Low Priority</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>Age:</label>
                        <select id="ageFilter">
                            <option value="">All Ages</option>
                            <option value="16-18">16-18 years</option>
                            <option value="19-21">19-21 years</option>
                            <option value="22-25">22-25 years</option>
                            <option value="25+">25+ years</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>Status:</label>
                        <select id="statusFilter">
                            <option value="">All Status</option>
                            <option value="watching">Watching</option>
                            <option value="contacted">Contacted</option>
                            <option value="negotiating">Negotiating</option>
                            <option value="signed">Signed</option>
                        </select>
                    </div>
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="searchInput" placeholder="Search players...">
                    </div>
                </div>
            </section>

            <!-- Players List -->
            <section class="players-section">
                <div class="section-header">
                    <h2><i class="fas fa-users"></i> Watched Players</h2>
                    <div class="view-toggle">
                        <button class="view-btn active" data-view="grid">
                            <i class="fas fa-th"></i>
                        </button>
                        <button class="view-btn" data-view="list">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>

                <div class="players-container" id="playersContainer">
                    <!-- Player Card 1 -->
                    <div class="player-card" data-sport="football" data-priority="high" data-age="19" data-status="watching">
                        <div class="player-header">
                            <img src="https://i.pravatar.cc/60?img=31" alt="Player" class="player-avatar">
                            <div class="player-basic-info">
                                <h3>Marcus Rodriguez</h3>
                                <p class="player-position">Midfielder</p>
                                <div class="player-details">
                                    <span class="player-age">19 years</span>
                                    <span class="player-sport">⚽ Football</span>
                                </div>
                            </div>
                            <div class="player-stats">
                                <div class="stat-item">
                                    <i class="fas fa-eye"></i>
                                    <span>15</span>
                                </div>
                                <div class="priority-badge high">
                                    <i class="fas fa-star"></i>
                                    High Priority
                                </div>
                            </div>
                        </div>
                        
                        <div class="player-metrics">
                            <div class="metric">
                                <span class="metric-label">Speed</span>
                                <div class="metric-bar">
                                    <div class="metric-fill" style="width: 85%"></div>
                                </div>
                                <span class="metric-value">85%</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Technique</span>
                                <div class="metric-bar">
                                    <div class="metric-fill" style="width: 92%"></div>
                                </div>
                                <span class="metric-value">92%</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Teamwork</span>
                                <div class="metric-bar">
                                    <div class="metric-fill" style="width: 78%"></div>
                                </div>
                                <span class="metric-value">78%</span>
                            </div>
                        </div>

                        <div class="player-notes">
                            <h4><i class="fas fa-sticky-note"></i> Private Notes</h4>
                            <textarea placeholder="Add your private notes about this player..." rows="3">Excellent ball control and vision. Shows great potential for professional level. Need to watch his defensive positioning.</textarea>
                        </div>

                        <div class="player-actions">
                            <button class="action-btn view-profile">
                                <i class="fas fa-user"></i> View Profile
                            </button>
                            <button class="action-btn contact-player">
                                <i class="fas fa-message"></i> Contact
                            </button>
                            <button class="action-btn remove-watch">
                                <i class="fas fa-eye-slash"></i> Remove
                            </button>
                            <div class="priority-selector">
                                <select>
                                    <option value="high" selected>High Priority</option>
                                    <option value="medium">Medium Priority</option>
                                    <option value="low">Low Priority</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Player Card 2 -->
                    <div class="player-card" data-sport="basketball" data-priority="medium" data-age="21" data-status="contacted">
                        <div class="player-header">
                            <img src="https://i.pravatar.cc/60?img=25" alt="Player" class="player-avatar">
                            <div class="player-basic-info">
                                <h3>Sarah Johnson</h3>
                                <p class="player-position">Point Guard</p>
                                <div class="player-details">
                                    <span class="player-age">21 years</span>
                                    <span class="player-sport">🏀 Basketball</span>
                                </div>
                            </div>
                            <div class="player-stats">
                                <div class="stat-item">
                                    <i class="fas fa-eye"></i>
                                    <span>8</span>
                                </div>
                                <div class="priority-badge medium">
                                    <i class="fas fa-star"></i>
                                    Medium
                                </div>
                            </div>
                        </div>
                        
                        <div class="player-metrics">
                            <div class="metric">
                                <span class="metric-label">Shooting</span>
                                <div class="metric-bar">
                                    <div class="metric-fill" style="width: 88%"></div>
                                </div>
                                <span class="metric-value">88%</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Defense</span>
                                <div class="metric-bar">
                                    <div class="metric-fill" style="width: 75%"></div>
                                </div>
                                <span class="metric-value">75%</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Leadership</span>
                                <div class="metric-bar">
                                    <div class="metric-fill" style="width: 94%"></div>
                                </div>
                                <span class="metric-value">94%</span>
                            </div>
                        </div>

                        <div class="player-notes">
                            <h4><i class="fas fa-sticky-note"></i> Private Notes</h4>
                            <textarea placeholder="Add your private notes about this player..." rows="3">Strong court vision and leadership. Already contacted - waiting for response. Consider offering training camp invitation.</textarea>
                        </div>

                        <div class="player-actions">
                            <button class="action-btn view-profile">
                                <i class="fas fa-user"></i> View Profile
                            </button>
                            <button class="action-btn contact-player contacted">
                                <i class="fas fa-check"></i> Contacted
                            </button>
                            <button class="action-btn remove-watch">
                                <i class="fas fa-eye-slash"></i> Remove
                            </button>
                            <div class="priority-selector">
                                <select>
                                    <option value="high">High Priority</option>
                                    <option value="medium" selected>Medium Priority</option>
                                    <option value="low">Low Priority</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Player Card 3 -->
                    <div class="player-card" data-sport="volleyball" data-priority="high" data-age="17" data-status="watching">
                        <div class="player-header">
                            <img src="https://i.pravatar.cc/60?img=45" alt="Player" class="player-avatar">
                            <div class="player-basic-info">
                                <h3>Emma Wilson</h3>
                                <p class="player-position">Spiker</p>
                                <div class="player-details">
                                    <span class="player-age">17 years</span>
                                    <span class="player-sport">🏐 Volleyball</span>
                                </div>
                            </div>
                            <div class="player-stats">
                                <div class="stat-item">
                                    <i class="fas fa-eye"></i>
                                    <span>22</span>
                                </div>
                                <div class="priority-badge high">
                                    <i class="fas fa-star"></i>
                                    High Priority
                                </div>
                            </div>
                        </div>
                        
                        <div class="player-metrics">
                            <div class="metric">
                                <span class="metric-label">Power</span>
                                <div class="metric-bar">
                                    <div class="metric-fill" style="width: 96%"></div>
                                </div>
                                <span class="metric-value">96%</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Accuracy</span>
                                <div class="metric-bar">
                                    <div class="metric-fill" style="width: 89%"></div>
                                </div>
                                <span class="metric-value">89%</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Agility</span>
                                <div class="metric-bar">
                                    <div class="metric-fill" style="width: 91%"></div>
                                </div>
                                <span class="metric-value">91%</span>
                            </div>
                        </div>

                        <div class="player-notes">
                            <h4><i class="fas fa-sticky-note"></i> Private Notes</h4>
                            <textarea placeholder="Add your private notes about this player..." rows="3">Exceptional talent! Very young but shows incredible promise. Recommend immediate contact - high competition from other scouts.</textarea>
                        </div>

                        <div class="player-actions">
                            <button class="action-btn view-profile">
                                <i class="fas fa-user"></i> View Profile
                            </button>
                            <button class="action-btn contact-player urgent">
                                <i class="fas fa-message"></i> Contact Now
                            </button>
                            <button class="action-btn remove-watch">
                                <i class="fas fa-eye-slash"></i> Remove
                            </button>
                            <div class="priority-selector">
                                <select>
                                    <option value="high" selected>High Priority</option>
                                    <option value="medium">Medium Priority</option>
                                    <option value="low">Low Priority</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Add Player Modal -->
    <div class="modal" id="addPlayerModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-plus"></i> Add Player to Watch List</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>Player Name or Username:</label>
                    <input type="text" id="playerSearch" placeholder="Search for player...">
                </div>
                <div class="form-group">
                    <label>Priority Level:</label>
                    <select id="newPlayerPriority">
                        <option value="high">High Priority</option>
                        <option value="medium" selected>Medium Priority</option>
                        <option value="low">Low Priority</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Initial Notes:</label>
                    <textarea id="initialNotes" placeholder="Add your initial thoughts about this player..." rows="4"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-cancel">Cancel</button>
                <button class="btn-confirm">Add to Watch List</button>
            </div>
        </div>
    </div>

    <script src="scout_dashboard.js"></script>
</body>
</html>