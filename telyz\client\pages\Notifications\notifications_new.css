/* Estilos generales */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: '<PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: #f0f2f5;
    color: #333;
}

.container {
    display: flex;
    min-height: 100vh;
    max-width: 1050px;
    margin: 0 auto;
    background-color: #f0f2f5;
}

/* Barra lateral izquierda */
.sidebar-left {
    background-color: #f0f2f5;
    padding: 15px 0;
    position: sticky;
    top: 0;
    height: 100vh;
    width: 270px;
    overflow-y: auto;
}

.logo {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

.logo-link {
    text-decoration: none;
    cursor: pointer;
    transition: transform 0.2s ease;
    display: block;
}

.logo-link:hover {
    transform: scale(1.05);
}

.logo-container {
    width: 40px;
    height: 40px;
    background-color: #6a0dad;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(106, 13, 173, 0.3);
}

.sidebar-menu {
    padding: 0 10px;
    max-height: calc(100vh - 100px);
    width: 100%;
}

.menu-title {
    font-size: 12px;
    color: #666;
    margin: 20px 0 8px;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.sidebar-menu ul {
    list-style: none;
}

.sidebar-menu > ul > li {
    margin: 4px 0;
    padding: 8px 5px;
    border-radius: 10px;
    transition: all 0.3s;
}

.sidebar-menu li:hover {
    background-color: rgba(106, 13, 173, 0.05);
}

/* Submenu de deportes */
.has-submenu {
    position: relative;
}

.submenu {
    display: none;
    list-style: none;
    padding-left: 0;
    margin-top: 5px;
    margin-bottom: 5px;
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 8px;
    overflow: visible;
    max-height: 0;
    transition: max-height 0.3s ease-in-out;
    width: 100%;
    min-width: 260px;
}

.has-submenu:hover .submenu {
    display: block;
    max-height: 400px;
}

/* Estilos para los tooltips en el menú AI Analysis */
.js-tooltip {
    position: fixed;
    width: 220px;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px;
    border-radius: 6px;
    font-size: 12px;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
    z-index: 1001;
    line-height: 1.4;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    pointer-events: none;
    text-align: left;
}

.js-tooltip::before {
    content: "";
    position: absolute;
    top: 50%;
    left: -10px;
    transform: translateY(-50%);
    border-width: 5px;
    border-style: solid;
    border-color: transparent var(--tooltip-color, rgba(0, 0, 0, 0.8)) transparent transparent;
}

.js-tooltip.visible {
    opacity: 1;
    visibility: visible;
}

/* Colores específicos para cada opción en el menú de AI Analysis */
.ai-submenu li:nth-child(1) i { color: #FF5722; } /* Video Analysis */
.ai-submenu li:nth-child(2) i { color: #4CAF50; } /* Performance Stats */
.ai-submenu li:nth-child(3) i { color: #2196F3; } /* Player Comparison */
.ai-submenu li:nth-child(4) i { color: #FFC107; } /* Training Recommendations */
.ai-submenu li:nth-child(5) i { color: #9C27B0; } /* Talent Benchmarks */

.coming-soon-text {
    font-style: italic;
    color: #6a0dad;
    margin-top: 5px;
    font-size: 11px;
}

.submenu li {
    margin: 0;
    padding: 8px 12px 8px 5px;
    min-width: 240px;
    position: relative;
}

.submenu a {
    font-size: 14px;
    padding: 4px 0;
    white-space: normal;
    overflow: visible;
    text-overflow: clip;
    display: flex;
    align-items: center;
    width: 100%;
    line-height: 1.4;
}

.submenu a span {
    display: inline-block;
    white-space: normal;
    overflow: visible;
    width: auto;
}

.submenu i {
    font-size: 16px;
    margin-right: 10px;
    width: 20px;
    text-align: center;
    display: inline-flex;
    justify-content: center;
    align-items: center;
}

/* Ajuste específico para los iconos del menú AI Analysis */
.ai-submenu i {
    margin-left: 0;
}

/* Ajustes específicos para el menú AI Analysis */
.ai-submenu {
    overflow: visible !important;
    width: 260px;
    padding: 8px 0;
}

.ai-submenu li {
    overflow: visible;
    padding: 12px 15px 12px 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.03);
}

.ai-submenu li:last-child {
    border-bottom: none;
}

.ai-submenu a {
    align-items: flex-start;
}

.ai-submenu a span {
    padding-top: 2px;
    font-weight: 500;
}

.ai-submenu i {
    font-size: 18px;
    margin-right: 12px;
}

/* Colores específicos para cada deporte en el menú - Variaciones de púrpura */
.submenu li:nth-child(1) i { color: #8e44ad; } /* Football - Púrpura medio */
.submenu li:nth-child(2) i { color: #9b59b6; } /* Basketball - Púrpura claro */
.submenu li:nth-child(3) i { color: #6a0dad; } /* Volleyball - Púrpura principal */
.submenu li:nth-child(4) i { color: #5d3fd3; } /* Baseball - Púrpura azulado */
.submenu li:nth-child(5) i { color: #7d3c98; } /* Cricket - Púrpura oscuro */
.submenu li:nth-child(6) i { color: #a569bd; } /* Field Hockey - Púrpura rosado */

.sidebar-menu li.active {
    background-color: rgba(106, 13, 173, 0.08);
}

.sidebar-menu a {
    text-decoration: none;
    color: #4a0080;
    display: flex;
    align-items: center;
    font-size: 16px;
    padding: 6px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 500;
    transition: all 0.3s;
    position: relative;
}

.sidebar-menu a:hover {
    background-color: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
    transform: translateX(2px);
}

.sidebar-menu a.active {
    color: #6a0dad;
    font-weight: 600;
}

.sidebar-menu i {
    margin-right: 15px;
    font-size: 20px;
    width: 25px;
    text-align: center;
    color: #6a0dad;
}

/* Badge styles */
.badge {
    background-color: #ff4757;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 8px;
    font-weight: 600;
}

.new-badge {
    background: linear-gradient(45deg, #ff6b6b, #ff4757);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* User profile sidebar */
.user-profile-sidebar {
    display: flex;
    align-items: center;
    margin-top: 23px;
    padding: 12px 18px;
    border-radius: 10px;
    background-color: rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
}

.user-profile-sidebar:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.profile-pic-small {
    width: 46px;
    height: 46px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 12px;
    border: 2px solid #6a0dad;
}

.profile-pic-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-info-sidebar {
    flex: 1;
}

.profile-info-sidebar span:first-child {
    display: block;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.profile-info-sidebar .teams-clubs {
    font-size: 12px;
    color: #666;
    margin-top: 2px;
}

.profile-dropdown-toggle {
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color 0.2s;
    color: #666;
}

.profile-dropdown-toggle:hover {
    background-color: rgba(0, 0, 0, 0.1);
    color: #333;
}

.profile-dropdown-toggle i {
    font-size: 20px;
    color: #444;
}

/* Profile dropdown menu */
.profile-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.2);
    width: 220px;
    z-index: 1000;
    overflow: hidden;
    display: none;
    border: 1px solid #eee;
    margin-top: 6px;
    max-height: 0;
    opacity: 0;
    transition: max-height 0.3s ease, opacity 0.3s ease;
    visibility: hidden;
    text-align: left;
}

.profile-dropdown-menu.show {
    display: block;
    max-height: 400px;
    opacity: 1;
    overflow-y: auto;
    z-index: 1001;
    visibility: visible;
}

.profile-dropdown-item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 14px 25px;
    text-decoration: none;
    color: #333;
    transition: all 0.2s ease;
    font-size: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.03);
    position: relative;
}

.profile-dropdown-item:last-child {
    border-bottom: none;
}

.profile-dropdown-item:hover {
    background-color: #f5f5f5;
    color: #6a0dad;
}

.profile-dropdown-item i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
    color: #6a0dad;
    font-size: 17px;
    display: inline-block;
}

.dropdown-divider {
    height: 1px;
    background-color: #e0e0e0;
    margin: 8px 0;
}

.logout-item {
    color: #e74c3c;
}

.logout-item:hover {
    background-color: rgba(231, 76, 60, 0.05);
    color: #e74c3c;
}

/* ================ MAIN CONTENT ================ */
.main-content {
    flex: 1;
    padding: 20px;
    background-color: white;
    border-radius: 15px;
    margin: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
    max-height: calc(100vh - 40px);
}

/* ================ NOTIFICATIONS HEADER ================ */
.notifications-header {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #f0f2f5;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-title h1 {
    color: #6a0dad;
    font-size: 28px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-title h1 i {
    color: #6a0dad;
    margin-right: 10px;
}

.header-title p {
    color: #666;
    font-size: 16px;
    margin: 0;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

.mark-all-read-btn {
    background: #6a0dad;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.mark-all-read-btn:hover {
    background: #5a0b8a;
    transform: translateY(-2px);
}

.notification-counter {
    background: #f8f9fa;
    padding: 8px 16px;
    border-radius: 20px;
    border: 1px solid #e9ecef;
    font-size: 14px;
    color: #333;
}

.unread-count {
    background: #ff4757;
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 12px;
    font-weight: 600;
    margin-right: 8px;
}

/* ================ NOTIFICATION FILTERS ================ */
.notification-filters {
    background: linear-gradient(135deg, #6a0dad, #8b4bcf);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 25px;
    border: 1px solid #6a0dad;
    box-shadow: 0 4px 15px rgba(106, 13, 173, 0.2);
}

.filters-header h3 {
    color: white;
    font-size: 18px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.filters-header h3 i {
    color: white;
}

.filter-tabs {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.filter-tab {
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 10px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    backdrop-filter: blur(10px);
}

.filter-tab:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.filter-tab.active {
    background: white;
    color: #6a0dad;
    border-color: white;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    font-weight: 600;
}

.filter-tab.active i {
    color: #6a0dad;
}

/* ================ NOTIFICATIONS LIST ================ */
.notifications-container {
    background: #f8f9fa;
    border-radius: 10px;
    border: 1px solid #e9ecef;
    overflow: hidden;
}

.notifications-list {
    padding: 15px;
    max-height: 800px;
    overflow-y: auto;
}

/* ================ NOTIFICATION ITEM ================ */
.notification-item {
    display: flex;
    padding: 20px;
    background: white;
    border-radius: 8px;
    margin-bottom: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
}

.notification-item:last-child {
    margin-bottom: 0;
}

.notification-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.notification-item.unread {
    border-left: 4px solid #6a0dad;
    background: #fefffe;
}

.notification-item.high-priority {
    border-left-color: #ff4757;
    background: #fff9f9;
}

.notification-item.read {
    opacity: 0.8;
}

.notification-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    flex-shrink: 0;
}

.notification-icon.opportunity {
    background: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
}

.notification-icon.message {
    background: rgba(52, 152, 219, 0.1);
    color: #3498db;
}

.notification-icon.achievement {
    background: rgba(241, 196, 15, 0.1);
    color: #f1c40f;
}

.notification-icon.system {
    background: rgba(149, 165, 166, 0.1);
    color: #95a5a6;
}

.notification-icon i {
    font-size: 20px;
}

.notification-content {
    flex: 1;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
}

.notification-header h4 {
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
    margin: 0;
    line-height: 1.3;
}

.notification-time {
    color: #7f8c8d;
    font-size: 12px;
    white-space: nowrap;
    margin-left: 16px;
}

.notification-text {
    color: #5d6d7e;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 12px;
}

.notification-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.action-btn {
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #6a0dad;
    color: white;
}

.btn-primary:hover {
    background: #5a0b8a;
}

.btn-secondary {
    background: #ecf0f1;
    color: #2c3e50;
}

.btn-secondary:hover {
    background: #d5dbdb;
}

.notification-options {
    margin-left: 16px;
}

.options-btn {
    background: transparent;
    border: none;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    color: #bdc3c7;
    transition: all 0.3s ease;
}

.options-btn:hover {
    background: rgba(189, 195, 199, 0.2);
    color: #7f8c8d;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        flex-direction: column;
        max-width: 100%;
    }
    
    .sidebar-left {
        width: 100%;
        height: auto;
        position: relative;
    }
    
    .main-content {
        margin: 10px;
        border-radius: 10px;
    }
    
    .header-content {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .header-actions {
        flex-direction: column;
        width: 100%;
    }
    
    .filter-tabs {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .notification-item {
        flex-direction: column;
        gap: 15px;
    }
    
    .notification-icon {
        margin-right: 0;
        align-self: flex-start;
    }
}