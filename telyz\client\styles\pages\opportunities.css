/* Opportunities Page Styles */

.opportunities-content {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.opportunities-header {
    margin-bottom: 30px;
    text-align: center;
}

.opportunities-header h1 {
    font-size: 32px;
    font-weight: 700;
    color: #333;
    margin-bottom: 10px;
}

.opportunities-subtitle {
    font-size: 16px;
    color: #666;
    margin-bottom: 25px;
}

.opportunities-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 30px;
}

.primary-button, .secondary-button {
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    border: none;
}

.primary-button {
    background-color: #6a0dad;
    color: white;
}

.primary-button:hover {
    background-color: #5a0b9a;
    transform: translateY(-2px);
}

.secondary-button {
    background-color: white;
    color: #6a0dad;
    border: 2px solid #6a0dad;
}

.secondary-button:hover {
    background-color: #6a0dad;
    color: white;
}

/* Filters Section */
.opportunities-filters {
    background-color: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.filter-select, .filter-input {
    padding: 10px 12px;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    font-size: 14px;
    min-width: 150px;
    transition: border-color 0.3s ease;
}

.filter-select:focus, .filter-input:focus {
    outline: none;
    border-color: #6a0dad;
}

.filter-button {
    padding: 10px 20px;
    background-color: #6a0dad;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-button:hover {
    background-color: #5a0b9a;
}

/* Opportunities List */
.opportunities-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.opportunity-card {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    padding: 25px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid #f0f0f0;
}

.opportunity-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.opportunity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.sport-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 6px;
}

.sport-badge.football {
    background-color: rgba(76, 175, 80, 0.1);
    color: #4caf50;
}

.sport-badge.basketball {
    background-color: rgba(255, 152, 0, 0.1);
    color: #ff9800;
}

.sport-badge.volleyball {
    background-color: rgba(233, 30, 99, 0.1);
    color: #e91e63;
}

.opportunity-date {
    font-size: 12px;
    color: #666;
    display: flex;
    align-items: center;
    gap: 5px;
}

.opportunity-title {
    font-size: 18px;
    font-weight: 700;
    color: #333;
    margin-bottom: 12px;
    line-height: 1.3;
}

.opportunity-description {
    font-size: 14px;
    color: #555;
    line-height: 1.6;
    margin-bottom: 20px;
}

.opportunity-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 20px;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    color: #666;
}

.detail-item i {
    color: #6a0dad;
    width: 16px;
    text-align: center;
}

.opportunity-actions {
    display: flex;
    gap: 10px;
    justify-content: space-between;
}

.apply-btn, .save-btn, .share-btn {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    border: none;
}

.apply-btn {
    background-color: #6a0dad;
    color: white;
    flex: 1;
}

.apply-btn:hover {
    background-color: #5a0b9a;
}

.save-btn, .share-btn {
    background-color: #f5f5f5;
    color: #666;
    padding: 8px 12px;
}

.save-btn:hover, .share-btn:hover {
    background-color: #e0e0e0;
    color: #333;
}

/* Load More Section */
.load-more-section {
    text-align: center;
    margin-top: 40px;
}

.load-more-btn {
    padding: 12px 30px;
    background-color: white;
    color: #6a0dad;
    border: 2px solid #6a0dad;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.load-more-btn:hover {
    background-color: #6a0dad;
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .opportunities-list {
        grid-template-columns: 1fr;
    }
    
    .opportunities-filters {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-group {
        width: 100%;
    }
    
    .filter-select, .filter-input {
        min-width: auto;
        width: 100%;
    }
    
    .opportunities-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .primary-button, .secondary-button {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
}
