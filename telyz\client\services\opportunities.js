/**
 * Opportunities Service
 * 
 * Service for handling opportunities operations.
 */

const api = require('./api');

class OpportunitiesService {
  /**
   * Create an opportunities service
   */
  constructor() {
    // Bind methods
    this.getOpportunities = this.getOpportunities.bind(this);
    this.getOpportunityById = this.getOpportunityById.bind(this);
    this.createOpportunity = this.createOpportunity.bind(this);
    this.updateOpportunity = this.updateOpportunity.bind(this);
    this.deleteOpportunity = this.deleteOpportunity.bind(this);
    this.applyForOpportunity = this.applyForOpportunity.bind(this);
    this.updateApplicationStatus = this.updateApplicationStatus.bind(this);
    this.searchOpportunities = this.searchOpportunities.bind(this);
    this.getFeaturedOpportunities = this.getFeaturedOpportunities.bind(this);
    this.getRecommendedOpportunities = this.getRecommendedOpportunities.bind(this);
    this.shareOpportunity = this.shareOpportunity.bind(this);
    this.saveOpportunity = this.saveOpportunity.bind(this);
    this.unsaveOpportunity = this.unsaveOpportunity.bind(this);
  }
  
  /**
   * Get all opportunities
   * 
   * @param {Object} params - Query parameters
   * @returns {Promise} Promise with opportunities data
   */
  async getOpportunities(params = {}) {
    try {
      return await api.get('/opportunities', params);
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * Get opportunity by ID
   * 
   * @param {string} id - Opportunity ID
   * @returns {Promise} Promise with opportunity data
   */
  async getOpportunityById(id) {
    try {
      return await api.get(`/opportunities/${id}`);
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * Create a new opportunity
   * 
   * @param {Object} opportunityData - Opportunity data
   * @returns {Promise} Promise with created opportunity data
   */
  async createOpportunity(opportunityData) {
    try {
      return await api.post('/opportunities', opportunityData);
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * Update an opportunity
   * 
   * @param {string} id - Opportunity ID
   * @param {Object} opportunityData - Updated opportunity data
   * @returns {Promise} Promise with updated opportunity data
   */
  async updateOpportunity(id, opportunityData) {
    try {
      return await api.put(`/opportunities/${id}`, opportunityData);
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * Delete an opportunity
   * 
   * @param {string} id - Opportunity ID
   * @returns {Promise} Promise that resolves when opportunity is deleted
   */
  async deleteOpportunity(id) {
    try {
      return await api.delete(`/opportunities/${id}`);
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * Apply for an opportunity
   * 
   * @param {string} id - Opportunity ID
   * @param {Object} applicationData - Application data
   * @returns {Promise} Promise with application data
   */
  async applyForOpportunity(id, applicationData = {}) {
    try {
      return await api.post(`/opportunities/${id}/apply`, applicationData);
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * Update application status
   * 
   * @param {string} opportunityId - Opportunity ID
   * @param {string} userId - User ID
   * @param {string} status - New status
   * @returns {Promise} Promise with updated application data
   */
  async updateApplicationStatus(opportunityId, userId, status) {
    try {
      return await api.put(`/opportunities/${opportunityId}/applications/${userId}`, { status });
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * Search opportunities
   * 
   * @param {Object} params - Search parameters
   * @returns {Promise} Promise with search results
   */
  async searchOpportunities(params = {}) {
    try {
      return await api.get('/opportunities/search', params);
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * Get featured opportunities
   * 
   * @param {Object} params - Query parameters
   * @returns {Promise} Promise with featured opportunities data
   */
  async getFeaturedOpportunities(params = {}) {
    try {
      return await api.get('/opportunities/featured', params);
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * Get recommended opportunities for current user
   * 
   * @param {Object} params - Query parameters
   * @returns {Promise} Promise with recommended opportunities data
   */
  async getRecommendedOpportunities(params = {}) {
    try {
      return await api.get('/opportunities/recommended', params);
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * Share an opportunity
   * 
   * @param {string} id - Opportunity ID
   * @param {Object} shareData - Share data
   * @returns {Promise} Promise that resolves when opportunity is shared
   */
  async shareOpportunity(id, shareData = {}) {
    try {
      return await api.post(`/opportunities/${id}/share`, shareData);
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * Save an opportunity
   * 
   * @param {string} id - Opportunity ID
   * @returns {Promise} Promise that resolves when opportunity is saved
   */
  async saveOpportunity(id) {
    try {
      return await api.post(`/opportunities/${id}/save`);
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * Unsave an opportunity
   * 
   * @param {string} id - Opportunity ID
   * @returns {Promise} Promise that resolves when opportunity is unsaved
   */
  async unsaveOpportunity(id) {
    try {
      return await api.delete(`/opportunities/${id}/save`);
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * Get user applications
   * 
   * @param {string} userId - User ID
   * @returns {Promise} Promise with user applications data
   */
  async getUserApplications(userId) {
    try {
      return await api.get(`/users/${userId}/applications`);
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * Get user opportunities
   * 
   * @param {string} userId - User ID
   * @returns {Promise} Promise with user opportunities data
   */
  async getUserOpportunities(userId) {
    try {
      return await api.get(`/users/${userId}/opportunities`);
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * Filter opportunities by criteria
   * 
   * @param {Array} opportunities - Opportunities to filter
   * @param {Object} filters - Filter criteria
   * @returns {Array} Filtered opportunities
   */
  filterOpportunities(opportunities, filters = {}) {
    return opportunities.filter(opportunity => {
      // Filter by sport
      if (filters.sport && opportunity.sport !== filters.sport) {
        return false;
      }
      
      // Filter by type
      if (filters.type && opportunity.type !== filters.type) {
        return false;
      }
      
      // Filter by location
      if (filters.location) {
        const locationMatch = 
          opportunity.location.city.toLowerCase().includes(filters.location.toLowerCase()) ||
          opportunity.location.country.toLowerCase().includes(filters.location.toLowerCase());
        
        if (!locationMatch) {
          return false;
        }
      }
      
      // Filter by position
      if (filters.position && !opportunity.positions.includes(filters.position)) {
        return false;
      }
      
      // Filter by status
      if (filters.status && opportunity.status !== filters.status) {
        return false;
      }
      
      // Filter by search term
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        const searchMatch = 
          opportunity.title.toLowerCase().includes(searchTerm) ||
          opportunity.description.toLowerCase().includes(searchTerm) ||
          opportunity.organization.name.toLowerCase().includes(searchTerm);
        
        if (!searchMatch) {
          return false;
        }
      }
      
      return true;
    });
  }
}

// Create opportunities service instance
const opportunities = new OpportunitiesService();

// Export opportunities service
if (typeof module !== 'undefined' && module.exports) {
  module.exports = opportunities;
}
