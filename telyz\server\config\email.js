/**
 * Email Configuration
 * 
 * Configuration and setup for email sending.
 */

const nodemailer = require('nodemailer');
const config = require('./index');

/**
 * Create email transporter
 * 
 * @returns {Object} Nodemailer transporter
 */
const createTransporter = () => {
  return nodemailer.createTransport({
    host: config.email.host,
    port: config.email.port,
    secure: config.email.secure,
    auth: {
      user: config.email.auth.user,
      pass: config.email.auth.pass,
    },
  });
};

/**
 * Send an email
 * 
 * @param {Object} options - Email options
 * @param {string} options.to - Recipient email
 * @param {string} options.subject - Email subject
 * @param {string} options.text - Plain text content
 * @param {string} options.html - HTML content
 * @param {string} options.from - Sender email (optional)
 * @param {string} options.replyTo - Reply-to email (optional)
 * @param {Array} options.attachments - Email attachments (optional)
 * @returns {Promise} Promise with send result
 */
const sendEmail = async (options) => {
  try {
    const transporter = createTransporter();
    
    const mailOptions = {
      from: options.from || config.email.from,
      to: options.to,
      subject: options.subject,
      text: options.text,
      html: options.html,
    };
    
    if (options.replyTo) {
      mailOptions.replyTo = options.replyTo;
    }
    
    if (options.attachments) {
      mailOptions.attachments = options.attachments;
    }
    
    const result = await transporter.sendMail(mailOptions);
    
    return result;
  } catch (error) {
    console.error(`Email sending error: ${error.message}`);
    throw error;
  }
};

/**
 * Send a welcome email
 * 
 * @param {Object} user - User data
 * @returns {Promise} Promise with send result
 */
const sendWelcomeEmail = async (user) => {
  const subject = 'Welcome to Telyz!';
  const text = `Hi ${user.firstName},\n\nWelcome to Telyz! We're excited to have you join our platform for sports professionals.\n\nBest regards,\nThe Telyz Team`;
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #6a0dad;">Welcome to Telyz!</h2>
      <p>Hi ${user.firstName},</p>
      <p>Welcome to Telyz! We're excited to have you join our platform for sports professionals.</p>
      <p>Here are a few things you can do to get started:</p>
      <ul>
        <li>Complete your profile</li>
        <li>Connect with other professionals</li>
        <li>Explore opportunities</li>
        <li>Upload videos for AI analysis</li>
      </ul>
      <p>If you have any questions, feel free to contact us.</p>
      <p>Best regards,<br>The Telyz Team</p>
    </div>
  `;
  
  return sendEmail({
    to: user.email,
    subject,
    text,
    html,
  });
};

/**
 * Send a verification email
 * 
 * @param {Object} user - User data
 * @param {string} token - Verification token
 * @returns {Promise} Promise with send result
 */
const sendVerificationEmail = async (user, token) => {
  const verificationUrl = `${config.server.host}/verify-email?token=${token}`;
  
  const subject = 'Verify Your Email Address';
  const text = `Hi ${user.firstName},\n\nPlease verify your email address by clicking the following link: ${verificationUrl}\n\nIf you did not create an account, please ignore this email.\n\nBest regards,\nThe Telyz Team`;
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #6a0dad;">Verify Your Email Address</h2>
      <p>Hi ${user.firstName},</p>
      <p>Please verify your email address by clicking the button below:</p>
      <p style="text-align: center;">
        <a href="${verificationUrl}" style="display: inline-block; background-color: #6a0dad; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Verify Email</a>
      </p>
      <p>If the button doesn't work, you can also click on the link below:</p>
      <p><a href="${verificationUrl}">${verificationUrl}</a></p>
      <p>If you did not create an account, please ignore this email.</p>
      <p>Best regards,<br>The Telyz Team</p>
    </div>
  `;
  
  return sendEmail({
    to: user.email,
    subject,
    text,
    html,
  });
};

/**
 * Send a password reset email
 * 
 * @param {Object} user - User data
 * @param {string} token - Reset token
 * @returns {Promise} Promise with send result
 */
const sendPasswordResetEmail = async (user, token) => {
  const resetUrl = `${config.server.host}/reset-password?token=${token}`;
  
  const subject = 'Reset Your Password';
  const text = `Hi ${user.firstName},\n\nYou requested a password reset. Please click the following link to reset your password: ${resetUrl}\n\nIf you did not request a password reset, please ignore this email.\n\nBest regards,\nThe Telyz Team`;
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #6a0dad;">Reset Your Password</h2>
      <p>Hi ${user.firstName},</p>
      <p>You requested a password reset. Please click the button below to reset your password:</p>
      <p style="text-align: center;">
        <a href="${resetUrl}" style="display: inline-block; background-color: #6a0dad; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reset Password</a>
      </p>
      <p>If the button doesn't work, you can also click on the link below:</p>
      <p><a href="${resetUrl}">${resetUrl}</a></p>
      <p>If you did not request a password reset, please ignore this email.</p>
      <p>Best regards,<br>The Telyz Team</p>
    </div>
  `;
  
  return sendEmail({
    to: user.email,
    subject,
    text,
    html,
  });
};

module.exports = {
  sendEmail,
  sendWelcomeEmail,
  sendVerificationEmail,
  sendPasswordResetEmail,
};
