/**
 * Database Service
 * 
 * Handles database connection and operations.
 */

const mongoose = require('mongoose');
const config = require('../../config');

/**
 * Connect to MongoDB database
 * @returns {Promise} Mongoose connection promise
 */
const connect = async () => {
  try {
    const conn = await mongoose.connect(config.database.uri, config.database.options);
    console.log(`MongoDB Connected: ${conn.connection.host}`);
    return conn;
  } catch (error) {
    console.error(`Error connecting to MongoDB: ${error.message}`);
    process.exit(1);
  }
};

/**
 * Disconnect from MongoDB database
 * @returns {Promise} Mongoose disconnection promise
 */
const disconnect = async () => {
  try {
    await mongoose.disconnect();
    console.log('MongoDB Disconnected');
  } catch (error) {
    console.error(`Error disconnecting from MongoDB: ${error.message}`);
  }
};

/**
 * Check if MongoDB is connected
 * @returns {boolean} Connection status
 */
const isConnected = () => {
  return mongoose.connection.readyState === 1;
};

module.exports = {
  connect,
  disconnect,
  isConnected,
  connection: mongoose.connection,
};
