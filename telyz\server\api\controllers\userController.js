/**
 * User Controller
 * 
 * Controller functions for user management.
 */

const User = require('../../models/User');
const AthleteProfile = require('../../models/AthleteProfile');
const AIAnalysis = require('../../models/AIAnalysis');
const Opportunity = require('../../models/Opportunity');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const config = require('../../config');

/**
 * Get all users (admin only)
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response
 */
exports.getAllUsers = async (req, res) => {
  try {
    const { page = 1, limit = 10, role, sport, search } = req.query;
    
    // Build query
    const query = {};
    
    if (role) {
      query.role = role;
    }
    
    if (sport) {
      query.sport = sport;
    }
    
    if (search) {
      query.$or = [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
      ];
    }
    
    // Execute query with pagination
    const users = await User.find(query)
      .select('-password')
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });
    
    // Get total count
    const count = await User.countDocuments(query);
    
    return res.status(200).json({
      users,
      totalPages: Math.ceil(count / limit),
      currentPage: page,
      totalUsers: count,
    });
  } catch (error) {
    console.error('Error getting users:', error);
    return res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get user by ID
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response
 */
exports.getUserById = async (req, res) => {
  try {
    const user = await User.findById(req.params.id).select('-password');
    
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    return res.status(200).json(user);
  } catch (error) {
    console.error('Error getting user:', error);
    return res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Create a new user
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response
 */
exports.createUser = async (req, res) => {
  try {
    const { firstName, lastName, email, password, role, sport, position } = req.body;
    
    // Check if user already exists
    let user = await User.findOne({ email });
    
    if (user) {
      return res.status(400).json({ message: 'User already exists' });
    }
    
    // Create new user
    user = new User({
      firstName,
      lastName,
      email,
      password,
      role,
      sport,
      position,
    });
    
    // Save user
    await user.save();
    
    // If user is an athlete, create athlete profile
    if (role === 'athlete') {
      const athleteProfile = new AthleteProfile({
        user: user._id,
      });
      
      await athleteProfile.save();
    }
    
    // Generate JWT token
    const payload = {
      user: {
        id: user.id,
        role: user.role,
      },
    };
    
    const token = jwt.sign(payload, config.jwt.secret, {
      expiresIn: config.jwt.expiresIn,
    });
    
    return res.status(201).json({
      token,
      user: {
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        role: user.role,
        sport: user.sport,
      },
    });
  } catch (error) {
    console.error('Error creating user:', error);
    return res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Update user
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response
 */
exports.updateUser = async (req, res) => {
  try {
    const { firstName, lastName, email, role, sport, position, isActive } = req.body;
    
    // Build update object
    const updateFields = {};
    
    if (firstName) updateFields.firstName = firstName;
    if (lastName) updateFields.lastName = lastName;
    if (email) updateFields.email = email;
    if (role) updateFields.role = role;
    if (sport) updateFields.sport = sport;
    if (position) updateFields.position = position;
    if (isActive !== undefined) updateFields.isActive = isActive;
    
    // Update user
    const user = await User.findByIdAndUpdate(
      req.params.id,
      { $set: updateFields },
      { new: true }
    ).select('-password');
    
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    return res.status(200).json(user);
  } catch (error) {
    console.error('Error updating user:', error);
    return res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Delete user
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response
 */
exports.deleteUser = async (req, res) => {
  try {
    // Find user
    const user = await User.findById(req.params.id);
    
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    // Delete user
    await user.remove();
    
    // Delete associated data
    await AthleteProfile.deleteMany({ user: req.params.id });
    await AIAnalysis.deleteMany({ user: req.params.id });
    
    return res.status(200).json({ message: 'User deleted successfully' });
  } catch (error) {
    console.error('Error deleting user:', error);
    return res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get user profile
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response
 */
exports.getUserProfile = async (req, res) => {
  try {
    const user = await User.findById(req.params.id).select('-password');
    
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    let profile = null;
    
    if (user.role === 'athlete') {
      profile = await AthleteProfile.findOne({ user: req.params.id });
    }
    
    return res.status(200).json({
      user,
      profile,
    });
  } catch (error) {
    console.error('Error getting user profile:', error);
    return res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Update user profile
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response
 */
exports.updateUserProfile = async (req, res) => {
  try {
    const user = await User.findById(req.params.id);
    
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    if (user.role === 'athlete') {
      let profile = await AthleteProfile.findOne({ user: req.params.id });
      
      if (!profile) {
        profile = new AthleteProfile({
          user: req.params.id,
        });
      }
      
      // Update profile fields
      const {
        dateOfBirth,
        nationality,
        height,
        weight,
        preferredFoot,
        jerseyNumber,
        specialties,
        attributes,
      } = req.body;
      
      if (dateOfBirth) profile.dateOfBirth = dateOfBirth;
      if (nationality) profile.nationality = nationality;
      if (height) profile.height = height;
      if (weight) profile.weight = weight;
      if (preferredFoot) profile.preferredFoot = preferredFoot;
      if (jerseyNumber) profile.jerseyNumber = jerseyNumber;
      if (specialties) profile.specialties = specialties;
      if (attributes) profile.attributes = attributes;
      
      await profile.save();
      
      return res.status(200).json(profile);
    }
    
    return res.status(400).json({ message: 'Profile update not supported for this user role' });
  } catch (error) {
    console.error('Error updating user profile:', error);
    return res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get user connections
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response
 */
exports.getUserConnections = async (req, res) => {
  // Implementation for getting user connections
  res.status(200).json({ message: 'Get user connections endpoint' });
};

/**
 * Send connection request
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response
 */
exports.sendConnectionRequest = async (req, res) => {
  // Implementation for sending connection request
  res.status(200).json({ message: 'Send connection request endpoint' });
};

/**
 * Respond to connection request
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response
 */
exports.respondToConnectionRequest = async (req, res) => {
  // Implementation for responding to connection request
  res.status(200).json({ message: 'Respond to connection request endpoint' });
};

/**
 * Remove connection
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response
 */
exports.removeConnection = async (req, res) => {
  // Implementation for removing connection
  res.status(200).json({ message: 'Remove connection endpoint' });
};

/**
 * Get user opportunities
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response
 */
exports.getUserOpportunities = async (req, res) => {
  try {
    const opportunities = await Opportunity.find({ creator: req.params.id });
    
    return res.status(200).json(opportunities);
  } catch (error) {
    console.error('Error getting user opportunities:', error);
    return res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get user applications
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response
 */
exports.getUserApplications = async (req, res) => {
  try {
    const opportunities = await Opportunity.find({
      'applicants.user': req.params.id,
    });
    
    const applications = opportunities.map(opportunity => ({
      opportunity: {
        id: opportunity._id,
        title: opportunity.title,
        organization: opportunity.organization,
        type: opportunity.type,
        sport: opportunity.sport,
        location: opportunity.location,
        dates: opportunity.dates,
      },
      status: opportunity.applicants.find(
        applicant => applicant.user.toString() === req.params.id
      ).status,
      appliedAt: opportunity.applicants.find(
        applicant => applicant.user.toString() === req.params.id
      ).appliedAt,
    }));
    
    return res.status(200).json(applications);
  } catch (error) {
    console.error('Error getting user applications:', error);
    return res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get user AI analyses
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response
 */
exports.getUserAnalyses = async (req, res) => {
  try {
    const analyses = await AIAnalysis.find({ user: req.params.id });
    
    return res.status(200).json(analyses);
  } catch (error) {
    console.error('Error getting user analyses:', error);
    return res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Change user password
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response
 */
exports.changePassword = async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;
    
    // Find user
    const user = await User.findById(req.params.id).select('+password');
    
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    // Check current password
    const isMatch = await bcrypt.compare(currentPassword, user.password);
    
    if (!isMatch) {
      return res.status(400).json({ message: 'Current password is incorrect' });
    }
    
    // Update password
    user.password = newPassword;
    await user.save();
    
    return res.status(200).json({ message: 'Password updated successfully' });
  } catch (error) {
    console.error('Error changing password:', error);
    return res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Verify user email
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response
 */
exports.verifyEmail = async (req, res) => {
  try {
    const { token } = req.body;
    
    // Verify token
    const decoded = jwt.verify(token, config.jwt.secret);
    
    // Find user
    const user = await User.findById(decoded.user.id);
    
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    // Update user
    user.isVerified = true;
    await user.save();
    
    return res.status(200).json({ message: 'Email verified successfully' });
  } catch (error) {
    console.error('Error verifying email:', error);
    return res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Forgot password
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response
 */
exports.forgotPassword = async (req, res) => {
  // Implementation for forgot password
  res.status(200).json({ message: 'Forgot password endpoint' });
};

/**
 * Reset password
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response
 */
exports.resetPassword = async (req, res) => {
  // Implementation for reset password
  res.status(200).json({ message: 'Reset password endpoint' });
};
