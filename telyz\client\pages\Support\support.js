// Support page JavaScript functionality
document.addEventListener('DOMContentLoaded', function() {
    
    // ================ FILTER FUNCTIONALITY ================ //
    const filterTabs = document.querySelectorAll('.filter-tab');
    const faqItems = document.querySelectorAll('.faq-item');

    filterTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Remove active class from all tabs
            filterTabs.forEach(t => t.classList.remove('active'));
            // Add active class to clicked tab
            this.classList.add('active');

            const filterValue = this.getAttribute('data-filter');
            
            // Filter FAQ items
            faqItems.forEach(item => {
                const category = item.getAttribute('data-category');
                
                if (filterValue === 'all' || category === filterValue) {
                    item.style.display = 'block';
                    item.style.animation = 'slideIn 0.3s ease';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    });

    // ================ FAQ TOGGLE FUNCTIONALITY ================ //
    window.toggleFAQ = function(questionElement) {
        const faqItem = questionElement.parentElement;
        const isActive = faqItem.classList.contains('active');
        
        // Close all FAQ items
        faqItems.forEach(item => {
            item.classList.remove('active');
        });
        
        // Open clicked item if it wasn't active
        if (!isActive) {
            faqItem.classList.add('active');
        }
    };

    // ================ QUICK ACTION FUNCTIONS ================ //
    window.startLiveChat = function() {
        // Simulate live chat opening
        showNotification('Starting live chat...', 'info');
        setTimeout(() => {
            showNotification('Connected to support agent! 💬', 'success');
        }, 2000);
    };

    window.openTutorials = function() {
        showNotification('Opening video tutorials...', 'info');
        // In real implementation, this would open a tutorial modal or redirect
    };

    window.openUserGuide = function() {
        showNotification('Opening user guide...', 'info');
        // In real implementation, this would open documentation
    };

    window.reportIssue = function() {
        showNotification('Opening issue reporting form...', 'info');
        // In real implementation, this would open an issue reporting modal
    };

    // ================ CONTACT FUNCTIONS ================ //
    window.sendEmail = function() {
        const subject = 'Support Request - Telyz Platform';
        const body = 'Hello Telyz Support Team,\n\nI need assistance with...\n\nThank you!';
        const mailtoLink = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
        window.location.href = mailtoLink;
    };

    window.callSupport = function() {
        if (confirm('Call +****************?\n\nOur support hours are Monday to Friday, 9 AM - 6 PM')) {
            window.location.href = 'tel:+15551234567';
        }
    };

    window.contactSupport = function() {
        startLiveChat();
    };

    // ================ UTILITY FUNCTIONS ================ //
    window.resetPassword = function() {
        showNotification('Redirecting to password reset...', 'info');
        // In real implementation, this would redirect to password reset page
        setTimeout(() => {
            window.location.href = '../Login/login.html';
        }, 1500);
    };

    window.clearBrowserCache = function() {
        showNotification('Instructions:', 'info');
        setTimeout(() => {
            alert(`To clear your browser cache:

Chrome:
• Ctrl+Shift+Delete (Windows) or Cmd+Shift+Delete (Mac)
• Select "Cached images and files"
• Click "Clear data"

Firefox:
• Ctrl+Shift+Delete (Windows) or Cmd+Shift+Delete (Mac)
• Select "Cache"
• Click "Clear Now"

Safari:
• Cmd+Option+E (Mac)
• Or go to Develop > Empty Caches`);
        }, 500);
    };

    window.openPrivacySettings = function() {
        showNotification('Opening privacy settings...', 'info');
        // In real implementation, this would open privacy settings page
    };

    window.upgradeToPremium = function() {
        showNotification('Redirecting to premium upgrade...', 'info');
        // In real implementation, this would open billing/upgrade page
    };

    // ================ NOTIFICATION SYSTEM ================ //
    function showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close" onclick="closeNotification(this)">
                <i class="fas fa-times"></i>
            </button>
        `;

        // Add notification to page
        document.body.appendChild(notification);

        // Position notification
        positionNotification(notification);

        // Show notification with animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        // Auto-hide after 5 seconds
        setTimeout(() => {
            closeNotification(notification.querySelector('.notification-close'));
        }, 5000);
    }

    function getNotificationIcon(type) {
        const icons = {
            'success': 'check-circle',
            'error': 'exclamation-circle',
            'warning': 'exclamation-triangle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    function positionNotification(notification) {
        const existingNotifications = document.querySelectorAll('.notification');
        const topOffset = 20 + (existingNotifications.length - 1) * 80;
        notification.style.top = `${topOffset}px`;
        notification.style.right = '20px';
    }

    window.closeNotification = function(closeButton) {
        const notification = closeButton.parentElement;
        notification.classList.remove('show');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
            repositionNotifications();
        }, 300);
    };

    function repositionNotifications() {
        const notifications = document.querySelectorAll('.notification');
        notifications.forEach((notification, index) => {
            notification.style.top = `${20 + index * 80}px`;
        });
    }

    // ================ SEARCH FUNCTIONALITY ================ //
    const searchInput = document.createElement('input');
    searchInput.type = 'text';
    searchInput.placeholder = 'Search FAQ...';
    searchInput.className = 'faq-search';
    searchInput.style.cssText = `
        width: 100%;
        padding: 12px 16px;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        font-size: 14px;
        margin-bottom: 20px;
        transition: border-color 0.3s ease;
    `;

    // Add search input before FAQ container
    const faqSection = document.querySelector('.faq-section');
    const faqContainer = document.querySelector('.faq-container');
    faqSection.insertBefore(searchInput, faqContainer);

    // Search functionality
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        
        faqItems.forEach(item => {
            const question = item.querySelector('.faq-question h4').textContent.toLowerCase();
            const answer = item.querySelector('.faq-answer').textContent.toLowerCase();
            
            if (question.includes(searchTerm) || answer.includes(searchTerm)) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    });

    searchInput.addEventListener('focus', function() {
        this.style.borderColor = '#6a0dad';
        this.style.boxShadow = '0 0 0 3px rgba(106, 13, 173, 0.1)';
    });

    searchInput.addEventListener('blur', function() {
        this.style.borderColor = '#e9ecef';
        this.style.boxShadow = 'none';
    });

});

// ================ NOTIFICATION STYLES ================ //
const notificationStyles = `
<style>
.notification {
    position: fixed;
    right: -400px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-left: 4px solid #6a0dad;
    z-index: 10000;
    max-width: 350px;
    transition: all 0.3s ease;
    overflow: hidden;
}

.notification.show {
    right: 20px;
}

.notification-success {
    border-left-color: #27ae60;
}

.notification-error {
    border-left-color: #e74c3c;
}

.notification-warning {
    border-left-color: #f39c12;
}

.notification-info {
    border-left-color: #3498db;
}

.notification-content {
    padding: 16px 20px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.notification-content i {
    font-size: 18px;
    color: #6a0dad;
}

.notification-success .notification-content i {
    color: #27ae60;
}

.notification-error .notification-content i {
    color: #e74c3c;
}

.notification-warning .notification-content i {
    color: #f39c12;
}

.notification-info .notification-content i {
    color: #3498db;
}

.notification-content span {
    flex: 1;
    color: #2c3e50;
    font-weight: 500;
}

.notification-close {
    position: absolute;
    top: 8px;
    right: 8px;
    background: none;
    border: none;
    color: #95a5a6;
    cursor: pointer;
    padding: 4px;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.notification-close:hover {
    background: #ecf0f1;
    color: #7f8c8d;
}

.faq-search:focus {
    outline: none;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}
</style>
`;

// Add notification styles to head
document.head.insertAdjacentHTML('beforeend', notificationStyles);