/**
 * Main Application
 * 
 * Express application setup and configuration.
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const compression = require('compression');
const path = require('path');
const config = require('./config');
const database = require('./config/database');

// Create Express application
const app = express();

// Connect to database
database.connect();
database.setupConnectionHandlers();

// Middleware
app.use(cors(config.server.cors));
app.use(helmet());
app.use(morgan(config.logging.format));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(compression());

// Rate limiting
app.use(rateLimit(config.server.rateLimit));

// Static files
app.use(express.static(path.join(__dirname, '../client')));
app.use('/storage', express.static(path.join(__dirname, '../', config.storage.local.directory)));

// API routes
app.use('/api/auth', require('./api/routes/auth'));
app.use('/api/users', require('./api/routes/users'));
app.use('/api/opportunities', require('./api/routes/opportunities'));
app.use('/api/ai-analysis', require('./api/routes/ai-analysis'));
app.use('/api/llm', require('../ai-analysis/llm/api'));

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  
  const statusCode = err.statusCode || 500;
  const message = err.message || 'Internal Server Error';
  
  res.status(statusCode).json({
    message,
    stack: config.isDevelopment ? err.stack : undefined,
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({ message: 'Not Found' });
});

// Start server
const PORT = config.server.port;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});

// Export app for testing
module.exports = app;
