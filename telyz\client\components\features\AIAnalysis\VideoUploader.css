/* Video Uploader Component Styles */

.video-uploader {
  display: flex;
  flex-direction: row;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  max-width: 1200px;
  margin: 0 auto;
}

.upload-container {
  flex: 2;
  padding: 30px;
  border-right: 1px solid #eaeaea;
}

.upload-drop-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 20px;
}

.upload-drop-area:hover, .upload-drop-area.drag-over {
  border-color: #00bcd4;
  background-color: rgba(0, 188, 212, 0.05);
}

.upload-drop-area i {
  font-size: 48px;
  color: #00bcd4;
  margin-bottom: 15px;
}

.upload-drop-area p {
  margin: 5px 0;
  color: #666;
}

.upload-browse-button {
  background-color: #00bcd4;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  margin-top: 15px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s;
}

.upload-browse-button:hover {
  background-color: #00a0b7;
}

.upload-file-info {
  display: flex;
  align-items: center;
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.file-preview {
  width: 60px;
  height: 60px;
  background-color: #e0e0e0;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.file-preview i {
  font-size: 24px;
  color: #666;
}

.file-details {
  flex: 1;
}

.file-details h4 {
  margin: 0 0 5px;
  font-size: 16px;
  color: #333;
}

.file-details p {
  margin: 2px 0;
  font-size: 14px;
  color: #666;
}

.file-remove-button {
  background: none;
  border: none;
  color: #ff5252;
  cursor: pointer;
  font-size: 18px;
  padding: 5px;
}

.upload-form {
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
}

.form-group input, .form-group textarea, .form-group select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-group textarea {
  min-height: 80px;
  resize: vertical;
}

.upload-actions {
  display: flex;
  justify-content: flex-end;
}

.upload-button {
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 12px 24px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
}

.upload-button:hover {
  background-color: #43a047;
}

.upload-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.upload-progress {
  margin-top: 20px;
}

.upload-progress-bar {
  height: 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 5px;
}

.upload-progress-fill {
  height: 100%;
  background-color: #4CAF50;
  transition: width 0.3s ease;
}

.upload-progress-text {
  text-align: right;
  font-size: 14px;
  color: #666;
}

.upload-info {
  flex: 1;
  padding: 30px;
  background-color: #f9f9f9;
}

.upload-info h3 {
  margin: 0 0 15px;
  color: #333;
  font-size: 18px;
}

.upload-info p {
  margin: 0 0 20px;
  color: #666;
}

.upload-info ul {
  padding-left: 0;
  list-style-type: none;
}

.upload-info ul li {
  margin-bottom: 8px;
  position: relative;
  padding-left: 20px;
}

.upload-info ul li:before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #4CAF50;
}

.ai-coach-link {
  margin-top: 30px;
  padding: 20px;
  background-color: #f0f8ff;
  border-radius: 8px;
  border-left: 4px solid #0084ff;
}

.ai-coach-link h3 {
  margin-top: 0;
  color: #0084ff;
}

.coach-chat-button {
  display: inline-flex;
  align-items: center;
  background-color: #0084ff;
  color: white;
  padding: 10px 15px;
  border-radius: 20px;
  text-decoration: none;
  font-weight: 500;
  margin-top: 10px;
  transition: background-color 0.2s;
}

.coach-chat-button:hover {
  background-color: #0073e6;
}

.coach-chat-button i {
  margin-right: 8px;
  font-size: 16px;
}

@media (max-width: 768px) {
  .video-uploader {
    flex-direction: column;
  }
  
  .upload-container {
    border-right: none;
    border-bottom: 1px solid #eaeaea;
  }
}