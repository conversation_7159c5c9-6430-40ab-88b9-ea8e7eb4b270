// Settings page functionality
document.addEventListener('DOMContentLoaded', function() {
    
    // Elements
    const navTabs = document.querySelectorAll('.nav-tab');
    const settingSections = document.querySelectorAll('.settings-section');
    const saveAllBtn = document.getElementById('saveAllBtn');
    const resetAllBtn = document.getElementById('resetAllBtn');
    const profilePicInput = document.getElementById('profilePicInput');
    const currentProfilePic = document.getElementById('currentProfilePic');
    const bioTextarea = document.getElementById('bio');
    const bioCounter = document.getElementById('bioCounter');
    const topBtn = document.querySelector('.top-btn');
    const helpBtn = document.querySelector('.help-btn');
    const toggleSwitches = document.querySelectorAll('.toggle-switch input');
    const themeOptions = document.querySelectorAll('input[name="theme"]');
    
    // Tab Navigation
    navTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const targetSection = this.getAttribute('data-section');
            
            // Remove active class from all tabs and sections
            navTabs.forEach(t => t.classList.remove('active'));
            settingSections.forEach(s => s.classList.remove('active'));
            
            // Add active class to clicked tab and corresponding section
            this.classList.add('active');
            document.getElementById(targetSection + '-section').classList.add('active');
            
            // Scroll to top of content
            document.querySelector('.settings-content').scrollTop = 0;
            
            // Update URL hash
            window.history.replaceState(null, null, `#${targetSection}`);
        });
    });
    
    // Handle URL hash on page load
    const hash = window.location.hash.substring(1);
    if (hash) {
        const targetTab = document.querySelector(`[data-section="${hash}"]`);
        if (targetTab) {
            targetTab.click();
        }
    }
    
    // Profile Picture Upload
    if (profilePicInput) {
        profilePicInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    currentProfilePic.src = e.target.result;
                    showNotification('Profile Picture Updated', 'Your profile picture has been changed successfully');
                };
                reader.readAsDataURL(file);
            }
        });
    }
    
    // Bio Character Counter
    if (bioTextarea && bioCounter) {
        bioTextarea.addEventListener('input', function() {
            const length = this.value.length;
            bioCounter.textContent = length;
            
            if (length > 500) {
                bioCounter.style.color = '#dc3545';
                this.style.borderColor = '#dc3545';
            } else {
                bioCounter.style.color = '#666';
                this.style.borderColor = '#e1e5e9';
            }
        });
        
        // Initial count
        bioCounter.textContent = bioTextarea.value.length;
    }
    
    // Toggle Switches
    toggleSwitches.forEach(toggle => {
        toggle.addEventListener('change', function() {
            const label = this.closest('.toggle-item').querySelector('.toggle-info label').textContent;
            const status = this.checked ? 'enabled' : 'disabled';
            showNotification(`${label} ${status}`, `This setting has been ${status}`);
        });
    });
    
    // Theme Selection
    themeOptions.forEach(option => {
        option.addEventListener('change', function() {
            const theme = this.value;
            applyTheme(theme);
            showNotification('Theme Changed', `${theme.charAt(0).toUpperCase() + theme.slice(1)} theme applied`);
        });
    });
    
    // Save All Settings
    saveAllBtn.addEventListener('click', function() {
        const button = this;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
        button.disabled = true;
        
        // Simulate save operation
        setTimeout(() => {
            button.innerHTML = '<i class="fas fa-check"></i> Saved!';
            showNotification('Settings Saved', 'All your settings have been saved successfully');
            
            setTimeout(() => {
                button.innerHTML = '<i class="fas fa-save"></i> Save Changes';
                button.disabled = false;
            }, 2000);
        }, 1500);
    });
    
    // Reset All Settings
    resetAllBtn.addEventListener('click', function() {
        showConfirmDialog(
            'Reset All Settings',
            'Are you sure you want to reset all settings to their default values? This action cannot be undone.',
            () => {
                resetAllSettings();
                showNotification('Settings Reset', 'All settings have been reset to default values');
            }
        );
    });
    
    // Connected Accounts
    const connectBtns = document.querySelectorAll('.btn-connect');
    const disconnectBtns = document.querySelectorAll('.btn-disconnect');
    
    connectBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const accountName = this.closest('.account-item').querySelector('label').textContent;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            
            setTimeout(() => {
                this.textContent = 'Disconnect';
                this.classList.remove('btn-connect');
                this.classList.add('btn-disconnect');
                
                const accountInfo = this.closest('.account-item').querySelector('.account-info span');
                accountInfo.textContent = 'Connected';
                
                showNotification(`${accountName} Connected`, `Your ${accountName} account has been linked successfully`);
            }, 1500);
        });
    });
    
    disconnectBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const accountName = this.closest('.account-item').querySelector('label').textContent;
            showConfirmDialog(
                `Disconnect ${accountName}`,
                `Are you sure you want to disconnect your ${accountName} account?`,
                () => {
                    this.textContent = 'Connect';
                    this.classList.remove('btn-disconnect');
                    this.classList.add('btn-connect');
                    
                    const accountInfo = this.closest('.account-item').querySelector('.account-info span');
                    accountInfo.textContent = 'Not connected';
                    
                    showNotification(`${accountName} Disconnected`, `Your ${accountName} account has been disconnected`);
                }
            );
        });
    });
    
    // Export Data
    const exportBtns = document.querySelectorAll('.export-item .btn-outline');
    exportBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const dataType = this.closest('.export-item').querySelector('h4').textContent;
            const originalText = this.innerHTML;
            
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Preparing...';
            this.disabled = true;
            
            setTimeout(() => {
                this.innerHTML = '<i class="fas fa-download"></i> Download';
                
                setTimeout(() => {
                    // Simulate download
                    const link = document.createElement('a');
                    link.href = '#';
                    link.download = `telyz-${dataType.toLowerCase().replace(' ', '-')}.zip`;
                    link.click();
                    
                    this.innerHTML = originalText;
                    this.disabled = false;
                    
                    showNotification('Export Complete', `Your ${dataType} has been exported successfully`);
                }, 1000);
            }, 2000);
        });
    });
    
    // Danger Zone Actions
    const dangerBtns = document.querySelectorAll('.btn-danger');
    dangerBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.textContent.trim();
            const messages = {
                'Clear Data': {
                    title: 'Clear All Data',
                    message: 'This will permanently delete all your uploaded files, analysis data, and media. This action cannot be undone.',
                    type: 'danger'
                },
                'Deactivate': {
                    title: 'Deactivate Account',
                    message: 'Your account will be temporarily disabled. You can reactivate it by logging in again.',
                    type: 'warning'
                },
                'Delete Account': {
                    title: 'Delete Account',
                    message: 'This will permanently delete your account and all associated data. This action cannot be undone.',
                    type: 'danger'
                }
            };
            
            const config = messages[action];
            if (config) {
                showConfirmDialog(
                    config.title,
                    config.message,
                    () => {
                        if (action === 'Delete Account') {
                            // Redirect to account deletion process
                            window.location.href = '/account-deletion';
                        } else {
                            showNotification(config.title, `${config.title} process initiated`);
                        }
                    },
                    config.type
                );
            }
        });
    });
    
    // Help Button
    helpBtn.addEventListener('click', function() {
        showHelpModal();
    });
    
    // Back to Top Button
    topBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
    
    // Show/hide back to top button
    window.addEventListener('scroll', function() {
        if (window.scrollY > 300) {
            topBtn.classList.add('visible');
        } else {
            topBtn.classList.remove('visible');
        }
    });
    
    // Submenu functionality - using hover for consistency with template
    const aiMenuItem = document.getElementById('aiMenuItem');
    const sportsMenuItem = document.getElementById('sportsMenuItem');
    
    // AI submenu hover functionality
    if (aiMenuItem) {
        const aiSubmenu = document.getElementById('aiDropdownMenu');
        aiMenuItem.addEventListener('mouseenter', function() {
            aiSubmenu.style.display = 'block';
            aiSubmenu.style.maxHeight = '400px';
        });
        
        aiMenuItem.addEventListener('mouseleave', function() {
            setTimeout(() => {
                if (!aiSubmenu.matches(':hover')) {
                    aiSubmenu.style.display = 'none';
                    aiSubmenu.style.maxHeight = '0';
                }
            }, 100);
        });
        
        aiSubmenu.addEventListener('mouseleave', function() {
            aiSubmenu.style.display = 'none';
            aiSubmenu.style.maxHeight = '0';
        });
    }
    
    // Sports submenu hover functionality
    if (sportsMenuItem) {
        const sportsSubmenu = document.getElementById('sportsSubmenu');
        sportsMenuItem.addEventListener('mouseenter', function() {
            sportsSubmenu.style.display = 'block';
            sportsSubmenu.style.maxHeight = '400px';
        });
        
        sportsMenuItem.addEventListener('mouseleave', function() {
            setTimeout(() => {
                if (!sportsSubmenu.matches(':hover')) {
                    sportsSubmenu.style.display = 'none';
                    sportsSubmenu.style.maxHeight = '0';
                }
            }, 100);
        });
        
        sportsSubmenu.addEventListener('mouseleave', function() {
            sportsSubmenu.style.display = 'none';
            sportsSubmenu.style.maxHeight = '0';
        });
    }
    
    // Form validation
    const forms = document.querySelectorAll('input, select, textarea');
    forms.forEach(form => {
        form.addEventListener('blur', function() {
            validateField(this);
        });
    });
    
    // Auto-save functionality
    let autoSaveTimeout;
    forms.forEach(form => {
        form.addEventListener('input', function() {
            clearTimeout(autoSaveTimeout);
            autoSaveTimeout = setTimeout(() => {
                autoSave();
            }, 2000);
        });
    });
    
    // Functions
    function applyTheme(theme) {
        const body = document.body;
        body.classList.remove('light-theme', 'dark-theme');
        
        if (theme === 'dark') {
            body.classList.add('dark-theme');
        } else if (theme === 'light') {
            body.classList.add('light-theme');
        } else {
            // Auto theme - detect system preference
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            body.classList.add(prefersDark ? 'dark-theme' : 'light-theme');
        }
        
        localStorage.setItem('theme', theme);
    }
    
    function resetAllSettings() {
        // Reset form values
        document.getElementById('firstName').value = 'John';
        document.getElementById('lastName').value = 'Doe';
        document.getElementById('email').value = '<EMAIL>';
        document.getElementById('phone').value = '+****************';
        document.getElementById('primarySport').value = 'football';
        document.getElementById('position').value = 'Midfielder';
        document.getElementById('skillLevel').value = 'semi-professional';
        document.getElementById('team').value = 'Barcelona Youth FC';
        document.getElementById('height').value = '178';
        document.getElementById('weight').value = '75';
        bioTextarea.value = 'Passionate footballer with 8+ years of experience. Currently playing as a midfielder for Barcelona Youth FC. Aspiring to play professionally and represent my country.';
        
        // Reset toggles
        toggleSwitches.forEach(toggle => {
            if (toggle.id === 'publicProfile' || toggle.id === 'showStats' || 
                toggle.id === 'analytics' || toggle.id === 'recommendations' ||
                toggle.id === 'emailMessages' || toggle.id === 'emailOpportunities' ||
                toggle.id === 'emailSummary' || toggle.id === 'pushMessages' ||
                toggle.id === 'pushScouts' || toggle.id === 'pushEvents' ||
                toggle.id === 'beginnerContent' || toggle.id === 'proOpportunities' ||
                toggle.id === 'internationalContent' || toggle.id === 'animations') {
                toggle.checked = true;
            } else {
                toggle.checked = false;
            }
        });
        
        // Reset theme
        document.getElementById('themeLight').checked = true;
        applyTheme('light');
        
        // Update bio counter
        if (bioCounter) {
            bioCounter.textContent = bioTextarea.value.length;
        }
    }
    
    function validateField(field) {
        const value = field.value.trim();
        let isValid = true;
        let message = '';
        
        // Email validation
        if (field.type === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                isValid = false;
                message = 'Please enter a valid email address';
            }
        }
        
        // Phone validation
        if (field.type === 'tel' && value) {
            const phoneRegex = /^[\+]?[0-9\s\-\(\)]+$/;
            if (!phoneRegex.test(value)) {
                isValid = false;
                message = 'Please enter a valid phone number';
            }
        }
        
        // Required fields
        if (field.hasAttribute('required') && !value) {
            isValid = false;
            message = 'This field is required';
        }
        
        // Update field styling
        if (isValid) {
            field.style.borderColor = '#e1e5e9';
            removeFieldError(field);
        } else {
            field.style.borderColor = '#dc3545';
            showFieldError(field, message);
        }
        
        return isValid;
    }
    
    function showFieldError(field, message) {
        removeFieldError(field);
        
        const error = document.createElement('div');
        error.className = 'field-error';
        error.textContent = message;
        error.style.cssText = `
            color: #dc3545;
            font-size: 12px;
            margin-top: 4px;
        `;
        
        field.parentNode.appendChild(error);
    }
    
    function removeFieldError(field) {
        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }
    }
    
    function autoSave() {
        const saveIndicator = document.createElement('div');
        saveIndicator.textContent = 'Auto-saving...';
        saveIndicator.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            z-index: 10000;
            animation: slideInRight 0.3s ease-out;
        `;
        
        document.body.appendChild(saveIndicator);
        
        setTimeout(() => {
            saveIndicator.textContent = 'Saved';
            setTimeout(() => {
                saveIndicator.remove();
            }, 1000);
        }, 1000);
    }
    
    function showNotification(title, message, type = 'success') {
        const notification = document.createElement('div');
        notification.className = 'notification';
        
        const iconMap = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };
        
        const colorMap = {
            success: '#28a745',
            error: '#dc3545',
            warning: '#ffc107',
            info: '#17a2b8'
        };
        
        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-icon">
                    <i class="${iconMap[type]}" style="color: ${colorMap[type]}"></i>
                </div>
                <div class="notification-text">
                    <strong>${title}</strong>
                    <p>${message}</p>
                </div>
                <button class="notification-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 9999;
            max-width: 350px;
            animation: slideInRight 0.3s ease-out;
        `;
        
        const content = notification.querySelector('.notification-content');
        content.style.cssText = `
            display: flex;
            align-items: center;
            padding: 15px;
            gap: 12px;
        `;
        
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.style.cssText = `
            background: none;
            border: none;
            color: #666;
            cursor: pointer;
            font-size: 14px;
            padding: 4px;
        `;
        
        document.body.appendChild(notification);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.animation = 'slideOutRight 0.3s ease-in';
                setTimeout(() => notification.remove(), 300);
            }
        }, 5000);
        
        // Close button
        closeBtn.onclick = () => {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => notification.remove(), 300);
        };
    }
    
    function showConfirmDialog(title, message, onConfirm, type = 'warning') {
        const modal = document.createElement('div');
        modal.className = 'confirm-modal';
        
        const iconMap = {
            warning: 'fas fa-exclamation-triangle',
            danger: 'fas fa-exclamation-circle',
            info: 'fas fa-info-circle'
        };
        
        const colorMap = {
            warning: '#ffc107',
            danger: '#dc3545',
            info: '#17a2b8'
        };
        
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <div class="modal-icon" style="color: ${colorMap[type]}">
                        <i class="${iconMap[type]}"></i>
                    </div>
                    <h3>${title}</h3>
                </div>
                <div class="modal-body">
                    <p>${message}</p>
                </div>
                <div class="modal-footer">
                    <button class="btn-secondary cancel-btn">Cancel</button>
                    <button class="btn-danger confirm-btn">Confirm</button>
                </div>
            </div>
        `;
        
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        `;
        
        const modalContent = modal.querySelector('.modal-content');
        modalContent.style.cssText = `
            background: white;
            border-radius: 12px;
            padding: 0;
            max-width: 500px;
            width: 90%;
            animation: modalSlideIn 0.3s ease-out;
        `;
        
        const modalHeader = modal.querySelector('.modal-header');
        modalHeader.style.cssText = `
            padding: 24px 24px 16px;
            display: flex;
            align-items: center;
            gap: 16px;
        `;
        
        const modalIcon = modal.querySelector('.modal-icon');
        modalIcon.style.cssText = `
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        `;
        
        const modalBody = modal.querySelector('.modal-body');
        modalBody.style.cssText = `
            padding: 0 24px;
        `;
        
        const modalFooter = modal.querySelector('.modal-footer');
        modalFooter.style.cssText = `
            padding: 16px 24px 24px;
            display: flex;
            gap: 12px;
            justify-content: flex-end;
        `;
        
        // Event listeners
        modal.querySelector('.cancel-btn').onclick = () => modal.remove();
        modal.querySelector('.confirm-btn').onclick = () => {
            onConfirm();
            modal.remove();
        };
        
        modal.onclick = (e) => {
            if (e.target === modal) modal.remove();
        };
        
        document.body.appendChild(modal);
    }
    
    function showHelpModal() {
        const modal = document.createElement('div');
        modal.className = 'help-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-question-circle"></i> Help & Support</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="help-section">
                        <h4>Frequently Asked Questions</h4>
                        <div class="faq-item">
                            <strong>How do I change my profile picture?</strong>
                            <p>Go to Profile Settings and click on your current picture. You can upload a new image or remove the existing one.</p>
                        </div>
                        <div class="faq-item">
                            <strong>Can I export my data?</strong>
                            <p>Yes! Go to Data & Storage section where you can export your profile data, media files, and messages.</p>
                        </div>
                        <div class="faq-item">
                            <strong>How do I delete my account?</strong>
                            <p>In the Data & Storage section, scroll down to the Danger Zone. Please note that account deletion is permanent.</p>
                        </div>
                    </div>
                    
                    <div class="help-section">
                        <h4>Contact Support</h4>
                        <p>Need more help? Our support team is here for you!</p>
                        <div class="contact-options">
                            <button class="contact-option">
                                <i class="fas fa-envelope"></i>
                                <div>
                                    <strong>Email Support</strong>
                                    <span><EMAIL></span>
                                </div>
                            </button>
                            <button class="contact-option">
                                <i class="fas fa-comment"></i>
                                <div>
                                    <strong>Live Chat</strong>
                                    <span>Available 24/7</span>
                                </div>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Styling
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        `;
        
        // Event listeners
        modal.querySelector('.modal-close').onclick = () => modal.remove();
        modal.onclick = (e) => {
            if (e.target === modal) modal.remove();
        };
        
        document.body.appendChild(modal);
    }
    
    // Load saved theme on page load
    const savedTheme = localStorage.getItem('theme') || 'light';
    const themeInput = document.getElementById('theme' + savedTheme.charAt(0).toUpperCase() + savedTheme.slice(1));
    if (themeInput) {
        themeInput.checked = true;
        applyTheme(savedTheme);
    }
    
    // Add required CSS animations
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        @keyframes slideOutRight {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
        
        @keyframes modalSlideIn {
            from { transform: scale(0.9) translateY(-20px); opacity: 0; }
            to { transform: scale(1) translateY(0); opacity: 1; }
        }
        
        .help-modal .modal-content { max-width: 600px; }
        .help-section { margin-bottom: 24px; }
        .help-section h4 { color: #6a0dad; margin-bottom: 16px; }
        .faq-item { margin-bottom: 16px; padding: 16px; background: #f8f9fa; border-radius: 8px; }
        .faq-item strong { display: block; margin-bottom: 8px; color: #333; }
        .faq-item p { color: #666; margin: 0; }
        .contact-options { display: flex; flex-direction: column; gap: 12px; }
        .contact-option { 
            display: flex; align-items: center; gap: 16px; padding: 16px; 
            background: #f8f9fa; border: none; border-radius: 8px; cursor: pointer; 
            transition: background 0.3s; text-align: left; width: 100%;
        }
        .contact-option:hover { background: #e9ecef; }
        .contact-option i { font-size: 24px; color: #6a0dad; }
        .contact-option div { flex: 1; }
        .contact-option strong { display: block; margin-bottom: 4px; }
        .contact-option span { color: #666; font-size: 14px; }
    `;
    document.head.appendChild(style);
});