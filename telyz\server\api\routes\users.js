/**
 * User Routes
 * 
 * API routes for user management.
 */

const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');
const authMiddleware = require('../middlewares/authMiddleware');
const { validateUser } = require('../validators/userValidator');

/**
 * @route   GET /api/users
 * @desc    Get all users (admin only)
 * @access  Private/Admin
 */
router.get('/', authMiddleware.authenticate, authMiddleware.isAdmin, userController.getAllUsers);

/**
 * @route   GET /api/users/:id
 * @desc    Get user by ID
 * @access  Private
 */
router.get('/:id', authMiddleware.authenticate, userController.getUserById);

/**
 * @route   POST /api/users
 * @desc    Create a new user
 * @access  Public
 */
router.post('/', validateUser, userController.createUser);

/**
 * @route   PUT /api/users/:id
 * @desc    Update user
 * @access  Private
 */
router.put('/:id', authMiddleware.authenticate, authMiddleware.isOwnerOrAdmin, validateUser, userController.updateUser);

/**
 * @route   DELETE /api/users/:id
 * @desc    Delete user
 * @access  Private
 */
router.delete('/:id', authMiddleware.authenticate, authMiddleware.isOwnerOrAdmin, userController.deleteUser);

/**
 * @route   GET /api/users/:id/profile
 * @desc    Get user profile
 * @access  Private
 */
router.get('/:id/profile', authMiddleware.authenticate, userController.getUserProfile);

/**
 * @route   PUT /api/users/:id/profile
 * @desc    Update user profile
 * @access  Private
 */
router.put('/:id/profile', authMiddleware.authenticate, authMiddleware.isOwnerOrAdmin, userController.updateUserProfile);

/**
 * @route   GET /api/users/:id/connections
 * @desc    Get user connections
 * @access  Private
 */
router.get('/:id/connections', authMiddleware.authenticate, userController.getUserConnections);

/**
 * @route   POST /api/users/:id/connections
 * @desc    Send connection request
 * @access  Private
 */
router.post('/:id/connections', authMiddleware.authenticate, userController.sendConnectionRequest);

/**
 * @route   PUT /api/users/:id/connections/:connectionId
 * @desc    Accept/Reject connection request
 * @access  Private
 */
router.put('/:id/connections/:connectionId', authMiddleware.authenticate, authMiddleware.isOwner, userController.respondToConnectionRequest);

/**
 * @route   DELETE /api/users/:id/connections/:connectionId
 * @desc    Remove connection
 * @access  Private
 */
router.delete('/:id/connections/:connectionId', authMiddleware.authenticate, authMiddleware.isOwner, userController.removeConnection);

/**
 * @route   GET /api/users/:id/opportunities
 * @desc    Get user opportunities
 * @access  Private
 */
router.get('/:id/opportunities', authMiddleware.authenticate, userController.getUserOpportunities);

/**
 * @route   GET /api/users/:id/applications
 * @desc    Get user applications
 * @access  Private
 */
router.get('/:id/applications', authMiddleware.authenticate, authMiddleware.isOwner, userController.getUserApplications);

/**
 * @route   GET /api/users/:id/analyses
 * @desc    Get user AI analyses
 * @access  Private
 */
router.get('/:id/analyses', authMiddleware.authenticate, userController.getUserAnalyses);

/**
 * @route   PUT /api/users/:id/password
 * @desc    Change user password
 * @access  Private
 */
router.put('/:id/password', authMiddleware.authenticate, authMiddleware.isOwner, userController.changePassword);

/**
 * @route   POST /api/users/:id/verify
 * @desc    Verify user email
 * @access  Public
 */
router.post('/:id/verify', userController.verifyEmail);

/**
 * @route   POST /api/users/forgot-password
 * @desc    Forgot password
 * @access  Public
 */
router.post('/forgot-password', userController.forgotPassword);

/**
 * @route   POST /api/users/reset-password
 * @desc    Reset password
 * @access  Public
 */
router.post('/reset-password', userController.resetPassword);

module.exports = router;
