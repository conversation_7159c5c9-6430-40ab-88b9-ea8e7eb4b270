// Search functionality and interactive features
document.addEventListener('DOMContentLoaded', function() {
    
    // Elements
    const searchInput = document.getElementById('mainSearchInput');
    const advancedSearchBtn = document.getElementById('advancedSearchBtn');
    const advancedFiltersPanel = document.getElementById('advancedFiltersPanel');
    const closeFiltersBtn = document.getElementById('closeFiltersBtn');
    const clearFiltersBtn = document.getElementById('clearFiltersBtn');
    const applyFiltersBtn = document.getElementById('applyFiltersBtn');
    const categoryTabs = document.querySelectorAll('.category-tab');
    const viewBtns = document.querySelectorAll('.view-btn');
    const resultsGrid = document.getElementById('resultsGrid');
    const searchTerm = document.getElementById('searchTerm');
    const saveBtns = document.querySelectorAll('.save-btn');
    const contactBtns = document.querySelectorAll('.contact-btn');
    const profileBtns = document.querySelectorAll('.profile-btn');
    const voiceSearchBtn = document.querySelector('.voice-search-btn');
    const chatButton = document.querySelector('.chat-button');
    const topButton = document.querySelector('.top-button');
    const locationFilter = document.getElementById('locationFilter');
    const locationSuggestions = document.getElementById('locationSuggestions');

    // Search input functionality
    searchInput.addEventListener('input', function(e) {
        const value = e.target.value;
        searchTerm.textContent = value || 'all';
        
        // Simulate search delay
        clearTimeout(searchInput.searchTimeout);
        searchInput.searchTimeout = setTimeout(() => {
            performSearch(value);
        }, 300);
    });

    // Enter key search
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performSearch(e.target.value);
        }
    });

    // Advanced filters toggle
    advancedSearchBtn.addEventListener('click', function() {
        advancedFiltersPanel.classList.toggle('show');
    });

    closeFiltersBtn.addEventListener('click', function() {
        advancedFiltersPanel.classList.remove('show');
    });

    // Clear filters
    clearFiltersBtn.addEventListener('click', function() {
        // Reset all form elements
        document.getElementById('sportFilter').value = '';
        document.getElementById('locationFilter').value = '';
        document.getElementById('minAge').value = '';
        document.getElementById('maxAge').value = '';
        document.getElementById('positionFilter').value = '';
        
        // Uncheck all skill level checkboxes
        document.querySelectorAll('.skill-option input[type="checkbox"]').forEach(checkbox => {
            checkbox.checked = false;
        });
        
        // Refresh results
        performSearch(searchInput.value);
        showNotification('Filters Cleared', 'All search filters have been reset');
    });

    // Apply filters
    applyFiltersBtn.addEventListener('click', function() {
        const filters = collectFilters();
        performSearch(searchInput.value, filters);
        advancedFiltersPanel.classList.remove('show');
        showNotification('Filters Applied', 'Search results updated with your filters');
    });

    // Category tabs
    categoryTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            categoryTabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');
            
            const category = this.getAttribute('data-category');
            filterByCategory(category);
        });
    });

    // View controls
    viewBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            viewBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            const view = this.getAttribute('data-view');
            toggleView(view);
        });
    });

    // Save buttons
    saveBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const icon = this.querySelector('i');
            const isSaved = icon.classList.contains('fas');
            
            if (isSaved) {
                icon.classList.remove('fas');
                icon.classList.add('far');
                this.setAttribute('title', 'Save');
                showNotification('Removed from Saved', 'Item removed from your saved list');
            } else {
                icon.classList.remove('far');
                icon.classList.add('fas');
                this.setAttribute('title', 'Saved');
                showNotification('Saved Successfully', 'Item added to your saved list');
            }
        });
    });

    // Contact buttons
    contactBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const card = this.closest('.result-card');
            const name = card.querySelector('.profile-name').textContent;
            showContactModal(name);
        });
    });

    // Profile buttons
    profileBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const card = this.closest('.result-card');
            const name = card.querySelector('.profile-name').textContent;
            showNotification('Profile Loading', `Loading ${name}'s profile...`);
            
            // Simulate navigation delay
            setTimeout(() => {
                // In a real app, this would navigate to the profile page
                console.log(`Navigating to ${name}'s profile`);
            }, 1000);
        });
    });

    // Voice search
    voiceSearchBtn.addEventListener('click', function() {
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            startVoiceRecognition();
        } else {
            showNotification('Voice Search Unavailable', 'Your browser does not support voice search');
        }
    });

    // Location suggestions
    locationFilter.addEventListener('input', function(e) {
        const value = e.target.value;
        if (value.length > 2) {
            showLocationSuggestions(value);
        } else {
            locationSuggestions.innerHTML = '';
        }
    });

    // Chat button
    chatButton.addEventListener('click', function() {
        showNotification('Chat Support', 'Connecting you to our support team...');
    });

    // Back to top button
    topButton.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });

    // Scroll to show/hide back to top button
    window.addEventListener('scroll', function() {
        if (window.scrollY > 300) {
            topButton.style.opacity = '1';
            topButton.style.visibility = 'visible';
        } else {
            topButton.style.opacity = '0';
            topButton.style.visibility = 'hidden';
        }
    });

    // Submenu functionality - using hover instead of click to match template
    const aiMenuItem = document.getElementById('aiMenuItem');
    const sportsMenuItem = document.getElementById('sportsMenuItem');
    
    // AI submenu hover functionality
    if (aiMenuItem) {
        const aiSubmenu = document.getElementById('aiDropdownMenu');
        aiMenuItem.addEventListener('mouseenter', function() {
            aiSubmenu.style.display = 'block';
            aiSubmenu.style.maxHeight = '400px';
        });
        
        aiMenuItem.addEventListener('mouseleave', function() {
            setTimeout(() => {
                if (!aiSubmenu.matches(':hover')) {
                    aiSubmenu.style.display = 'none';
                    aiSubmenu.style.maxHeight = '0';
                }
            }, 100);
        });
        
        aiSubmenu.addEventListener('mouseleave', function() {
            aiSubmenu.style.display = 'none';
            aiSubmenu.style.maxHeight = '0';
        });
    }
    
    // Sports submenu hover functionality
    if (sportsMenuItem) {
        const sportsSubmenu = document.getElementById('sportsSubmenu');
        sportsMenuItem.addEventListener('mouseenter', function() {
            sportsSubmenu.style.display = 'block';
            sportsSubmenu.style.maxHeight = '400px';
        });
        
        sportsMenuItem.addEventListener('mouseleave', function() {
            setTimeout(() => {
                if (!sportsSubmenu.matches(':hover')) {
                    sportsSubmenu.style.display = 'none';
                    sportsSubmenu.style.maxHeight = '0';
                }
            }, 100);
        });
        
        sportsSubmenu.addEventListener('mouseleave', function() {
            sportsSubmenu.style.display = 'none';
            sportsSubmenu.style.maxHeight = '0';
        });
    }

    // Functions
    function performSearch(query, filters = {}) {
        // Simulate search API call
        console.log('Performing search:', query, filters);
        
        // Update results count
        const randomCount = Math.floor(Math.random() * 2000) + 500;
        document.querySelector('.results-count').innerHTML = 
            `Showing <strong>${randomCount.toLocaleString()}</strong> results for "<span id="searchTerm">${query || 'all'}</span>"`;
        
        // Update search time
        const randomTime = (Math.random() * 0.5 + 0.1).toFixed(2);
        document.querySelector('.results-time').textContent = 
            `Search completed in ${randomTime} seconds`;
    }

    function collectFilters() {
        const filters = {
            sport: document.getElementById('sportFilter').value,
            location: document.getElementById('locationFilter').value,
            minAge: document.getElementById('minAge').value,
            maxAge: document.getElementById('maxAge').value,
            position: document.getElementById('positionFilter').value,
            skillLevels: []
        };

        // Collect skill levels
        document.querySelectorAll('.skill-option input[type="checkbox"]:checked').forEach(checkbox => {
            filters.skillLevels.push(checkbox.value);
        });

        return filters;
    }

    function filterByCategory(category) {
        const cards = document.querySelectorAll('.result-card');
        
        cards.forEach(card => {
            if (category === 'all') {
                card.style.display = 'block';
            } else {
                const cardType = card.classList[1]; // e.g., 'player-card'
                const cardCategory = cardType.replace('-card', ''); // e.g., 'player'
                
                if (cardCategory === category || 
                    (category === 'teams' && cardCategory === 'team') ||
                    (category === 'coaches' && cardCategory === 'coach') ||
                    (category === 'opportunities' && cardCategory === 'opportunity') ||
                    (category === 'events' && cardCategory === 'event') ||
                    (category === 'players' && cardCategory === 'player')) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            }
        });
    }

    function toggleView(view) {
        const resultsGrid = document.getElementById('resultsGrid');
        
        if (view === 'list') {
            resultsGrid.style.gridTemplateColumns = '1fr';
            resultsGrid.querySelectorAll('.result-card').forEach(card => {
                card.style.display = 'flex';
                card.style.alignItems = 'center';
                card.style.padding = '15px 20px';
            });
        } else {
            resultsGrid.style.gridTemplateColumns = 'repeat(auto-fill, minmax(350px, 1fr))';
            resultsGrid.querySelectorAll('.result-card').forEach(card => {
                card.style.display = 'block';
                card.style.padding = '20px';
            });
        }
    }

    function showLocationSuggestions(query) {
        // Mock location suggestions
        const suggestions = [
            'Barcelona, Spain',
            'Madrid, Spain',
            'Valencia, Spain',
            'London, UK',
            'Manchester, UK',
            'Paris, France',
            'Milan, Italy',
            'Berlin, Germany',
            'Amsterdam, Netherlands',
            'Lisbon, Portugal'
        ].filter(location => 
            location.toLowerCase().includes(query.toLowerCase())
        ).slice(0, 5);

        if (suggestions.length > 0) {
            locationSuggestions.innerHTML = suggestions.map(suggestion => 
                `<div class="location-suggestion" onclick="selectLocation('${suggestion}')">${suggestion}</div>`
            ).join('');
            
            locationSuggestions.style.display = 'block';
        } else {
            locationSuggestions.innerHTML = '';
        }
    }

    function selectLocation(location) {
        locationFilter.value = location;
        locationSuggestions.innerHTML = '';
    }

    function startVoiceRecognition() {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        const recognition = new SpeechRecognition();
        
        recognition.continuous = false;
        recognition.interimResults = false;
        recognition.lang = 'en-US';

        recognition.onstart = function() {
            voiceSearchBtn.innerHTML = '<i class="fas fa-microphone-slash"></i>';
            voiceSearchBtn.style.color = '#dc3545';
            showNotification('Voice Search', 'Listening... Speak now');
        };

        recognition.onresult = function(event) {
            const transcript = event.results[0][0].transcript;
            searchInput.value = transcript;
            performSearch(transcript);
            showNotification('Voice Search Complete', `Searching for: ${transcript}`);
        };

        recognition.onerror = function(event) {
            showNotification('Voice Search Error', 'Could not recognize speech. Please try again.');
        };

        recognition.onend = function() {
            voiceSearchBtn.innerHTML = '<i class="fas fa-microphone"></i>';
            voiceSearchBtn.style.color = '#6a0dad';
        };

        recognition.start();
    }

    function showContactModal(name) {
        // Create modal
        const modal = document.createElement('div');
        modal.className = 'contact-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Contact ${name}</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <form class="contact-form">
                        <div class="form-group">
                            <label>Subject</label>
                            <input type="text" placeholder="Enter subject" required>
                        </div>
                        <div class="form-group">
                            <label>Message</label>
                            <textarea placeholder="Enter your message" rows="4" required></textarea>
                        </div>
                        <div class="form-actions">
                            <button type="button" class="cancel-btn">Cancel</button>
                            <button type="submit" class="send-btn">Send Message</button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        // Add styles
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        `;

        const modalContent = modal.querySelector('.modal-content');
        modalContent.style.cssText = `
            background: white;
            border-radius: 12px;
            padding: 0;
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
        `;

        // Add event listeners
        modal.querySelector('.modal-close').onclick = () => modal.remove();
        modal.querySelector('.cancel-btn').onclick = () => modal.remove();
        modal.onclick = (e) => {
            if (e.target === modal) modal.remove();
        };

        modal.querySelector('.contact-form').onsubmit = (e) => {
            e.preventDefault();
            modal.remove();
            showNotification('Message Sent', `Your message has been sent to ${name}`);
        };

        document.body.appendChild(modal);
    }

    function showNotification(title, message) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = 'notification';
        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="notification-text">
                    <strong>${title}</strong>
                    <p>${message}</p>
                </div>
                <button class="notification-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // Add styles
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 9999;
            max-width: 350px;
            animation: slideInRight 0.3s ease-out;
        `;

        const notificationContent = notification.querySelector('.notification-content');
        notificationContent.style.cssText = `
            display: flex;
            align-items: center;
            padding: 15px;
            gap: 12px;
        `;

        const icon = notification.querySelector('.notification-icon i');
        icon.style.cssText = `
            color: #4caf50;
            font-size: 20px;
        `;

        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.style.cssText = `
            background: none;
            border: none;
            color: #666;
            cursor: pointer;
            font-size: 14px;
            padding: 4px;
        `;

        // Add to DOM
        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.animation = 'slideOutRight 0.3s ease-in';
                setTimeout(() => notification.remove(), 300);
            }
        }, 5000);

        // Close button
        closeBtn.onclick = () => {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => notification.remove(), 300);
        };
    }

    // Make selectLocation globally available
    window.selectLocation = selectLocation;

    // Add CSS animations
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
        
        .location-suggestion {
            padding: 8px 12px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
            font-size: 14px;
            color: #333;
        }
        
        .location-suggestion:hover {
            background-color: #f8f9fa;
            color: #6a0dad;
        }
        
        .location-suggestion:last-child {
            border-bottom: none;
        }
        
        .location-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 2px solid #e1e5e9;
            border-top: none;
            border-radius: 0 0 8px 8px;
            z-index: 1000;
            display: none;
        }
        
        .modal-header {
            padding: 20px;
            border-bottom: 1px solid #e1e5e9;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-header h3 {
            margin: 0;
            color: #333;
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }
        
        .modal-close:hover {
            background: #f8f9fa;
        }
        
        .modal-body {
            padding: 20px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
        
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 14px;
            font-family: inherit;
        }
        
        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #6a0dad;
        }
        
        .form-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 20px;
        }
        
        .cancel-btn,
        .send-btn {
            padding: 10px 20px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            border: 2px solid;
            transition: all 0.3s ease;
        }
        
        .cancel-btn {
            background: white;
            color: #666;
            border-color: #e1e5e9;
        }
        
        .cancel-btn:hover {
            background: #f8f9fa;
        }
        
        .send-btn {
            background: #6a0dad;
            color: white;
            border-color: #6a0dad;
        }
        
        .send-btn:hover {
            background: #5a0c9a;
            border-color: #5a0c9a;
        }
    `;
    document.head.appendChild(style);
});