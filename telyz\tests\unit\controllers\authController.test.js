/**
 * Authentication Controller Tests
 * 
 * Unit tests for the authentication controller.
 */

const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const authController = require('../../../server/api/controllers/authController');
const User = require('../../../server/models/User');
const config = require('../../../server/config');

// Mock dependencies
jest.mock('bcryptjs', () => ({
  compare: jest.fn(),
}));

jest.mock('jsonwebtoken', () => ({
  sign: jest.fn(),
  verify: jest.fn(),
}));

jest.mock('../../../server/models/User');

jest.mock('../../../server/config', () => ({
  jwt: {
    secret: 'test-secret',
    expiresIn: '1h',
    refreshSecret: 'test-refresh-secret',
    refreshExpiresIn: '7d',
  },
}));

describe('Auth Controller', () => {
  let req;
  let res;
  
  beforeEach(() => {
    req = {
      body: {},
      user: null,
    };
    
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    
    // Clear all mocks
    jest.clearAllMocks();
  });
  
  describe('login', () => {
    it('should return 400 if user not found', async () => {
      req.body = {
        email: '<EMAIL>',
        password: 'password123',
      };
      
      User.findOne.mockResolvedValue(null);
      
      await authController.login(req, res);
      
      expect(User.findOne).toHaveBeenCalledWith({ email: '<EMAIL>' });
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({ message: 'Invalid credentials' });
    });
    
    it('should return 401 if user is inactive', async () => {
      req.body = {
        email: '<EMAIL>',
        password: 'password123',
      };
      
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        password: 'hashedPassword',
        isActive: false,
      };
      
      User.findOne.mockResolvedValue(mockUser);
      
      await authController.login(req, res);
      
      expect(User.findOne).toHaveBeenCalledWith({ email: '<EMAIL>' });
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({ message: 'Account is inactive' });
    });
    
    it('should return 400 if password is incorrect', async () => {
      req.body = {
        email: '<EMAIL>',
        password: 'password123',
      };
      
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        password: 'hashedPassword',
        isActive: true,
      };
      
      User.findOne.mockResolvedValue(mockUser);
      bcrypt.compare.mockResolvedValue(false);
      
      await authController.login(req, res);
      
      expect(User.findOne).toHaveBeenCalledWith({ email: '<EMAIL>' });
      expect(bcrypt.compare).toHaveBeenCalledWith('password123', 'hashedPassword');
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({ message: 'Invalid credentials' });
    });
    
    it('should return 200 with token and user data if login successful', async () => {
      req.body = {
        email: '<EMAIL>',
        password: 'password123',
      };
      
      const mockUser = {
        id: 'user123',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'hashedPassword',
        role: 'athlete',
        sport: 'football',
        isActive: true,
        isVerified: true,
        isPremium: false,
      };
      
      User.findOne.mockResolvedValue(mockUser);
      bcrypt.compare.mockResolvedValue(true);
      jwt.sign.mockReturnValueOnce('test-token').mockReturnValueOnce('test-refresh-token');
      
      await authController.login(req, res);
      
      expect(User.findOne).toHaveBeenCalledWith({ email: '<EMAIL>' });
      expect(bcrypt.compare).toHaveBeenCalledWith('password123', 'hashedPassword');
      
      expect(jwt.sign).toHaveBeenCalledTimes(2);
      expect(jwt.sign).toHaveBeenNthCalledWith(
        1,
        { user: { id: 'user123', role: 'athlete' } },
        'test-secret',
        { expiresIn: '1h' }
      );
      expect(jwt.sign).toHaveBeenNthCalledWith(
        2,
        { userId: 'user123' },
        'test-refresh-secret',
        { expiresIn: '7d' }
      );
      
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        token: 'test-token',
        refreshToken: 'test-refresh-token',
        user: {
          id: 'user123',
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          role: 'athlete',
          sport: 'football',
          isVerified: true,
          isPremium: false,
        },
      });
    });
    
    it('should return 500 if an error occurs', async () => {
      req.body = {
        email: '<EMAIL>',
        password: 'password123',
      };
      
      const error = new Error('Database error');
      User.findOne.mockRejectedValue(error);
      
      await authController.login(req, res);
      
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({ message: 'Server error' });
    });
  });
  
  describe('logout', () => {
    it('should return 200 with success message', async () => {
      await authController.logout(req, res);
      
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({ message: 'Logout successful' });
    });
    
    it('should return 500 if an error occurs', async () => {
      const error = new Error('Logout error');
      res.status.mockImplementationOnce(() => {
        throw error;
      });
      
      await authController.logout(req, res);
      
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({ message: 'Server error' });
    });
  });
  
  describe('getCurrentUser', () => {
    it('should return 200 with user data', async () => {
      req.user = {
        id: 'user123',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        role: 'athlete',
      };
      
      await authController.getCurrentUser(req, res);
      
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith(req.user);
    });
    
    it('should return 500 if an error occurs', async () => {
      req.user = {
        id: 'user123',
      };
      
      const error = new Error('Get user error');
      res.status.mockImplementationOnce(() => {
        throw error;
      });
      
      await authController.getCurrentUser(req, res);
      
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({ message: 'Server error' });
    });
  });
  
  describe('refreshToken', () => {
    it('should return 400 if refresh token is missing', async () => {
      req.body = {};
      
      await authController.refreshToken(req, res);
      
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({ message: 'Refresh token is required' });
    });
    
    it('should return 401 if refresh token is invalid', async () => {
      req.body = {
        refreshToken: 'invalid-token',
      };
      
      jwt.verify.mockImplementation(() => {
        throw new Error('Invalid token');
      });
      
      await authController.refreshToken(req, res);
      
      expect(jwt.verify).toHaveBeenCalledWith('invalid-token', 'test-refresh-secret');
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({ message: 'Invalid refresh token' });
    });
    
    it('should return 401 if user not found', async () => {
      req.body = {
        refreshToken: 'valid-token',
      };
      
      jwt.verify.mockReturnValue({ userId: 'user123' });
      User.findById.mockResolvedValue(null);
      
      await authController.refreshToken(req, res);
      
      expect(jwt.verify).toHaveBeenCalledWith('valid-token', 'test-refresh-secret');
      expect(User.findById).toHaveBeenCalledWith('user123');
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({ message: 'Invalid refresh token' });
    });
    
    it('should return 401 if user is inactive', async () => {
      req.body = {
        refreshToken: 'valid-token',
      };
      
      jwt.verify.mockReturnValue({ userId: 'user123' });
      User.findById.mockResolvedValue({
        id: 'user123',
        isActive: false,
      });
      
      await authController.refreshToken(req, res);
      
      expect(jwt.verify).toHaveBeenCalledWith('valid-token', 'test-refresh-secret');
      expect(User.findById).toHaveBeenCalledWith('user123');
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({ message: 'Account is inactive' });
    });
    
    it('should return 200 with new token if refresh successful', async () => {
      req.body = {
        refreshToken: 'valid-token',
      };
      
      jwt.verify.mockReturnValue({ userId: 'user123' });
      User.findById.mockResolvedValue({
        id: 'user123',
        role: 'athlete',
        isActive: true,
      });
      jwt.sign.mockReturnValue('new-token');
      
      await authController.refreshToken(req, res);
      
      expect(jwt.verify).toHaveBeenCalledWith('valid-token', 'test-refresh-secret');
      expect(User.findById).toHaveBeenCalledWith('user123');
      expect(jwt.sign).toHaveBeenCalledWith(
        { user: { id: 'user123', role: 'athlete' } },
        'test-secret',
        { expiresIn: '1h' }
      );
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({ token: 'new-token' });
    });
  });
});
