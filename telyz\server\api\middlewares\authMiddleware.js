/**
 * Authentication Middleware
 * 
 * Middleware functions for authentication and authorization.
 */

const jwt = require('jsonwebtoken');
const config = require('../../config');
const User = require('../../models/User');

/**
 * Authenticate user
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
exports.authenticate = async (req, res, next) => {
  try {
    // Get token from header
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ message: 'No token, authorization denied' });
    }
    
    // Verify token
    const decoded = jwt.verify(token, config.jwt.secret);
    
    // Find user
    const user = await User.findById(decoded.user.id).select('-password');
    
    if (!user) {
      return res.status(401).json({ message: 'User not found' });
    }
    
    if (!user.isActive) {
      return res.status(401).json({ message: 'User account is inactive' });
    }
    
    // Set user in request
    req.user = user;
    
    next();
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(401).json({ message: 'Token is not valid' });
  }
};

/**
 * Check if user is admin
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
exports.isAdmin = (req, res, next) => {
  if (req.user.role !== 'admin') {
    return res.status(403).json({ message: 'Access denied, admin role required' });
  }
  
  next();
};

/**
 * Check if user is owner or admin
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
exports.isOwnerOrAdmin = (req, res, next) => {
  if (req.user.role !== 'admin' && req.user.id !== req.params.id) {
    return res.status(403).json({ message: 'Access denied, not authorized' });
  }
  
  next();
};

/**
 * Check if user is owner
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
exports.isOwner = (req, res, next) => {
  if (req.user.id !== req.params.id) {
    return res.status(403).json({ message: 'Access denied, not authorized' });
  }
  
  next();
};

/**
 * Check if user has specific role
 * 
 * @param {string[]} roles - Array of allowed roles
 * @returns {Function} Middleware function
 */
exports.hasRole = (roles) => {
  return (req, res, next) => {
    if (!roles.includes(req.user.role)) {
      return res.status(403).json({ message: `Access denied, ${roles.join(' or ')} role required` });
    }
    
    next();
  };
};

/**
 * Check if user is verified
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
exports.isVerified = (req, res, next) => {
  if (!req.user.isVerified) {
    return res.status(403).json({ message: 'Access denied, email verification required' });
  }
  
  next();
};

/**
 * Check if user is premium
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
exports.isPremium = (req, res, next) => {
  if (!req.user.isPremium) {
    return res.status(403).json({ message: 'Access denied, premium subscription required' });
  }
  
  next();
};
