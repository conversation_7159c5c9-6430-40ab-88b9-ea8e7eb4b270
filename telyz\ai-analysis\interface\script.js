document.addEventListener('DOMContentLoaded', function() {
    // Referencias a elementos del DOM
    const welcomeScreen = document.getElementById('welcomeScreen');
    const uploadScreen = document.getElementById('uploadScreen');
    const resultsScreen = document.getElementById('resultsScreen');
    const uploadVideoBtn = document.getElementById('uploadVideoBtn');
    const dropArea = document.getElementById('dropArea');
    const videoFileInput = document.getElementById('videoFileInput');
    const startAnalysisBtn = document.getElementById('startAnalysisBtn');
    const speedButtons = document.querySelectorAll('.ai-speed-button');
    
    // Crear SVG para la ilustración de IA
    createAIIllustration();
    
    // Evento para el botón "Upload Video"
    uploadVideoBtn.addEventListener('click', function() {
        welcomeScreen.style.display = 'none';
        uploadScreen.style.display = 'block';
    });
    
    // Eventos para el área de arrastrar y soltar
    dropArea.addEventListener('click', function() {
        videoFileInput.click();
    });
    
    dropArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        dropArea.classList.add('drag-over');
    });
    
    dropArea.addEventListener('dragleave', function() {
        dropArea.classList.remove('drag-over');
    });
    
    dropArea.addEventListener('drop', function(e) {
        e.preventDefault();
        dropArea.classList.remove('drag-over');
        
        if (e.dataTransfer.files.length) {
            handleFileUpload(e.dataTransfer.files[0]);
        }
    });
    
    videoFileInput.addEventListener('change', function() {
        if (this.files.length) {
            handleFileUpload(this.files[0]);
        }
    });
    
    // Función para manejar la carga de archivos
    function handleFileUpload(file) {
        // Aquí se manejaría la lógica real de carga de archivos
        // Por ahora, solo mostramos el nombre del archivo
        console.log('Archivo seleccionado:', file.name);
        
        // Cambiar el estilo del área de carga para mostrar que se ha seleccionado un archivo
        dropArea.innerHTML = `
            <i class="fas fa-check-circle" style="color: #4CAF50;"></i>
            <p>${file.name}</p>
            <p class="ai-upload-subtitle">File selected successfully</p>
        `;
    }
    
    // Evento para el botón "Start Analysis"
    startAnalysisBtn.addEventListener('click', function() {
        // Simulamos un análisis con un temporizador
        startAnalysisBtn.disabled = true;
        startAnalysisBtn.textContent = 'Analyzing...';
        
        setTimeout(function() {
            uploadScreen.style.display = 'none';
            resultsScreen.style.display = 'block';
            startAnalysisBtn.disabled = false;
            startAnalysisBtn.textContent = 'Start Analysis';
        }, 3000);
    });
    
    // Eventos para los botones de velocidad
    speedButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Quitar la clase active de todos los botones
            speedButtons.forEach(btn => btn.classList.remove('active'));
            // Añadir la clase active al botón seleccionado
            this.classList.add('active');
        });
    });
    
    // Función para crear la ilustración de IA
    function createAIIllustration() {
        // Crear un SVG para la ilustración de IA
        const svgNS = "http://www.w3.org/2000/svg";
        const svg = document.createElementNS(svgNS, "svg");
        svg.setAttribute("width", "400");
        svg.setAttribute("height", "400");
        svg.setAttribute("viewBox", "0 0 400 400");
        
        // Crear el círculo principal
        const circle = document.createElementNS(svgNS, "circle");
        circle.setAttribute("cx", "200");
        circle.setAttribute("cy", "200");
        circle.setAttribute("r", "150");
        circle.setAttribute("fill", "none");
        circle.setAttribute("stroke", "#00bcd4");
        circle.setAttribute("stroke-width", "2");
        svg.appendChild(circle);
        
        // Crear líneas de conexión
        for (let i = 0; i < 10; i++) {
            const line = document.createElementNS(svgNS, "path");
            const startAngle = Math.random() * Math.PI * 2;
            const endAngle = startAngle + Math.PI + (Math.random() - 0.5);
            
            const startX = 200 + Math.cos(startAngle) * 150;
            const startY = 200 + Math.sin(startAngle) * 150;
            const endX = 200 + Math.cos(endAngle) * 150;
            const endY = 200 + Math.sin(endAngle) * 150;
            
            const controlX = 200 + Math.cos((startAngle + endAngle) / 2) * (Math.random() * 50 + 100);
            const controlY = 200 + Math.sin((startAngle + endAngle) / 2) * (Math.random() * 50 + 100);
            
            line.setAttribute("d", `M ${startX} ${startY} Q ${controlX} ${controlY} ${endX} ${endY}`);
            line.setAttribute("fill", "none");
            line.setAttribute("stroke", "#00bcd4");
            line.setAttribute("stroke-width", "1");
            line.setAttribute("opacity", "0.6");
            svg.appendChild(line);
        }
        
        // Crear puntos de datos
        for (let i = 0; i < 20; i++) {
            const angle = Math.random() * Math.PI * 2;
            const distance = Math.random() * 140;
            
            const x = 200 + Math.cos(angle) * distance;
            const y = 200 + Math.sin(angle) * distance;
            
            const dot = document.createElementNS(svgNS, "circle");
            dot.setAttribute("cx", x);
            dot.setAttribute("cy", y);
            dot.setAttribute("r", Math.random() * 3 + 2);
            dot.setAttribute("fill", "#00bcd4");
            svg.appendChild(dot);
        }
        
        // Añadir la cara sonriente
        const face = document.createElementNS(svgNS, "rect");
        face.setAttribute("x", "150");
        face.setAttribute("y", "280");
        face.setAttribute("width", "100");
        face.setAttribute("height", "100");
        face.setAttribute("rx", "20");
        face.setAttribute("fill", "none");
        face.setAttribute("stroke", "#00bcd4");
        face.setAttribute("stroke-width", "2");
        svg.appendChild(face);
        
        // Ojos
        const leftEye = document.createElementNS(svgNS, "circle");
        leftEye.setAttribute("cx", "175");
        leftEye.setAttribute("cy", "315");
        leftEye.setAttribute("r", "10");
        leftEye.setAttribute("fill", "#00bcd4");
        svg.appendChild(leftEye);
        
        const rightEye = document.createElementNS(svgNS, "circle");
        rightEye.setAttribute("cx", "225");
        rightEye.setAttribute("cy", "315");
        rightEye.setAttribute("r", "10");
        rightEye.setAttribute("fill", "#00bcd4");
        svg.appendChild(rightEye);
        
        // Sonrisa
        const smile = document.createElementNS(svgNS, "path");
        smile.setAttribute("d", "M 175 340 Q 200 360 225 340");
        smile.setAttribute("fill", "none");
        smile.setAttribute("stroke", "#00bcd4");
        smile.setAttribute("stroke-width", "3");
        svg.appendChild(smile);
        
        // Reemplazar la imagen con el SVG
        const aiIllustration = document.getElementById('aiIllustration');
        if (aiIllustration) {
            aiIllustration.parentNode.replaceChild(svg, aiIllustration);
        }
    }
});
