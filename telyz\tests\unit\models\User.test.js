/**
 * User Model Tests
 * 
 * Unit tests for the User model.
 */

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const User = require('../../../server/models/User');

// Mock mongoose and bcrypt
jest.mock('mongoose', () => {
  const mMongoose = {
    Schema: jest.fn().mockReturnValue({
      pre: jest.fn().mockReturnThis(),
      methods: {},
    }),
    model: jest.fn().mockReturnValue({}),
  };
  return mMongoose;
});

jest.mock('bcryptjs', () => ({
  genSalt: jest.fn().mockResolvedValue('salt'),
  hash: jest.fn().mockResolvedValue('hashedPassword'),
  compare: jest.fn().mockResolvedValue(true),
}));

describe('User Model', () => {
  let userSchema;
  let preSaveHook;
  
  beforeAll(() => {
    // Get the schema and pre-save hook
    userSchema = mongoose.Schema.mock.calls[0][0];
    preSaveHook = mongoose.Schema.mock.results[0].value.pre.mock.calls[0][1];
  });
  
  describe('Schema', () => {
    it('should have firstName field', () => {
      expect(userSchema.firstName).toBeDefined();
      expect(userSchema.firstName.type).toBe(String);
      expect(userSchema.firstName.required[0]).toBe(true);
    });
    
    it('should have lastName field', () => {
      expect(userSchema.lastName).toBeDefined();
      expect(userSchema.lastName.type).toBe(String);
      expect(userSchema.lastName.required[0]).toBe(true);
    });
    
    it('should have email field', () => {
      expect(userSchema.email).toBeDefined();
      expect(userSchema.email.type).toBe(String);
      expect(userSchema.email.required[0]).toBe(true);
      expect(userSchema.email.unique).toBe(true);
      expect(userSchema.email.lowercase).toBe(true);
    });
    
    it('should have password field', () => {
      expect(userSchema.password).toBeDefined();
      expect(userSchema.password.type).toBe(String);
      expect(userSchema.password.required[0]).toBe(true);
      expect(userSchema.password.minlength[0]).toBe(8);
      expect(userSchema.password.select).toBe(false);
    });
    
    it('should have role field', () => {
      expect(userSchema.role).toBeDefined();
      expect(userSchema.role.type).toBe(String);
      expect(userSchema.role.enum).toContain('athlete');
      expect(userSchema.role.enum).toContain('coach');
      expect(userSchema.role.enum).toContain('scout');
      expect(userSchema.role.enum).toContain('club');
      expect(userSchema.role.enum).toContain('journalist');
      expect(userSchema.role.enum).toContain('doctor');
      expect(userSchema.role.enum).toContain('admin');
      expect(userSchema.role.default).toBe('athlete');
    });
    
    it('should have sport field', () => {
      expect(userSchema.sport).toBeDefined();
      expect(userSchema.sport.type).toBe(String);
      expect(userSchema.sport.enum).toContain('football');
      expect(userSchema.sport.enum).toContain('basketball');
      expect(userSchema.sport.enum).toContain('volleyball');
      expect(userSchema.sport.enum).toContain('baseball');
      expect(userSchema.sport.enum).toContain('cricket');
      expect(userSchema.sport.enum).toContain('hockey');
      expect(userSchema.sport.required[0]).toBe(true);
    });
    
    it('should have isVerified field', () => {
      expect(userSchema.isVerified).toBeDefined();
      expect(userSchema.isVerified.type).toBe(Boolean);
      expect(userSchema.isVerified.default).toBe(false);
    });
    
    it('should have isPremium field', () => {
      expect(userSchema.isPremium).toBeDefined();
      expect(userSchema.isPremium.type).toBe(Boolean);
      expect(userSchema.isPremium.default).toBe(false);
    });
    
    it('should have isActive field', () => {
      expect(userSchema.isActive).toBeDefined();
      expect(userSchema.isActive.type).toBe(Boolean);
      expect(userSchema.isActive.default).toBe(true);
    });
    
    it('should have createdAt field', () => {
      expect(userSchema.createdAt).toBeDefined();
      expect(userSchema.createdAt.type).toBe(Date);
      expect(userSchema.createdAt.default).toBeDefined();
    });
    
    it('should have updatedAt field', () => {
      expect(userSchema.updatedAt).toBeDefined();
      expect(userSchema.updatedAt.type).toBe(Date);
      expect(userSchema.updatedAt.default).toBeDefined();
    });
  });
  
  describe('Pre-save hook', () => {
    it('should hash password before saving', async () => {
      const next = jest.fn();
      const user = {
        isModified: jest.fn().mockReturnValue(true),
        password: 'password123',
        updatedAt: null,
      };
      
      await preSaveHook.call(user, next);
      
      expect(user.isModified).toHaveBeenCalledWith('password');
      expect(bcrypt.genSalt).toHaveBeenCalledWith(10);
      expect(bcrypt.hash).toHaveBeenCalledWith('password123', 'salt');
      expect(user.password).toBe('hashedPassword');
      expect(user.updatedAt).toBeDefined();
      expect(next).toHaveBeenCalled();
    });
    
    it('should not hash password if not modified', async () => {
      const next = jest.fn();
      const user = {
        isModified: jest.fn().mockReturnValue(false),
        password: 'password123',
      };
      
      await preSaveHook.call(user, next);
      
      expect(user.isModified).toHaveBeenCalledWith('password');
      expect(bcrypt.genSalt).not.toHaveBeenCalled();
      expect(bcrypt.hash).not.toHaveBeenCalled();
      expect(user.password).toBe('password123');
      expect(next).toHaveBeenCalled();
    });
    
    it('should call next with error if hashing fails', async () => {
      const next = jest.fn();
      const error = new Error('Hashing error');
      bcrypt.genSalt.mockRejectedValueOnce(error);
      
      const user = {
        isModified: jest.fn().mockReturnValue(true),
        password: 'password123',
      };
      
      await preSaveHook.call(user, next);
      
      expect(next).toHaveBeenCalledWith(error);
    });
  });
  
  describe('Methods', () => {
    beforeAll(() => {
      // Add methods to schema
      userSchema.methods = {
        comparePassword: jest.fn().mockImplementation(async function(candidatePassword) {
          return await bcrypt.compare(candidatePassword, this.password);
        }),
        getFullName: jest.fn().mockImplementation(function() {
          return `${this.firstName} ${this.lastName}`;
        }),
        getCurrentTeam: jest.fn().mockImplementation(function() {
          return this.teams.find(team => team.current) || null;
        }),
      };
    });
    
    it('should compare passwords correctly', async () => {
      const user = {
        password: 'hashedPassword',
      };
      
      const result = await userSchema.methods.comparePassword.call(user, 'password123');
      
      expect(bcrypt.compare).toHaveBeenCalledWith('password123', 'hashedPassword');
      expect(result).toBe(true);
    });
    
    it('should get full name correctly', () => {
      const user = {
        firstName: 'John',
        lastName: 'Doe',
      };
      
      const result = userSchema.methods.getFullName.call(user);
      
      expect(result).toBe('John Doe');
    });
    
    it('should get current team correctly', () => {
      const user = {
        teams: [
          { name: 'Team A', current: false },
          { name: 'Team B', current: true },
          { name: 'Team C', current: false },
        ],
      };
      
      const result = userSchema.methods.getCurrentTeam.call(user);
      
      expect(result).toEqual({ name: 'Team B', current: true });
    });
    
    it('should return null if no current team', () => {
      const user = {
        teams: [
          { name: 'Team A', current: false },
          { name: 'Team B', current: false },
        ],
      };
      
      const result = userSchema.methods.getCurrentTeam.call(user);
      
      expect(result).toBeNull();
    });
  });
});
