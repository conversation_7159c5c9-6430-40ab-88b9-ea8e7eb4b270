/* Athletic Profile Styles */

/* Main content container */
.athletic-profile-content {
    padding: 0;
    background-color: #f0f2f5;
    width: 100%;
    max-width: 750px; /* Reducido de 900px a 750px para dar más espacio al sidebar */
    margin: 2% auto 0; /* <PERSON><PERSON><PERSON><PERSON> margen superior del 2% */
}

/* Profile Header */
.profile-header {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    overflow: hidden;
    padding-top: 0;
}

.profile-top-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px 0;
}

.profile-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-bottom: 0;
}

.profile-action-btn {
    background-color: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 20px;
    padding: 8px 15px;
    font-size: 13px;
    font-weight: 500;
    color: #333;
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: all 0.2s;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

.profile-action-btn i {
    margin-right: 5px;
    font-size: 14px;
}

.profile-action-btn:hover {
    background-color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.profile-action-btn.primary {
    background-color: #6a0dad;
    color: white;
    border: none;
}

.profile-action-btn.primary:hover {
    background-color: #8e44ad;
    transform: translateY(-2px);
}

.profile-info-container {
    padding: 0 20px 20px;
    position: relative;
}

.profile-photo-container {
    position: relative;
    margin: 0;
    margin-right: 20px; /* Añadido margen derecho para mover la foto hacia la derecha */
    width: 120px;
    height: 120px;
}

.profile-photo {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 4px solid white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    object-fit: cover;
}

.verification-badge {
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 25px;
    height: 25px;
    background-color: #6a0dad;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    border: 2px solid white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.profile-details {
    margin-bottom: 20px;
}

.profile-name-container {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    margin-right: 20px; /* Añadido margen derecho para mover los elementos hacia la derecha */
}

.profile-name-container h1 {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin: 0;
    margin-right: 10px;
}

.profile-title {
    font-size: 16px;
    color: #666;
    margin-bottom: 5px;
}

.profile-location {
    font-size: 14px;
    color: #777;
    margin-bottom: 8px; /* Reducido de 15px a 8px para acercar el equipo a la ubicación */
}

.profile-location i {
    margin-right: 5px;
    color: #6a0dad;
}

.profile-teams {
    display: flex;
    align-items: center;
    margin-top: 5px; /* Reducido de 10px a 5px para acercar el equipo a la información del jugador */
}

.current-team {
    display: flex;
    align-items: center;
    background-color: #f8f9fa;
    padding: 8px 15px;
    border-radius: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.team-logo {
    width: 24px;
    height: 24px;
    margin-right: 8px;
}

.team-info {
    display: flex;
    flex-direction: column;
}

.team-name {
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.team-duration {
    font-size: 12px;
    color: #777;
}

.profile-stats-summary {
    display: flex;
    justify-content: space-between;
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-top: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    padding: 0 10px;
    border-right: 1px solid #e0e0e0;
}

.stat-item:last-child {
    border-right: none;
}

.stat-value {
    font-size: 22px;
    font-weight: 700;
    color: #6a0dad;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Profile Navigation */
.profile-navigation {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    overflow: hidden;
}

.profile-nav-tabs {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    overflow-x: auto;
    gap: 2px;
}

.profile-nav-tabs li {
    flex: 1;
    min-width: 120px;
    text-align: center;
}

.profile-nav-tabs li a {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 15px 18px;
    color: #555;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s;
    border-bottom: 3px solid transparent;
}

.profile-nav-tabs li a i {
    margin-right: 8px;
    font-size: 16px;
}

/* Colores modernos para cada pestaña */
.profile-nav-tabs li a[href="#overview"] {
    color: #5c6bc0;
}

.profile-nav-tabs li a[href="#overview"] i {
    color: #5c6bc0;
}

.profile-nav-tabs li a[href="#career"] {
    color: #26a69a;
}

.profile-nav-tabs li a[href="#career"] i {
    color: #26a69a;
}

.profile-nav-tabs li a[href="#stats"] {
    color: #ec407a;
}

.profile-nav-tabs li a[href="#stats"] i {
    color: #ec407a;
}

.profile-nav-tabs li a[href="#skills"] {
    color: #7e57c2;
}

.profile-nav-tabs li a[href="#skills"] i {
    color: #7e57c2;
}

.profile-nav-tabs li a[href="#achievements"] {
    color: #ffa726;
}

.profile-nav-tabs li a[href="#achievements"] i {
    color: #ffa726;
}

.profile-nav-tabs li a[href="#media"] {
    color: #42a5f5;
}

.profile-nav-tabs li a[href="#media"] i {
    color: #42a5f5;
}

.profile-nav-tabs li a[href="#recommendations"] {
    color: #66bb6a;
}

.profile-nav-tabs li a[href="#recommendations"] i {
    color: #66bb6a;
}

.profile-nav-tabs li a[href="#ai-analysis"] {
    color: #00b8d4;
}

.profile-nav-tabs li a[href="#ai-analysis"] i {
    color: #00b8d4;
}

.profile-nav-tabs li a:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

/* Estilos para pestañas activas */
.profile-nav-tabs li.active a[href="#overview"] {
    border-bottom: 3px solid #5c6bc0;
}

.profile-nav-tabs li.active a[href="#career"] {
    border-bottom: 3px solid #26a69a;
}

.profile-nav-tabs li.active a[href="#stats"] {
    border-bottom: 3px solid #ec407a;
}

.profile-nav-tabs li.active a[href="#skills"] {
    border-bottom: 3px solid #7e57c2;
}

.profile-nav-tabs li.active a[href="#achievements"] {
    border-bottom: 3px solid #ffa726;
}

.profile-nav-tabs li.active a[href="#media"] {
    border-bottom: 3px solid #42a5f5;
}

.profile-nav-tabs li.active a[href="#recommendations"] {
    border-bottom: 3px solid #66bb6a;
}

.profile-nav-tabs li.active a[href="#ai-analysis"] {
    border-bottom: 3px solid #00b8d4;
}

/* Profile Content Sections */
.profile-content-sections {
    margin-bottom: 20px;
}

.profile-section {
    display: none;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;
    animation: fadeIn 0.3s ease-in-out;
}

.profile-section:first-child {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.profile-section h2 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-top: 0;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;
}

/* About Section */
.profile-about-section p {
    font-size: 14px;
    line-height: 1.6;
    color: #555;
    margin-bottom: 25px;
}

.profile-about-section h3 {
    font-size: 16px;
    font-weight: 600;
    color: #444;
    margin: 25px 0 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
}

/* Personal Information Section */
.personal-info-section {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.personal-info-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}

.personal-info-item {
    padding: 10px;
    transition: background-color 0.2s;
    border-radius: 6px;
}

.personal-info-item:hover {
    background-color: #f0f0f0;
}

.info-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
}

.info-label i {
    margin-right: 8px;
    color: #6a0dad;
    width: 16px;
    text-align: center;
}

.info-value {
    font-size: 15px;
    font-weight: 500;
    color: #333;
}

/* Player Specialties */
.player-specialties {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: 25px;
}

.specialty-tag {
    display: inline-flex;
    align-items: center;
    background-color: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
    padding: 8px 14px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s, box-shadow 0.2s;
}

.specialty-tag:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.specialty-tag i {
    margin-right: 6px;
    font-size: 14px;
}

/* Two-column layout */
.profile-two-column-layout {
    display: flex;
    gap: 25px;
    margin-top: 25px;
}

.profile-column {
    flex: 1;
}

.profile-column h3 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-top: 0;
    margin-bottom: 18px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;
}

.key-attributes {
    display: grid;
    grid-template-columns: 1fr;
    gap: 18px;
}

.attribute {
    display: flex;
    flex-direction: column;
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
    transition: transform 0.2s;
}

.attribute:hover {
    transform: translateY(-2px);
}

.attribute-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.attribute i {
    color: #6a0dad;
    font-size: 16px;
    margin-right: 8px;
}

.attribute-header span {
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.attribute-value {
    margin-left: auto;
    background-color: #6a0dad;
    color: white;
    font-weight: 600;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 12px;
}

.attribute-bar {
    height: 8px;
    background-color: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
}

.attribute-fill {
    height: 100%;
    background-color: #6a0dad;
    border-radius: 4px;
    transition: width 0.5s ease-in-out;
}

/* Career Highlights */
.highlights-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
}

.highlight-card {
    display: flex;
    align-items: center;
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.highlight-icon {
    width: 40px;
    height: 40px;
    background-color: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    margin-right: 15px;
}

.highlight-content h3 {
    font-size: 15px;
    font-weight: 600;
    color: #333;
    margin: 0 0 5px 0;
}

.highlight-content p {
    font-size: 13px;
    color: #666;
    margin: 0;
}

/* Recent Performance */
.recent-performance-section {
    margin-bottom: 30px;
}

.performance-chart {
    margin-bottom: 20px;
    overflow: hidden;
}

.chart-placeholder {
    width: 100%;
    max-height: 200px;
    object-fit: contain;
    border-radius: 8px;
    display: block;
}

.recent-matches {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.match-card {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    height: 100%;
}

.match-teams {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
}

.match-teams .team-logo {
    width: 30px;
    height: 30px;
}

.match-score {
    font-size: 18px;
    font-weight: 700;
    color: #333;
    margin: 0 15px;
}

.match-details {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.match-date {
    font-size: 13px;
    color: #666;
    margin-bottom: 8px;
}

.player-stats {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 10px;
}

.player-stats .stat {
    font-size: 13px;
    color: #555;
    background-color: white;
    padding: 5px 10px;
    border-radius: 15px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.player-stats .stat i {
    color: #6a0dad;
    margin-right: 5px;
}

/* Recommendations Section */
.recommendations-content {
    padding: 20px;
}

/* Recommendations Summary */
.recommendations-summary {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 20px;
    margin-bottom: 30px;
}

.recommendations-stats {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 15px;
    flex: 1;
}

.recommendation-stat-card {
    display: flex;
    align-items: center;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 15px;
    transition: transform 0.2s, box-shadow 0.2s;
}

.recommendation-stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.recommendation-stat-icon {
    width: 45px;
    height: 45px;
    background-color: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    margin-right: 15px;
}

.recommendation-stat-details {
    display: flex;
    flex-direction: column;
}

.recommendation-stat-value {
    font-size: 22px;
    font-weight: 700;
    color: #333;
    margin-bottom: 3px;
}

.recommendation-stat-label {
    font-size: 13px;
    color: #666;
}

.recommendations-actions {
    display: flex;
    flex-direction: column;
    gap: 10px;
    justify-content: center;
}

.recommendation-action-btn {
    padding: 10px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    background-color: white;
    color: #555;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 8px;
}

.recommendation-action-btn:hover {
    background-color: #f5f5f5;
}

.recommendation-action-btn.primary {
    background-color: #6a0dad;
    color: white;
    border-color: #6a0dad;
}

.recommendation-action-btn.primary:hover {
    background-color: #5a0b8d;
}

/* Featured Recommendation */
.featured-recommendation {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    overflow: hidden;
}

.featured-recommendation-header {
    background-color: rgba(106, 13, 173, 0.05);
    padding: 15px 20px;
    border-bottom: 1px solid rgba(106, 13, 173, 0.1);
}

.featured-recommendation-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.featured-recommendation-content {
    padding: 20px;
}

.recommender-info {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.recommender-photo {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
}

.recommender-details {
    flex: 1;
    min-width: 200px;
}

.recommender-details h4 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0 0 5px 0;
}

.recommender-title {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
}

.recommendation-date {
    font-size: 13px;
    color: #888;
}

.recommendation-badge {
    display: flex;
    align-items: center;
    background-color: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 500;
    gap: 6px;
}

.recommendation-text {
    margin-bottom: 20px;
    line-height: 1.6;
}

.recommendation-text p {
    font-size: 15px;
    color: #444;
    margin: 0 0 15px 0;
}

.recommendation-text p:last-child {
    margin-bottom: 0;
}

.recommendation-skills h5 {
    font-size: 15px;
    font-weight: 600;
    color: #333;
    margin: 0 0 10px 0;
}

.skill-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.skill-tag {
    display: inline-block;
    padding: 5px 12px;
    background-color: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 500;
}

/* Recommendations List */
.recommendations-list {
    margin-bottom: 30px;
}

.recommendations-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.recommendations-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.recommendations-filter {
    display: flex;
    align-items: center;
    gap: 10px;
}

.recommendations-filter label {
    font-size: 14px;
    color: #555;
}

.recommendation-filter-dropdown {
    padding: 8px 12px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    background-color: white;
    font-size: 14px;
    color: #333;
    cursor: pointer;
}

.recommendation-items {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.recommendation-item {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.2s, box-shadow 0.2s;
}

.recommendation-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.recommendation-item-header {
    display: flex;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
    gap: 12px;
}

.recommender-photo-small {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
}

.recommender-info-small {
    flex: 1;
}

.recommender-info-small h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0 0 3px 0;
}

.recommender-title-small {
    font-size: 13px;
    color: #666;
    margin-bottom: 3px;
}

.recommendation-date-small {
    font-size: 12px;
    color: #888;
}

.recommendation-badge-small {
    width: 30px;
    height: 30px;
    background-color: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.recommendation-item-content {
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
    max-height: 80px;
    overflow: hidden;
    transition: max-height 0.3s ease-out;
}

.recommendation-item-content p {
    font-size: 14px;
    color: #444;
    line-height: 1.5;
    margin: 0;
}

.recommendation-item-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    flex-wrap: wrap;
    gap: 10px;
}

.skill-tags-small {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.recommendation-expand-btn {
    background: none;
    border: none;
    color: #6a0dad;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
}

.recommendation-expand-btn:hover {
    text-decoration: underline;
}

/* Recommendation Request Section */
.recommendation-request-section {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.recommendation-request-section h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0 0 10px 0;
}

.recommendation-request-info {
    font-size: 14px;
    color: #555;
    line-height: 1.5;
    margin-bottom: 20px;
}

.recommendation-request-steps {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 25px;
}

.request-step {
    flex: 1;
    min-width: 200px;
    display: flex;
    gap: 15px;
}

.step-number {
    width: 30px;
    height: 30px;
    background-color: #6a0dad;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 600;
    flex-shrink: 0;
}

.step-content {
    flex: 1;
}

.step-content h4 {
    font-size: 15px;
    font-weight: 600;
    color: #333;
    margin: 0 0 5px 0;
}

.step-content p {
    font-size: 13px;
    color: #555;
    line-height: 1.5;
    margin: 0;
}

.recommendation-request-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    background-color: #6a0dad;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 12px 20px;
    font-size: 15px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
    width: 100%;
    max-width: 300px;
    margin: 0 auto;
}

.recommendation-request-btn:hover {
    background-color: #5a0b8d;
}

/* Responsive adjustments for recommendations */
@media (max-width: 768px) {
    .recommendations-stats {
        grid-template-columns: 1fr 1fr;
    }

    .recommendations-actions {
        width: 100%;
        flex-direction: row;
    }

    .recommendation-action-btn {
        flex: 1;
    }

    .recommender-info {
        flex-direction: column;
        align-items: flex-start;
    }

    .recommendation-badge {
        align-self: flex-start;
    }

    .recommendations-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .recommendation-request-steps {
        flex-direction: column;
    }
}

/* Media & Highlights Section */
.media-highlights-content {
    padding: 20px;
}

/* Media Navigation */
.media-navigation {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 25px;
}

.media-nav-button {
    padding: 10px 20px;
    background-color: #f0f0f0;
    border: none;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    color: #555;
    cursor: pointer;
    transition: all 0.2s;
}

.media-nav-button:hover {
    background-color: #e0e0e0;
}

.media-nav-button.active {
    background-color: #6a0dad;
    color: white;
}

/* Featured Media */
.featured-media {
    margin-bottom: 30px;
}

.featured-media-container {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.featured-media-player {
    position: relative;
    width: 100%;
    cursor: pointer;
}

.featured-media-player img {
    width: 100%;
    height: auto;
    display: block;
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 70px;
    height: 70px;
    background-color: rgba(106, 13, 173, 0.8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 30px;
    transition: all 0.2s;
}

.featured-media-player:hover .play-button {
    background-color: rgba(106, 13, 173, 1);
    transform: translate(-50%, -50%) scale(1.1);
}

.featured-media-info {
    padding: 20px;
}

.featured-media-info h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-top: 0;
    margin-bottom: 10px;
}

.media-metadata {
    display: flex;
    gap: 15px;
    margin-bottom: 10px;
}

.media-date {
    font-size: 14px;
    color: #666;
}

.media-views {
    font-size: 14px;
    color: #666;
}

.media-views i {
    margin-right: 5px;
}

.media-description {
    font-size: 14px;
    line-height: 1.5;
    color: #555;
    margin: 0;
}

/* Media Grid */
.media-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.media-item {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.2s, box-shadow 0.2s;
}

.media-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.media-thumbnail {
    position: relative;
    cursor: pointer;
}

.media-thumbnail img {
    width: 100%;
    height: auto;
    display: block;
}

.media-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
    opacity: 0;
    transition: opacity 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.media-thumbnail:hover .media-overlay {
    opacity: 1;
}

.media-play-button, .media-zoom-button {
    width: 50px;
    height: 50px;
    background-color: rgba(106, 13, 173, 0.8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    transition: all 0.2s;
}

.media-thumbnail:hover .media-play-button,
.media-thumbnail:hover .media-zoom-button {
    transform: scale(1.1);
}

.media-duration {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    font-size: 12px;
    padding: 3px 8px;
    border-radius: 4px;
}

.media-info {
    padding: 15px;
}

.media-info h4 {
    font-size: 15px;
    font-weight: 600;
    color: #333;
    margin-top: 0;
    margin-bottom: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Load More Button */
.load-more-container {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
}

.load-more-button {
    padding: 10px 25px;
    background-color: #f0f0f0;
    border: none;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    color: #555;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 8px;
}

.load-more-button:hover {
    background-color: #e0e0e0;
}

.load-more-button i {
    font-size: 16px;
}

/* Media Stats */
.media-stats {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.media-stat-item {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 15px;
    text-align: center;
}

.media-stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #6a0dad;
    margin-bottom: 5px;
}

.media-stat-label {
    font-size: 14px;
    color: #666;
}

/* Media Lightbox */
.media-lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    z-index: 9999;
    display: none;
    align-items: center;
    justify-content: center;
}

.lightbox-content {
    position: relative;
    width: 80%;
    max-width: 1000px;
}

.lightbox-close {
    position: absolute;
    top: -40px;
    right: 0;
    color: white;
    font-size: 24px;
    cursor: pointer;
    transition: color 0.2s;
}

.lightbox-close:hover {
    color: #6a0dad;
}

.lightbox-media-container {
    width: 100%;
    background-color: black;
    border-radius: 8px;
    overflow: hidden;
}

.lightbox-media-container img,
.lightbox-media-container video {
    width: 100%;
    height: auto;
    display: block;
}

.lightbox-caption {
    color: white;
    font-size: 16px;
    text-align: center;
    margin-top: 15px;
}

/* Responsive adjustments for media & highlights */
@media (max-width: 768px) {
    .media-grid {
        grid-template-columns: 1fr;
    }

    .media-stats {
        grid-template-columns: 1fr 1fr;
    }

    .lightbox-content {
        width: 95%;
    }
}

/* Achievements Section */
.achievements-content {
    padding: 20px;
}

/* Achievement Stats Summary */
.achievement-stats {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.achievement-stat-card {
    display: flex;
    align-items: center;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 20px;
    transition: transform 0.2s, box-shadow 0.2s;
}

.achievement-stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.achievement-stat-icon {
    width: 50px;
    height: 50px;
    background-color: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 22px;
    margin-right: 15px;
}

.achievement-stat-details {
    display: flex;
    flex-direction: column;
}

.achievement-stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 5px;
}

.achievement-stat-label {
    font-size: 14px;
    color: #666;
}

/* Major Trophies */
.major-trophies {
    margin-bottom: 30px;
}

.major-trophies h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-top: 0;
    margin-bottom: 15px;
}

.trophies-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.trophy-card {
    display: flex;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 20px;
    transition: transform 0.2s, box-shadow 0.2s;
}

.trophy-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.trophy-image {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    flex-shrink: 0;
}

.trophy-image img {
    max-width: 100%;
    max-height: 100%;
}

.trophy-details {
    flex: 1;
}

.trophy-details h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-top: 0;
    margin-bottom: 8px;
}

.trophy-metadata {
    display: flex;
    margin-bottom: 10px;
}

.trophy-year {
    font-size: 14px;
    font-weight: 500;
    color: #6a0dad;
    margin-right: 15px;
}

.trophy-team {
    font-size: 14px;
    color: #666;
}

.trophy-description {
    font-size: 14px;
    color: #555;
    line-height: 1.5;
    margin: 0;
}

/* Individual Awards */
.individual-awards {
    margin-bottom: 30px;
}

.individual-awards h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-top: 0;
    margin-bottom: 15px;
}

.awards-timeline {
    position: relative;
    padding-left: 30px;
}

.awards-timeline::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 10px;
    width: 2px;
    background-color: #e0e0e0;
}

.award-item {
    position: relative;
    margin-bottom: 25px;
}

.award-item:last-child {
    margin-bottom: 0;
}

.award-year {
    position: absolute;
    left: -30px;
    top: 0;
    width: 20px;
    height: 20px;
    background-color: #6a0dad;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: 700;
}

.award-content {
    display: flex;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 15px;
}

.award-icon {
    width: 40px;
    height: 40px;
    background-color: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    margin-right: 15px;
    flex-shrink: 0;
}

.award-details {
    flex: 1;
}

.award-details h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-top: 0;
    margin-bottom: 8px;
}

.award-details p {
    font-size: 14px;
    color: #555;
    line-height: 1.5;
    margin: 0;
}

/* Records & Milestones */
.records-milestones {
    margin-bottom: 30px;
}

.records-milestones h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-top: 0;
    margin-bottom: 15px;
}

.milestones-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}

.milestone-card {
    display: flex;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 15px;
    transition: transform 0.2s, box-shadow 0.2s;
}

.milestone-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.milestone-icon {
    width: 40px;
    height: 40px;
    background-color: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    margin-right: 15px;
    flex-shrink: 0;
}

.milestone-content {
    flex: 1;
}

.milestone-content h4 {
    font-size: 15px;
    font-weight: 600;
    color: #333;
    margin-top: 0;
    margin-bottom: 8px;
}

.milestone-content p {
    font-size: 13px;
    color: #555;
    line-height: 1.5;
    margin: 0;
}

/* Achievement Gallery */
.achievement-gallery {
    margin-bottom: 20px;
}

.achievement-gallery h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-top: 0;
    margin-bottom: 15px;
}

.gallery-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}

.gallery-item {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.2s, box-shadow 0.2s;
}

.gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.gallery-item img {
    width: 100%;
    height: auto;
    display: block;
}

.gallery-caption {
    padding: 12px 15px;
    font-size: 14px;
    color: #333;
    text-align: center;
}

/* Responsive adjustments for achievements */
@media (max-width: 768px) {
    .achievement-stats {
        grid-template-columns: 1fr 1fr;
    }

    .trophies-grid,
    .milestones-grid,
    .gallery-container {
        grid-template-columns: 1fr;
    }

    .trophy-card {
        flex-direction: column;
    }

    .trophy-image {
        margin-right: 0;
        margin-bottom: 15px;
        align-self: center;
    }
}

/* Skills & Attributes Section */
.skills-attributes-content {
    padding: 20px;
}

/* Skills Overview */
.skills-overview {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 30px;
}

.skills-summary {
    flex: 1;
    min-width: 250px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.overall-rating {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.rating-circle {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: linear-gradient(135deg, #6a0dad, #9c27b0);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
}

.rating-value {
    font-size: 36px;
    font-weight: 700;
    color: white;
}

.rating-label {
    font-size: 16px;
    font-weight: 500;
    color: #333;
}

.player-style {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.player-style h3 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-top: 0;
    margin-bottom: 15px;
}

.style-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.style-tag {
    display: inline-block;
    padding: 6px 12px;
    background-color: #f0f0f0;
    color: #333;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
}

.style-tag.primary {
    background-color: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
    border: 1px solid rgba(106, 13, 173, 0.2);
}

.key-attributes {
    flex: 2;
    min-width: 300px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.key-attributes h3 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-top: 0;
    margin-bottom: 15px;
}

.attributes-grid {
    display: flex;
    flex-direction: column;
    gap: 18px;
    padding: 10px 5px;
}

.attribute-item {
    display: flex;
    align-items: center;
    gap: 15px;
    background-color: #f8f9fa;
    padding: 12px 15px;
    border-radius: 8px;
    transition: transform 0.2s, box-shadow 0.2s;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.attribute-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    background-color: #f5eeff;
}

.attribute-name {
    width: 150px;
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.attribute-bar-container {
    flex: 1;
    height: 8px;
    background-color: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
}

.attribute-bar {
    height: 100%;
    background: linear-gradient(90deg, #6a0dad, #9c27b0);
    border-radius: 4px;
}

.attribute-value {
    width: 30px;
    font-size: 14px;
    font-weight: 700;
    color: #6a0dad;
    text-align: right;
}

/* Detailed Attributes */
.detailed-attributes {
    margin-bottom: 30px;
}

.detailed-attributes h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-top: 0;
    margin-bottom: 15px;
}

.attributes-tabs {
    display: flex;
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 20px;
    overflow-x: auto;
}

.attribute-tab {
    padding: 12px 25px;
    background: none;
    border: none;
    font-size: 14px;
    font-weight: 600;
    color: #666;
    cursor: pointer;
    transition: all 0.3s;
    border-bottom: 3px solid transparent;
    position: relative;
    overflow: hidden;
}

.attribute-tab:hover {
    color: #6a0dad;
    background-color: rgba(106, 13, 173, 0.05);
}

.attribute-tab.active {
    color: #6a0dad;
    border-bottom-color: #6a0dad;
    background-color: rgba(106, 13, 173, 0.08);
}

.attribute-tab.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, #6a0dad, #9c27b0);
    border-radius: 3px 3px 0 0;
}

.attributes-panel {
    display: none;
}

.attributes-panel.active {
    display: block;
}

.attributes-category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
}

.attribute-card {
    display: flex;
    align-items: center;
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s;
}

.attribute-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.attribute-icon {
    width: 40px;
    height: 40px;
    background-color: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    margin-right: 15px;
}

.attribute-details {
    flex: 1;
}

.attribute-details .attribute-name {
    display: block;
    width: auto;
    margin-bottom: 5px;
}

.attribute-rating {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.rating-stars {
    display: flex;
    gap: 3px;
    color: #6a0dad;
}

.rating-stars i {
    font-size: 16px;
}

.rating-stars .far {
    color: #e0e0e0;
}

.rating-value {
    font-size: 16px;
    font-weight: 700;
    color: #6a0dad;
    margin-left: 10px;
}

.rating-dots {
    display: flex;
    gap: 3px;
}

.dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #e0e0e0;
}

.dot.filled {
    background-color: #6a0dad;
}

.rating-number {
    font-size: 14px;
    font-weight: 700;
    color: #6a0dad;
}

.goalkeeper-note {
    display: flex;
    align-items: center;
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.goalkeeper-note i {
    font-size: 24px;
    color: #6a0dad;
    margin-right: 15px;
}

.goalkeeper-note p {
    font-size: 14px;
    color: #555;
    margin: 0;
}

/* Special Abilities */
.special-abilities {
    margin-bottom: 30px;
}

.special-abilities h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-top: 0;
    margin-bottom: 15px;
}

.abilities-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.ability-card {
    display: flex;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 20px;
    transition: transform 0.2s, box-shadow 0.2s;
}

.ability-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.ability-icon {
    width: 50px;
    height: 50px;
    background-color: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 22px;
    margin-right: 15px;
}

.ability-content {
    flex: 1;
}

.ability-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-top: 0;
    margin-bottom: 8px;
}

.ability-content p {
    font-size: 14px;
    color: #555;
    line-height: 1.5;
    margin: 0;
}

/* Development Areas */
.development-areas {
    margin-bottom: 20px;
}

.development-areas h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-top: 0;
    margin-bottom: 15px;
}

.development-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.development-item {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.development-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.development-header i {
    font-size: 20px;
    color: #4caf50;
    margin-right: 10px;
}

.development-header h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.development-item p {
    font-size: 14px;
    color: #555;
    line-height: 1.5;
    margin-bottom: 15px;
}

.development-progress {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.progress-bar {
    height: 8px;
    background-color: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background-color: #4caf50;
    border-radius: 4px;
}

.progress-label {
    font-size: 12px;
    color: #666;
    text-align: right;
}

/* Responsive adjustments for skills & attributes */
@media (max-width: 768px) {
    .skills-overview {
        flex-direction: column;
    }

    .attributes-category-grid {
        grid-template-columns: 1fr;
    }

    .abilities-container,
    .development-container {
        grid-template-columns: 1fr;
    }

    .attributes-tabs {
        flex-wrap: nowrap;
        overflow-x: auto;
    }

    .attribute-tab {
        flex: 0 0 auto;
        white-space: nowrap;
    }
}

/* Performance Stats Section */
.performance-stats-content {
    padding: 20px;
}

.season-selector {
    display: flex;
    align-items: center;
    margin-bottom: 25px;
}

.season-selector label {
    font-size: 15px;
    font-weight: 500;
    color: #333;
    margin-right: 10px;
}

.season-dropdown {
    padding: 8px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 14px;
    color: #333;
    background-color: white;
    cursor: pointer;
}

/* Key Performance Indicators */
.kpi-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.kpi-card {
    display: flex;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 20px;
    transition: transform 0.2s, box-shadow 0.2s;
}

.kpi-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.kpi-icon {
    width: 50px;
    height: 50px;
    background-color: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 22px;
    margin-right: 15px;
}

.kpi-content {
    flex: 1;
}

.kpi-value {
    display: block;
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 5px;
}

.kpi-label {
    display: block;
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
}

.kpi-trend {
    display: flex;
    align-items: center;
    font-size: 12px;
    font-weight: 500;
}

.kpi-trend.positive {
    color: #4caf50;
}

.kpi-trend.negative {
    color: #f44336;
}

.kpi-trend i {
    margin-right: 5px;
}

/* Performance Charts */
.performance-charts {
    margin-bottom: 30px;
}

.chart-container {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;
}

.chart-container h3 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-top: 0;
    margin-bottom: 15px;
}

.chart-placeholder {
    width: 100%;
    overflow: hidden;
    border-radius: 4px;
}

.chart-placeholder img {
    width: 100%;
    height: auto;
    display: block;
}

/* Detailed Stats */
.detailed-stats {
    margin-bottom: 30px;
}

.detailed-stats h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-top: 0;
    margin-bottom: 15px;
}

.stats-tabs {
    display: flex;
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 20px;
    overflow-x: auto;
}

.stats-tab {
    padding: 10px 20px;
    background: none;
    border: none;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    cursor: pointer;
    transition: all 0.2s;
    border-bottom: 3px solid transparent;
}

.stats-tab:hover {
    color: #6a0dad;
}

.stats-tab.active {
    color: #6a0dad;
    border-bottom-color: #6a0dad;
}

.stats-panel {
    display: none;
}

.stats-panel.active {
    display: block;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 15px;
}

.stat-card {
    display: flex;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 15px;
    transition: transform 0.2s, box-shadow 0.2s;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 45px;
    height: 45px;
    background-color: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    margin-right: 15px;
    flex-shrink: 0;
}

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 22px;
    font-weight: 700;
    color: #6a0dad;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 10px;
}

.stat-chart {
    height: 6px;
    background-color: #e0e0e0;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 8px;
}

.chart-bar {
    height: 100%;
    background: linear-gradient(90deg, #6a0dad, #9c27b0);
    border-radius: 3px;
}

.stat-subtitle {
    font-size: 12px;
    color: #888;
}

.stat-box {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s;
}

.stat-box:hover {
    transform: translateY(-3px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.stat-title {
    font-size: 14px;
    font-weight: 500;
    color: #666;
    margin-bottom: 8px;
}

/* Mini chart for pass types */
.mini-chart {
    display: flex;
    height: 20px;
    width: 100%;
    border-radius: 10px;
    overflow: hidden;
}

.mini-bar {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    color: white;
    font-weight: 500;
}

.mini-bar:nth-child(1) {
    background-color: #6a0dad;
}

.mini-bar:nth-child(2) {
    background-color: #9c27b0;
}

.mini-bar:nth-child(3) {
    background-color: #ba68c8;
}

/* Performance Comparison */
.performance-comparison {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.performance-comparison h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-top: 0;
    margin-bottom: 15px;
}

.premium-feature-note {
    background-color: #fff8e1;
    border-left: 4px solid #ffc107;
    padding: 12px 15px;
    margin-bottom: 20px;
    border-radius: 4px;
    font-size: 14px;
    color: #555;
}

.premium-feature-note i {
    color: #ffc107;
    margin-right: 8px;
}

.premium-link {
    color: #6a0dad;
    font-weight: 500;
    text-decoration: none;
}

.premium-link:hover {
    text-decoration: underline;
}

.comparison-placeholder {
    width: 100%;
    overflow: hidden;
    border-radius: 4px;
}

.comparison-placeholder img {
    width: 100%;
    height: auto;
    display: block;
}

/* Responsive adjustments for performance stats */
@media (max-width: 768px) {
    .kpi-container {
        grid-template-columns: 1fr 1fr;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .stats-tabs {
        flex-wrap: nowrap;
        overflow-x: auto;
    }

    .stats-tab {
        flex: 0 0 auto;
        white-space: nowrap;
    }
}

/* Career History Section */
.career-history-content {
    padding: 20px;
}

.career-timeline {
    display: flex;
    flex-direction: column;
    gap: 30px;
    margin-bottom: 40px;
}

.career-entry {
    display: flex;
    background-color: #f8f9fa;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: transform 0.2s, box-shadow 0.2s;
}

.career-entry:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.career-entry.current-club {
    border-left: 4px solid #6a0dad;
}

.career-entry.youth-career {
    background-color: #f0f0f0;
}

.club-logo-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    background-color: white;
    width: 120px;
    flex-shrink: 0;
}

.club-logo {
    max-width: 80px;
    max-height: 80px;
}

.career-entry-content {
    flex: 1;
    padding: 20px;
}

.career-entry-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 10px;
}

.career-entry-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.career-period {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.career-entry-details {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.position-tag {
    display: inline-block;
    padding: 6px 12px;
    background-color: rgba(106, 13, 173, 0.1);
    border-radius: 20px;
    font-size: 13px;
    color: #6a0dad;
    font-weight: 500;
    align-self: flex-start;
}

.career-stats {
    display: flex;
    gap: 20px;
    margin-bottom: 10px;
}

.career-stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: white;
    padding: 10px 15px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    min-width: 80px;
}

.stat-value {
    font-size: 20px;
    font-weight: 700;
    color: #6a0dad;
}

.stat-label {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

.career-achievements h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0 0 10px 0;
}

.career-achievements ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.career-achievements li {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;
    color: #555;
}

.career-achievements li i {
    color: #ffc107;
    margin-right: 8px;
    font-size: 16px;
}

.career-description p {
    font-size: 14px;
    line-height: 1.6;
    color: #555;
    margin: 0;
}

/* International Career Section */
.international-career-section {
    background-color: #f8f9fa;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    padding: 20px;
}

.international-career-section h2 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-top: 0;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e0e0e0;
}

.international-career-content {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.national-team-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.national-team-logo img {
    max-width: 120px;
    max-height: 120px;
}

.international-stats {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.int-stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    min-width: 80px;
}

.int-stat-value {
    font-size: 20px;
    font-weight: 700;
    color: #6a0dad;
}

.int-stat-label {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

.international-achievements {
    flex: 1 0 100%;
}

.international-achievements h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0 0 10px 0;
}

.international-achievements ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.international-achievements li {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;
    color: #555;
}

.international-achievements li i {
    color: #ffc107;
    margin-right: 8px;
    font-size: 16px;
}

.international-description {
    flex: 1 0 100%;
}

.international-description p {
    font-size: 14px;
    line-height: 1.6;
    color: #555;
    margin: 0;
}

/* Responsive adjustments for career history */
@media (max-width: 768px) {
    .career-entry {
        flex-direction: column;
    }

    .club-logo-container {
        width: 100%;
        padding: 15px;
    }

    .career-stats {
        flex-wrap: wrap;
    }

    .international-career-content {
        flex-direction: column;
    }

    .national-team-logo {
        width: 100%;
        justify-content: center;
    }
}

/* AI Analysis Section */
.ai-analysis-content {
    padding: 20px;
}

.ai-analysis-intro {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    background-color: rgba(0, 184, 212, 0.1);
    padding: 20px;
    border-radius: 8px;
}

.ai-icon-container {
    font-size: 40px;
    color: #00b8d4;
    margin-right: 20px;
    background-color: rgba(0, 184, 212, 0.15);
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.ai-analysis-intro p {
    flex: 1;
    margin: 0;
    font-size: 16px;
    line-height: 1.6;
}

/* AI Report Styles */
.ai-analysis-report {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.ai-report-header {
    display: flex;
    align-items: center;
    padding: 20px;
    background-color: rgba(0, 184, 212, 0.05);
    border-bottom: 1px solid rgba(0, 184, 212, 0.1);
}

.ai-report-icon {
    width: 50px;
    height: 50px;
    background-color: rgba(0, 184, 212, 0.1);
    color: #00b8d4;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    margin-right: 15px;
    flex-shrink: 0;
}

.ai-report-title h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0 0 5px 0;
}

.ai-report-title p {
    font-size: 14px;
    color: #555;
    margin: 0;
    line-height: 1.5;
}

.ai-report-preview {
    display: flex;
    padding: 20px;
    gap: 20px;
    flex-wrap: wrap;
}

.pdf-preview {
    position: relative;
    width: 250px;
    height: 350px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    flex-shrink: 0;
    cursor: pointer;
    transition: transform 0.2s;
}

.pdf-preview:hover {
    transform: scale(1.02);
}

.pdf-thumbnail {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.pdf-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    opacity: 0;
    transition: opacity 0.2s;
    z-index: 10; /* Lower z-index than the upload icon */
}

.pdf-preview:hover .pdf-overlay {
    opacity: 1;
}

.pdf-overlay i {
    font-size: 36px;
    margin-bottom: 10px;
}

.pdf-overlay span {
    font-size: 14px;
    font-weight: 500;
}

.pdf-upload-icon {
    position: absolute;
    bottom: 15px;
    right: 15px;
    width: 40px;
    height: 40px;
    background-color: #ff4081;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    cursor: pointer;
    transition: transform 0.2s, background-color 0.2s;
    z-index: 20; /* Increased z-index to be above the overlay */
}

.pdf-upload-icon:hover {
    transform: scale(1.1);
    background-color: #e91e63;
}

.ai-report-info {
    flex: 1;
    min-width: 300px;
}

.report-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 15px;
}

.report-meta-item {
    display: flex;
    align-items: center;
    background-color: #f8f9fa;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 13px;
    color: #555;
}

.report-meta-item i {
    color: #00b8d4;
    margin-right: 8px;
}

.report-description p {
    font-size: 14px;
    color: #444;
    line-height: 1.6;
    margin: 0 0 15px 0;
}

.report-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 20px;
}

.ai-report-button {
    padding: 10px 15px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s;
    background-color: white;
    border: 1px solid #e0e0e0;
    color: #555;
}

.ai-report-button:hover {
    background-color: #f5f5f5;
}

.ai-report-button.primary {
    background-color: #00b8d4;
    color: white;
    border-color: #00b8d4;
}

.ai-report-button.primary:hover {
    background-color: #0097a7;
}

.ai-report-button.update {
    background-color: #6a0dad;
    color: white;
    border-color: #6a0dad;
}

.ai-report-button.update:hover {
    background-color: #5a0b8d;
}

.ai-report-highlights {
    padding: 20px;
    border-top: 1px solid #f0f0f0;
}

.ai-report-highlights h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0 0 20px 0;
}

.highlights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.highlight-item {
    display: flex;
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.highlight-icon {
    width: 40px;
    height: 40px;
    background-color: rgba(0, 184, 212, 0.1);
    color: #00b8d4;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    margin-right: 15px;
    flex-shrink: 0;
}

.highlight-content h5 {
    font-size: 15px;
    font-weight: 600;
    color: #333;
    margin: 0 0 10px 0;
}

.highlight-content ul {
    margin: 0;
    padding-left: 18px;
}

.highlight-content li {
    font-size: 13px;
    color: #555;
    margin-bottom: 5px;
}

.ai-analysis-cta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    background-color: #f8f9fa;
    border-top: 1px solid #f0f0f0;
    flex-wrap: wrap;
    gap: 15px;
}

.cta-content {
    display: flex;
    align-items: center;
}

.cta-content i {
    width: 45px;
    height: 45px;
    background-color: rgba(0, 184, 212, 0.1);
    color: #00b8d4;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    margin-right: 15px;
}

.cta-text h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0 0 5px 0;
}

.cta-text p {
    font-size: 14px;
    color: #555;
    margin: 0;
}

.ai-cta-button {
    padding: 10px 20px;
    background-color: #00b8d4;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.2s;
}

.ai-cta-button:hover {
    background-color: #0097a7;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .profile-stats-summary {
        flex-wrap: wrap;
    }

    .stat-item {
        flex: 0 0 50%;
        margin-bottom: 15px;
        border-right: none;
        border-bottom: 1px solid #e0e0e0;
    }

    .profile-nav-tabs {
        flex-wrap: nowrap;
        overflow-x: auto;
    }

    .profile-nav-tabs li {
        flex: 0 0 auto;
    }

    .profile-two-column-layout {
        flex-direction: column;
    }

    .recent-matches {
        grid-template-columns: 1fr;
    }
}
