<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Template 1 - Telez Layout</title>
    <link rel="stylesheet" href="template1.css">
    <link rel="stylesheet" href="../../styles/global.css">
    <link rel="stylesheet" href="../Home/styles.css">
    <link rel="stylesheet" href="../نجاة/opportunities.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Agregamos fuentes de Google -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Barra lateral izquierda -->
        <div class="sidebar-left">
            <div class="logo">
                <a href="#" class="logo-link">
                    <div class="logo-container">T</div>
                </a>
            </div>
            <div class="sidebar-menu">
                <ul>
                    <li class="has-submenu" id="aiMenuItem">
    <a href="#"><i class="fas fa-robot"></i> AI Analysis <span class="badge new-badge">NEW</span></a>
    <ul class="submenu ai-submenu" id="aiDropdownMenu">
        <li>
            <a href="#" data-description="Upload game footage for AI breakdown of techniques and plays" data-color="#FF5722">
                <i class="fas fa-video"></i> <span>Video Analysis</span>
            </a>
        </li>
        <li>
            <a href="#" data-description="View weekly stats of star players and compare with your performance" data-color="#4CAF50">
                <i class="fas fa-chart-line"></i> <span>Performance Stats</span>
            </a>
        </li>
        <li>
            <a href="#" data-description="Compare your metrics with other players in your position" data-color="#2196F3">
                <i class="fas fa-users"></i> <span>Player Comparison</span>
            </a>
        </li>
        <li>
            <a href="#" data-description="Get personalized training plans based on your performance data" data-color="#FFC107">
                <i class="fas fa-dumbbell"></i> <span>Training Recommendations</span>
            </a>
        </li>
        <li>
            <a href="#" data-description="See how you measure against professional standards in your sport" data-color="#9C27B0">
                <i class="fas fa-trophy"></i> <span>Talent Benchmarks</span>
            </a>
        </li>
    </ul>
</li>
                    <li><a href="#"><i class="fas fa-hashtag"></i> Explore</a></li>
                    <li><a href="#"><i class="fas fa-bullhorn"></i> Announcements</a></li>
                    <li><a href="#"><i class="fas fa-envelope"></i> Messages</a></li>
                    <li class="has-submenu" id="sportsMenuItem">
                        <a href="#"><i class="fas fa-running"></i> Sports</a>
                        <ul class="submenu sports-submenu" id="sportsSubmenu">
                            <li><a href="#"><i class="fas fa-futbol"></i> Football</a></li>
                            <li><a href="#"><i class="fas fa-basketball-ball"></i> Basketball</a></li>
                            <li><a href="#"><i class="fas fa-volleyball-ball"></i> Volleyball</a></li>
                            <li><a href="#"><i class="fas fa-baseball-ball"></i> Baseball</a></li>
                            <li><a href="#"><i class="fas fa-table-tennis"></i> Cricket</a></li>
                            <li><a href="#"><i class="fas fa-hockey-puck"></i> Field Hockey</a></li>
                        </ul>
                    </li>
                    <li><a href="#"><i class="fas fa-briefcase"></i> Opportunities</a></li>
                    <li><a href="#"><i class="fas fa-trophy"></i> Achievements</a></li>
                    <li><a href="#"><i class="fas fa-dumbbell"></i> Training</a></li>
                </ul>
                <div class="user-profile-sidebar">
                    <div class="profile-pic-small">
                        <img src="https://via.placeholder.com/40" alt="Profile" class="profile-pic-img">
                    </div>
                    <div class="profile-info-sidebar">
                        <span>John Doe</span>
                        <span class="teams-clubs">Teams & Clubs</span>
                    </div>
                    <div class="profile-dropdown-toggle" id="profileDropdownToggle">
                        <i class="fas fa-ellipsis-v"></i>
                    </div>
                    <div class="profile-dropdown-menu" id="profileDropdownMenu">
                        <a href="#" class="profile-dropdown-item" data-tooltip="View your complete athletic profile and career history"><i class="fas fa-user"></i> View Athletic Profile</a>
                        <a href="#" class="profile-dropdown-item" data-tooltip="Edit your athletic profile information, skills and achievements"><i class="fas fa-cog"></i> Edit Athletic Profile</a>
                        <a href="#" class="profile-dropdown-item" data-tooltip="View and manage your sports achievements, trophies and awards"><i class="fas fa-medal"></i> Achievements</a>
                        <a href="#" class="profile-dropdown-item" data-tooltip="View and manage recommendations from coaches, scouts and sports experts"><i class="fas fa-star"></i> Expert Recommendations</a>
                        <a href="#" class="profile-dropdown-item" data-tooltip="Control your athletic profile privacy and who can view it"><i class="fas fa-shield-alt"></i> Privacy Settings</a>
                        <div class="dropdown-divider"></div>
                        <a href="#" class="profile-dropdown-item logout-item" data-tooltip="Sign out from your account"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </div>
                </div>

                <script>
                    // JavaScript para controlar el menú desplegable
                    document.addEventListener('DOMContentLoaded', function() {
                        const toggleButton = document.getElementById('profileDropdownToggle');
                        const dropdownMenu = document.getElementById('profileDropdownMenu');
                        const userProfile = document.querySelector('.user-profile-sidebar');

                        let isMenuOpen = false;

                        // Función para mostrar/ocultar el menú
                        toggleButton.addEventListener('click', function(e) {
                            e.stopPropagation();
                            isMenuOpen = !isMenuOpen;
                            dropdownMenu.classList.toggle('show', isMenuOpen);

                            // Asegurarse de que los iconos sean visibles
                            if (isMenuOpen) {
                                setTimeout(() => {
                                    const icons = dropdownMenu.querySelectorAll('i');
                                    icons.forEach(icon => {
                                        icon.style.display = 'inline-block';
                                    });
                                }, 50);
                            }
                        });

                        // Cerrar el menú al hacer clic fuera de él
                        document.addEventListener('click', function(e) {
                            if (!dropdownMenu.contains(e.target) && !toggleButton.contains(e.target) && !userProfile.contains(e.target)) {
                                isMenuOpen = false;
                                dropdownMenu.classList.remove('show');
                            }
                        });

                        // Evitar que el menú se cierre al hacer clic dentro de él
                        dropdownMenu.addEventListener('click', function(e) {
                            e.stopPropagation();
                        });

                        // Evitar que el menú se cierre al pasar el ratón sobre él
                        dropdownMenu.addEventListener('mouseenter', function() {
                            if (isMenuOpen) {
                                dropdownMenu.classList.add('show');
                            }
                        });

                        // Configurar tooltips para el menú del perfil
                        const profileTooltip = document.getElementById('profile-tooltip');
                        const profileMenuItems = dropdownMenu.querySelectorAll('.profile-dropdown-item');

                        profileMenuItems.forEach((item) => {
                            // Mostrar tooltip al pasar el ratón
                            item.addEventListener('mouseenter', function(event) {
                                const description = this.getAttribute('data-tooltip');
                                const rect = this.getBoundingClientRect();

                                // Configurar el tooltip
                                profileTooltip.textContent = description;
                                profileTooltip.style.backgroundColor = 'rgba(106, 13, 173, 0.9)';
                                profileTooltip.style.left = (rect.right + 15) + 'px';
                                profileTooltip.style.top = (rect.top + rect.height/2 - 20) + 'px';

                                // Configurar el color de la flecha
                                profileTooltip.style.setProperty('--tooltip-color', 'rgba(106, 13, 173, 0.9)');

                                // Mostrar el tooltip
                                profileTooltip.classList.add('visible');
                            });

                            // Ocultar tooltip al quitar el ratón
                            item.addEventListener('mouseleave', function() {
                                profileTooltip.classList.remove('visible');
                            });
                        });
                    });
                </script>
            </div>
        </div>

        <!-- Wrapper for Main Content and Right Sidebar -->
        <div class="main-content-and-sidebar-wrapper">
            <!-- Contenido principal -->
            <div class="main-content">
                <div class="opportunities-header">
                    <h1>Global Opportunities</h1>
                    <p class="opportunities-subtitle">Discover your next career move in the world of sports</p>

                    <div class="opportunities-stats">
                        <div class="stat-item">
                            <span class="stat-value">2,500+</span>
                            <span class="stat-label">Active Opportunities</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">120+</span>
                            <span class="stat-label">Countries</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">6</span>
                            <span class="stat-label">Sports Categories</span>
                        </div>
                    </div>
                </div>

                <div class="search-container">
                    <div class="search-input-container">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="Search opportunities by keyword, club, or location...">
                    </div>
                    <button class="search-button">Search</button>
                </div>

                <div class="filter-tabs">
                    <div class="filter-tabs-row">
                        <button class="filter-tab active" data-filter="all">All</button>
                        <button class="filter-tab" data-filter="trials">Trials</button>
                        <button class="filter-tab" data-filter="contracts">Contracts</button>
                        <button class="filter-tab" data-filter="academies">Academies</button>
                        <button class="filter-tab" data-filter="scholarships">Scholarships</button>
                        <button class="filter-tab" data-filter="coaching">Coaching</button>
                    </div>
                    <div class="filter-tabs-row">
                        <button class="filter-tab" data-filter="training">Training</button>
                        <button class="filter-tab" data-filter="events">Events</button>
                        <button class="filter-tab" data-filter="camps">Camps</button>
                        <button class="filter-tab" data-filter="internships">Internships</button>
                        <button class="filter-tab" data-filter="volunteering">Volunteering</button>
                    </div>
                </div>

                <div class="filter-options">
                    <div class="filter-row">
                        <div class="filter-group">
                            <label>Sport</label>
                            <select>
                                <option>All Sports</option>
                                <option>Football</option>
                                <option>Basketball</option>
                                <option>Volleyball</option>
                                <option>Baseball</option>
                                <option>Cricket</option>
                                <option>Field Hockey</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label>Region</label>
                            <select>
                                <option>Worldwide</option>
                                <option>Europe</option>
                                <option>North America</option>
                                <option>South America</option>
                                <option>Asia</option>
                                <option>Africa</option>
                                <option>Oceania</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label>Level</label>
                            <select>
                                <option>All Levels</option>
                                <option>Professional</option>
                                <option>Semi-Professional</option>
                                <option>Youth</option>
                                <option>Amateur</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label>Age Group</label>
                            <select>
                                <option>All Ages</option>
                                <option>Under 18</option>
                                <option>18-23</option>
                                <option>24-30</option>
                                <option>31+</option>
                            </select>
                        </div>
                    </div>

                    <div class="filter-actions">
                        <button class="reset-button"><i class="fas fa-undo"></i> Reset</button>
                        <button class="apply-button"><i class="fas fa-filter"></i> Apply Filters</button>
                    </div>
                </div>

                <!-- Featured Opportunities Section -->
                <div class="featured-opportunities">
                    <div class="section-header">
                        <h2>Featured Opportunities</h2>
                        <a href="#" class="view-all">View All <i class="fas fa-arrow-right"></i></a>
                    </div>

                    <div class="featured-cards">
                        <!-- Featured Opportunity 1 -->
                        <div class="featured-card">
                            <div class="opportunity-badge premium">Premium</div>
                            <div class="card-image">
                                <img src="https://images.unsplash.com/photo-1522778119026-d647f0596c20?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8c29jY2VyJTIwc3RhZGl1bXxlbnwwfHwwfHw%3D&auto=format&fit=crop&w=600&q=60" alt="Real Madrid Stadium">
                                <div class="card-logo">
                                    <img src="https://upload.wikimedia.org/wikipedia/en/5/56/Real_Madrid_CF.svg" alt="Real Madrid Logo">
                                  </div>
                            </div>
                            <div class="card-content">
                                <h3>International Talent Search Program</h3>
                                <p class="card-organization">Real Madrid C.F.</p>
                                <p class="card-location"><i class="fas fa-map-marker-alt"></i> Madrid, Spain</p>
                                <div class="card-tags">
                                    <span class="tag"><i class="fas fa-futbol"></i> Football</span>
                                    <span class="tag"><i class="fas fa-user-graduate"></i> Youth Academy</span>
                                </div>
                                <p class="card-description">Join one of the world's most prestigious football academies. Real Madrid is searching for exceptional young talent worldwide.</p>
                                <div class="card-actions">
                                    <button class="view-details-btn">View Details</button>
                                    <button class="save-btn"><i class="far fa-bookmark"></i></button>
                                </div>
                            </div>
                        </div>

                        <!-- Featured Opportunity 2 -->
                        <div class="featured-card">
                            <div class="opportunity-badge scholarship">Scholarship</div>
                            <div class="card-image">
                                <img src="https://images.unsplash.com/photo-1504450758481-7338eba7524a?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8YmFza2V0YmFsbCUyMGNvdXJ0fGVufDB8fDB8fA%3D%3D&auto=format&fit=crop&w=600&q=60" alt="UCLA Basketball Court">
                                <div class="card-logo">
                                    <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/0/0d/The_University_of_California_UCLA.svg/1280px-The_University_of_California_UCLA.svg.png" alt="UCLA Logo">
                                </div>
                            </div>
                            <div class="card-content">
                                <h3>International Basketball Scholarship</h3>
                                <p class="card-organization">UCLA Athletics</p>
                                <p class="card-location"><i class="fas fa-map-marker-alt"></i> Los Angeles, USA</p>
                                <div class="card-tags">
                                    <span class="tag"><i class="fas fa-basketball-ball"></i> Basketball</span>
                                    <span class="tag"><i class="fas fa-graduation-cap"></i> Collegiate</span>
                                </div>
                                <p class="card-description">Full athletic scholarships available for international basketball players. Join one of America's most successful collegiate programs.</p>
                                <div class="card-actions">
                                    <button class="view-details-btn">View Details</button>
                                    <button class="save-btn"><i class="far fa-bookmark"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Opportunities Section -->
                <div class="recent-opportunities">
                    <div class="section-header">
                        <h2>Recent Opportunities</h2>
                        <div class="view-controls">
                            <button class="view-control active" data-view="grid"><i class="fas fa-th-large"></i></button>
                            <button class="view-control" data-view="list"><i class="fas fa-list"></i></button>
                        </div>
                    </div>

                    <div class="opportunities-grid">
                        <!-- Opportunity Card 1 -->
                        <div class="opportunity-card">
                            <div class="opportunity-badge trial">Open Trial</div>
                            <div class="card-header">
                                <div class="card-logo">
                                    <img src="https://upload.wikimedia.org/wikipedia/en/7/7a/Manchester_United_FC_crest.svg" alt="Manchester United Logo">
                                </div>
                                <div class="card-title">
                                    <h3>Youth Academy Midfielders Trials</h3>
                                    <p>Manchester United FC</p>
                                </div>
                            </div>
                            <div class="card-body">
                                <p class="card-location"><i class="fas fa-map-marker-alt"></i> Manchester, United Kingdom</p>
                                <div class="card-tags">
                                    <span class="tag football">Football</span>
                                    <span class="tag youth">Youth</span>
                                    <span class="tag position">Midfielder</span>
                                </div>
                                <p class="card-description">Manchester United is looking for talented midfielders to join our youth academy. Open trials for players aged 16-18.</p>
                            </div>
                            <div class="card-footer">
                                <div class="deadline">
                                    <i class="far fa-clock"></i> Deadline: June 15, 2023
                                </div>
                                <button class="apply-now-btn">Apply</button>
                            </div>
                        </div>

                        <!-- Opportunity Card 2 -->
                        <div class="opportunity-card">
                            <div class="opportunity-badge contract">Contract</div>
                            <div class="card-header">
                                <div class="card-logo">
                                    <img src="https://upload.wikimedia.org/wikipedia/en/thumb/0/05/PSG_Logo.svg/1200px-PSG_Logo.svg.png" alt="PSG Logo">
                                </div>
                                <div class="card-title">
                                    <h3>Professional Goalkeeper Position</h3>
                                    <p>Paris Saint-Germain</p>
                                </div>
                            </div>
                            <div class="card-body">
                                <p class="card-location"><i class="fas fa-map-marker-alt"></i> Paris, France</p>
                                <div class="card-tags">
                                    <span class="tag football">Football</span>
                                    <span class="tag professional">Professional</span>
                                    <span class="tag position">Goalkeeper</span>
                                </div>
                                <p class="card-description">PSG is seeking a professional goalkeeper to join the first team squad for the upcoming season.</p>
                            </div>
                            <div class="card-footer">
                                <div class="deadline">
                                    <i class="far fa-clock"></i> Deadline: July 10, 2023
                                </div>
                                <button class="apply-now-btn">Apply</button>
                            </div>
                        </div>

                        <!-- Opportunity Card 3 -->
                        <div class="opportunity-card">
                            <div class="opportunity-badge coaching">Coaching</div>
                            <div class="card-header">
                                <div class="card-logo">
                                    <img src="https://upload.wikimedia.org/wikipedia/en/thumb/e/eb/Manchester_City_FC_badge.svg/1200px-Manchester_City_FC_badge.svg.png" alt="Manchester City Logo">
                                </div>
                                <div class="card-title">
                                    <h3>Youth Academy Coach Position</h3>
                                    <p>Manchester City FC</p>
                                </div>
                            </div>
                            <div class="card-body">
                                <p class="card-location"><i class="fas fa-map-marker-alt"></i> Manchester, United Kingdom</p>
                                <div class="card-tags">
                                    <span class="tag football">Football</span>
                                    <span class="tag coaching">Coaching</span>
                                    <span class="tag youth">Youth</span>
                                </div>
                                <p class="card-description">Manchester City is looking for experienced youth coaches to join our academy coaching staff.</p>
                            </div>
                            <div class="card-footer">
                                <div class="deadline">
                                    <i class="far fa-clock"></i> Deadline: August 5, 2023
                                </div>
                                <button class="apply-now-btn">Apply</button>
                            </div>
                        </div>
                    </div>

                    <div class="load-more">
                        <button class="load-more-btn">Load More <i class="fas fa-sync"></i></button>
                    </div>
                </div>
            </div>
        <!-- Right Sidebar -->
        <div class="sidebar-right">
            <div class="your-applications">
                <h3>Your Applications</h3>

                <div class="application-item">
                    <div class="application-status youth">
                        <div class="status-dot"></div>
                    </div>
                    <div class="application-details">
                        <h4>Youth Development Program</h4>
                        <p>Real Madrid C.F.</p>
                        <span class="application-badge under-review">Under Review</span>
                    </div>
                </div>

                <div class="application-item">
                    <div class="application-status academy">
                        <div class="status-dot"></div>
                    </div>
                    <div class="application-details">
                        <h4>Academy Trials</h4>
                        <p>Liverpool FC</p>
                        <span class="application-badge interview">Interview Scheduled</span>
                    </div>
                </div>

                <div class="application-item">
                    <div class="application-status position">
                        <div class="status-dot"></div>
                    </div>
                    <div class="application-details">
                        <h4>Goalkeeper Position</h4>
                        <p>Chelsea FC</p>
                        <span class="application-badge not-selected">Not Selected</span>
                    </div>
                </div>

                <a href="#" class="view-all-link">View All Applications</a>
            </div>

            <div class="create-alert">
                <h3>Create Alert</h3>
                <p>Get notified when new opportunities match your profile</p>

                <div class="alert-form">
                    <input type="text" placeholder="E.g. Football Goalkeeper" class="alert-input">
                    <button class="create-alert-button">Create Alert</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Botones flotantes -->
    <div class="floating-buttons">
        <button class="chat-button"><i class="fas fa-comment"></i></button>
        <button class="top-button"><i class="fas fa-arrow-up"></i></button>
    </div>

    <!-- Tooltips para los menús -->
    <div id="feature-tooltip" class="js-tooltip"></div>
    <div id="profile-tooltip" class="js-tooltip"></div>

    <!-- Script para la insignia NEW y el menú desplegable de AI Analysis -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Eye icon functionality
            const watchingEyeIcon = document.getElementById('watchingEyeIcon');
            if (watchingEyeIcon) {
                watchingEyeIcon.addEventListener('click', function() {
                    // Create notification for premium feature
                    showNotification('Talent Monitoring System', '5 scouts and clubs are actively monitoring your performance and career development. Complete an AI analysis and subscribe to premium to enhance your visibility to top clubs.');
                });
            }

            // Function to show notifications
            function showNotification(title, message) {
                // Create a notification element
                const notification = document.createElement('div');
                notification.className = 'feature-notification';
                notification.innerHTML = `
                    <div class="notification-content">
                        <i class="fas fa-eye"></i>
                        <div class="notification-text">
                            <strong>${title}</strong>
                            <p>${message}</p>
                        </div>
                        <button class="notification-close"><i class="fas fa-times"></i></button>
                    </div>
                `;

                // Add inline styles for the notification
                notification.style.position = 'fixed';
                notification.style.bottom = '20px';
                notification.style.right = '20px';
                notification.style.backgroundColor = 'white';
                notification.style.borderRadius = '8px';
                notification.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
                notification.style.zIndex = '9999';
                notification.style.overflow = 'hidden';
                notification.style.maxWidth = '350px';
                notification.style.animation = 'slideIn 0.3s forwards';

                // Styles for the notification content
                const notificationContent = notification.querySelector('.notification-content');
                notificationContent.style.display = 'flex';
                notificationContent.style.padding = '15px';
                notificationContent.style.alignItems = 'center';

                // Styles for the icon
                const icon = notification.querySelector('.fas.fa-eye');
                icon.style.color = '#2ecc71';
                icon.style.fontSize = '24px';
                icon.style.marginRight = '15px';
                icon.style.textShadow = '0 0 5px rgba(46, 204, 113, 0.5)';

                // Styles for the text
                const notificationText = notification.querySelector('.notification-text');
                notificationText.style.flex = '1';

                // Styles for the close button
                const closeButton = notification.querySelector('.notification-close');
                closeButton.style.background = 'none';
                closeButton.style.border = 'none';
                closeButton.style.cursor = 'pointer';
                closeButton.style.fontSize = '16px';
                closeButton.style.color = '#666';

                // Add the notification to the DOM
                document.body.appendChild(notification);

                // Add event for closing the notification
                closeButton.addEventListener('click', function() {
                    notification.style.animation = 'slideOut 0.3s forwards';
                    setTimeout(() => {
                        notification.remove();
                    }, 300);
                });

                // Auto-close after 5 seconds
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        notification.style.animation = 'slideOut 0.3s forwards';
                        setTimeout(() => {
                            notification.remove();
                        }, 300);
                    }
                }, 5000);
            }

            // Obtener el enlace de AI Analysis y el menú desplegable
            const aiMenuItem = document.getElementById('aiMenuItem');
            const aiAnalysisLink = aiMenuItem.querySelector('a');
            const aiDropdownMenu = document.getElementById('aiDropdownMenu');

            // Asegurarse de que la insignia NEW siempre sea visible
            const badge = aiAnalysisLink.querySelector('.new-badge');
            if (badge) {
                badge.style.display = 'inline-block';
                badge.style.opacity = '1';
            }

            // Configurar el tooltip dinámico
            const tooltip = document.getElementById('feature-tooltip');

            // Añadir eventos para los elementos del menú desplegable
            const aiMenuItems = aiDropdownMenu.querySelectorAll('li a');

            aiMenuItems.forEach((item, index) => {
                // Mostrar tooltip al pasar el ratón
                item.addEventListener('mouseenter', function(event) {
                    const description = this.getAttribute('data-description');
                    const color = this.getAttribute('data-color');
                    const rect = this.getBoundingClientRect();

                    // Configurar el tooltip
                    tooltip.textContent = description;
                    tooltip.style.backgroundColor = color + 'E6'; // Color con 90% de opacidad
                    tooltip.style.left = (rect.right + 15) + 'px';
                    tooltip.style.top = (rect.top + rect.height/2 - 20) + 'px';

                    // Configurar el color de la flecha
                    tooltip.style.setProperty('--tooltip-color', color + 'E6');

                    // Mostrar el tooltip
                    tooltip.classList.add('visible');
                });

                // Ocultar tooltip al quitar el ratón
                item.addEventListener('mouseleave', function() {
                    tooltip.classList.remove('visible');
                });

                // Click en el elemento del menú
                item.addEventListener('click', function(event) {
                    // Asegurarse de que la insignia NEW permanezca visible
                    const badge = aiAnalysisLink.querySelector('.new-badge');
                    if (badge) {
                        badge.style.display = 'inline-block';
                        badge.style.opacity = '1';
                    }

                    // Si el elemento no tiene un href válido, prevenir la navegación
                    if (this.getAttribute('href') === '#') {
                        event.preventDefault();
                        // Mostrar mensaje de "Próximamente"
                        const featureName = this.querySelector('span').textContent.trim();
                        const featureDescription = this.getAttribute('data-description');
                        showFeatureComingSoon(featureName, featureDescription);
                    }
                });
            });

            // Función para mostrar un mensaje de "Próximamente"
            function showFeatureComingSoon(featureName, featureDescription = '') {
                // Crear un elemento de notificación
                const notification = document.createElement('div');
                notification.className = 'feature-notification';
                notification.innerHTML = `
                    <div class="notification-content">
                        <i class="fas fa-info-circle"></i>
                        <div class="notification-text">
                            <strong>${featureName}</strong>
                            <p>${featureDescription}</p>
                            <p class="coming-soon-text">This feature is coming soon! We're working hard to bring it to you.</p>
                        </div>
                        <button class="notification-close"><i class="fas fa-times"></i></button>
                    </div>
                `;

                // Añadir estilos inline para la notificación
                notification.style.position = 'fixed';
                notification.style.bottom = '20px';
                notification.style.right = '20px';
                notification.style.backgroundColor = 'white';
                notification.style.borderRadius = '8px';
                notification.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
                notification.style.zIndex = '9999';
                notification.style.overflow = 'hidden';
                notification.style.maxWidth = '350px';
                notification.style.animation = 'slideIn 0.3s forwards';

                // Estilos para el contenido de la notificación
                const notificationContent = notification.querySelector('.notification-content');
                notificationContent.style.display = 'flex';
                notificationContent.style.padding = '15px';
                notificationContent.style.alignItems = 'center';

                // Estilos para el icono
                const icon = notification.querySelector('.fas.fa-info-circle');
                icon.style.color = '#6a0dad';
                icon.style.fontSize = '24px';
                icon.style.marginRight = '15px';

                // Estilos para el texto
                const notificationText = notification.querySelector('.notification-text');
                notificationText.style.flex = '1';

                // Estilos para el botón de cierre
                const closeButton = notification.querySelector('.notification-close');
                closeButton.style.background = 'none';
                closeButton.style.border = 'none';
                closeButton.style.cursor = 'pointer';
                closeButton.style.fontSize = '16px';
                closeButton.style.color = '#666';

                // Añadir la notificación al DOM
                document.body.appendChild(notification);

                // Añadir keyframes para la animación
                const style = document.createElement('style');
                style.textContent = `
                    @keyframes slideIn {
                        from { transform: translateX(100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }
                    @keyframes slideOut {
                        from { transform: translateX(0); opacity: 1; }
                        to { transform: translateX(100%); opacity: 0; }
                    }
                `;
                document.head.appendChild(style);

                // Añadir evento para cerrar la notificación
                closeButton.addEventListener('click', function() {
                    notification.style.animation = 'slideOut 0.3s forwards';
                    setTimeout(() => {
                        notification.remove();
                    }, 300);
                });

                // Cerrar automáticamente después de 5 segundos
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        notification.style.animation = 'slideOut 0.3s forwards';
                        setTimeout(() => {
                            notification.remove();
                        }, 300);
                    }
                }, 5000);
            }

            // Scroll to top button functionality
            const topButton = document.querySelector('.top-button');
            if (topButton) {
                topButton.addEventListener('click', function() {
                    window.scrollTo({
                        top: 0,
                        behavior: 'smooth'
                    });
                });
            }

            // Chat button functionality
            const chatButton = document.querySelector('.chat-button');
            if (chatButton) {
                chatButton.addEventListener('click', function() {
                    showNotification('Chat Feature', 'Chat functionality is coming soon! Connect with other athletes and professionals.');
                });
            }
        });
    </script>
</body>
</html>