:root {
    --vscode-bg: #1e1e1e;
    --vscode-sidebar: #252526;
    --vscode-highlight: #2a2d2e;
    --vscode-text: #cccccc;
    --vscode-text-light: #a0a0a0;
    --vscode-accent: #0e639c;
    --vscode-button: #0e639c;
    --vscode-button-hover: #1177bb;
    --vscode-border: #3c3c3c;
    --chat-user-bg: #2b5797;
    --chat-assistant-bg: #3c3c3c;
}

/* Theme Classes */
body.theme-light {
    --main-bg: #f5f5f5;
    --sidebar-bg: #e0e0e0;
    --chat-bg: #f0f0f0;
    --header-bg: #e8e8e8;
    --text-color: #333333;
    --accent-color: #4caf50;
    --message-user-bg: #e8f5e9;
    --message-assistant-bg: #f5f5f5;
    --border-color: rgba(0, 0, 0, 0.1);
}

body.theme-dark {
    --main-bg: #121212;
    --sidebar-bg: #1a1a1a;
    --chat-bg: #1e1e1e;
    --header-bg: #1a1a1a;
    --text-color: #e0e0e0;
    --accent-color: #4caf50;
    --message-user-bg: #1e3a2f;
    --message-assistant-bg: #2a2a2a;
    --border-color: rgba(255, 255, 255, 0.1);
}

body.theme-current {
    --main-bg: #1e5b3d;
    --sidebar-bg: #174d33;
    --chat-bg: #2a6e4a;
    --header-bg: #235e3f;
    --text-color: #ffffff;
    --accent-color: #4dbd7d;
    --message-user-bg: #3c8a5e;
    --message-assistant-bg: #235e3f;
    --border-color: rgba(255, 255, 255, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background-color: var(--vscode-bg);
    color: var(--vscode-text);
    line-height: 1.6;
    height: 100vh;
    overflow: hidden;
}

.main-container {
    display: flex;
    height: 100vh;
    width: 100%;
}

/* Chat Section - 30% width (doubled from 15%) */
.chat-section {
    width: 30%;
    display: flex;
    flex-direction: column;
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    background-color: #2a6e4a; /* Vibrant modern green */
}

.chat-header {
    padding: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
    background-color: #235e3f; /* Slightly darker than the main green */
}

.chat-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
    color: #e0e0e0;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 18px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    background-color: #2a6e4a; /* Matching the chat section */
}

.message {
    display: flex;
    flex-direction: column;
    max-width: 95%;
    padding: 10px 12px;
    border-radius: 8px;
    position: relative;
    font-size: 14px;
}

.message.user {
    align-self: flex-end;
    background-color: #3c8a5e; /* Brighter vibrant green for user messages */
    border-radius: 12px 12px 0 12px;
}

.message.assistant {
    align-self: flex-start;
    background-color: #235e3f; /* Darker green for assistant messages */
    border-radius: 12px 12px 12px 0;
}

.message-content {
    word-break: break-word;
}

.message-content p {
    margin-bottom: 8px;
}

.message-content p:last-child {
    margin-bottom: 0;
}

.message-time {
    font-size: 10px;
    color: var(--vscode-text-light);
    align-self: flex-end;
    margin-top: 4px;
}

.chat-input-container {
    display: flex;
    padding: 12px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background-color: #235e3f; /* Matching header */
}

#chatInput {
    flex: 1;
    background-color: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 18px;
    color: #e0e0e0;
    padding: 10px 15px;
    font-family: inherit;
    font-size: 14px;
    resize: none;
    max-height: 100px;
    overflow-y: auto;
}

#chatInput:focus {
    outline: none;
    border-color: #4dbd7d;
    box-shadow: 0 0 0 1px rgba(77, 189, 125, 0.3);
}

#sendMessageBtn {
    background-color: #4dbd7d; /* Matching metric value color */
    color: white;
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    margin-left: 10px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
}

#sendMessageBtn:hover {
    background-color: #5ecf8e; /* Brighter on hover */
    transform: scale(1.05);
}

/* Tools section */
.tools-container {
    padding: 12px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    background-color: #235e3f; /* Matching header and input */
}

.tools-button {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    background-color: #3c8a5e; /* Matching user message color */
    color: white;
    border: none;
    border-radius: 18px;
    padding: 10px;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 14px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.tools-button:hover {
    background-color: #4a9c6e;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.tools-dropdown {
    position: absolute;
    bottom: 100%;
    left: 12px;
    right: 12px;
    background-color: #2a6e4a; /* Matching chat section */
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    display: none;
    flex-direction: column;
    z-index: 100;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    margin-bottom: 8px;
}

.tools-dropdown.active {
    display: flex;
}

.tool-item {
    width: 100%;
    background: none;
    border: none;
    padding: 12px 15px;
    text-align: left;
    color: #e0e0e0;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 14px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.tool-item:last-child {
    border-bottom: none;
}

.tool-item:hover {
    background-color: #3c8a5e; /* Matching user message color */
}

.tool-item i {
    width: 20px;
    margin-right: 10px;
    text-align: center;
    color: #4dbd7d;
}

/* Content Section - 70% width (adjusted for chat section) */
.content-section {
    width: 70%;
    height: 100%;
    display: flex;
    background-color: #1e5b3d; /* Matching the main content area */
}

/* Main Content Area - 85.9% of the content section (increased to compensate for sidebar reduction) */
.main-content-area {
    width: 85.9%;
    height: 100%;
    overflow: auto;
    background-color: #1e5b3d; /* Vibrant modern green background */
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    padding: 25px;
}

/* Title Section */
.title-section {
    width: 100%;
    margin-bottom: 20px;
    text-align: center;
}

.title-section h1 {
    color: #ffffff;
    font-size: 2.5rem;
    font-weight: bold;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    letter-spacing: 1px;
}



/* Sidebar Area - 14.1% of the content section (reduced by 6%) */
.sidebar-area {
    width: 14.1%;
    height: 100%;
    overflow: auto;
    background-color: #174d33; /* Slightly darker green for sidebar */
    padding: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* Sidebar Icons */
.sidebar-icons {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
}

.sidebar-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background-color: #235e3f;
    color: white;
    margin-bottom: 15px;
    text-decoration: none;
    transition: all 0.2s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    cursor: pointer;
}

.sidebar-icon:hover {
    background-color: #3c8a5e;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.sidebar-icon i {
    font-size: 20px;
}

/* Theme Toggle and Popup */
.theme-toggle {
    position: relative;
}

.theme-popup {
    position: absolute;
    left: 70px;
    top: 60px;
    background-color: #174d33;
    border-radius: 8px;
    padding: 10px;
    width: 120px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    z-index: 100;
    display: none;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.theme-popup.active {
    display: block;
}

.theme-option {
    display: flex;
    align-items: center;
    padding: 8px;
    cursor: pointer;
    border-radius: 5px;
    transition: background-color 0.2s;
}

.theme-option:hover {
    background-color: #235e3f;
}

.theme-color {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-right: 10px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.current-theme {
    background: linear-gradient(135deg, #1e5b3d 0%, #2a6e4a 100%);
}

.light-theme {
    background: linear-gradient(135deg, #e0f2f1 0%, #b2dfdb 100%);
}

.dark-theme {
    background: linear-gradient(135deg, #263238 0%, #37474f 100%);
}

.theme-option span {
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
}



/* Video Container */
.video-container {
    background-color: rgba(0, 0, 0, 0.15);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
    margin-bottom: 30px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.video-player {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    background-color: #000;
    overflow: hidden;
}

.main-video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: contain;
    z-index: 10;
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 20;
    cursor: pointer;
    transition: opacity 0.3s ease;
}

.video-notification {
    position: absolute;
    bottom: 20px;
    background-color: rgba(77, 189, 125, 0.8);
    color: white;
    padding: 10px 20px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    transition: opacity 0.5s ease;
    z-index: 30;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.05);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 0.8;
    }
}

.play-button {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background-color: rgba(60, 138, 94, 0.8); /* Matching user message color */
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.play-button:hover {
    background-color: rgba(74, 156, 110, 1); /* Brighter on hover */
    transform: scale(1.05);
}

.play-button i {
    color: white;
    font-size: 28px;
    margin-left: 5px; /* Slight offset for play icon */
}



/* Chat video preview */
.chat-video-preview {
    margin-top: 8px;
    border-radius: 4px;
    overflow: hidden;
    width: 100%;
    max-width: 200px;
}

.chat-video-preview video {
    display: block;
    background-color: #000;
    width: 100%;
}

/* Analysis Preview Styles */
.analysis-preview, .quick-analysis {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 12px;
    padding: 16px;
    margin: 12px 0;
}

.quick-analysis ul {
    margin: 8px 0;
    padding-left: 16px;
}

.quick-analysis li {
    margin: 4px 0;
    color: var(--text-color);
}

/* Dark theme adjustments */
body.theme-dark .analysis-preview,
body.theme-dark .quick-analysis {
    background: rgba(59, 130, 246, 0.15);
    border-color: rgba(59, 130, 246, 0.4);
}

/* Dark Theme for Top Sidebar */
body.theme-dark .top-sidebar-area {
    background-color: #1a1a1a;
    border-color: #333;
}

body.theme-dark .top-sidebar-icon {
    background-color: #2d2d2d;
    border-color: #444;
}

body.theme-dark .top-sidebar-icon:hover {
    background-color: #3b82f6;
}

/* Dark Theme for Expanded Sidebar */
body.theme-dark .sidebar-area {
    background-color: #1a1a1a;
}

body.theme-dark .sidebar-icon {
    background-color: #2d2d2d;
    border-color: #444;
}

body.theme-dark .sidebar-icon:hover {
    background-color: #3b82f6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content-area {
        width: 100%;
        padding: 15px;
    }
    
    .sidebar-area {
        width: 100%;
        height: auto;
        flex-direction: row;
        justify-content: center;
        padding: 15px;
    }
    
    .sidebar-icons {
        flex-direction: row;
        gap: 15px;
    }
    
    .sidebar-icon {
        margin-bottom: 0;
        width: 50px;
        height: 50px;
    }
    
    .top-sidebar-area {
        height: auto;
        padding: 15px;
    }
    
    .top-sidebar-icon {
        width: 50px;
        height: 50px;
    }
    
    .top-sidebar-icon i {
        font-size: 20px;
    }
}