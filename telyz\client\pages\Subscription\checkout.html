<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Telyz - Checkout</title>
    <link rel="stylesheet" href="checkout.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Stripe JS -->
    <script src="https://js.stripe.com/v3/"></script>
    <!-- PayPal JS -->
    <script src="https://www.paypal.com/sdk/js?client-id=YOUR_PAYPAL_CLIENT_ID&currency=USD"></script>
</head>
<body>
    <div class="checkout-container">
        <!-- Header -->
        <header class="checkout-header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-arrow-left back-btn" onclick="goBack()"></i>
                    <h1>Telyz</h1>
                </div>
                <div class="security-badge">
                    <i class="fas fa-shield-alt"></i>
                    <span>Secure Checkout</span>
                </div>
            </div>
        </header>

        <!-- Progress Bar -->
        <div class="progress-container">
            <div class="progress-bar">
                <div class="progress-step active" data-step="1">
                    <div class="step-number">1</div>
                    <div class="step-label">Plan Confirmation</div>
                </div>
                <div class="progress-step" data-step="2">
                    <div class="step-number">2</div>
                    <div class="step-label">Payment Details</div>
                </div>
                <div class="progress-step" data-step="3">
                    <div class="step-number">3</div>
                    <div class="step-label">Final Review</div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="checkout-main">
            <!-- Step 1: Plan Confirmation -->
            <div class="step-content active" id="step-1">
                <div class="step-container">
                    <div class="step-left">
                        <div class="step-header">
                            <h2>Confirm Your Plan</h2>
                            <p>Review and customize your subscription</p>
                        </div>

                        <!-- Selected Plan -->
                        <div class="selected-plan">
                            <div class="plan-card">
                                <div class="plan-header">
                                    <div class="plan-icon">
                                        <i class="fas fa-star"></i>
                                    </div>
                                    <div class="plan-info">
                                        <h3 id="selected-plan-name">Pro Player</h3>
                                        <p id="selected-plan-category">Player & Amateur</p>
                                    </div>
                                </div>
                                <div class="plan-price">
                                    <span class="currency">$</span>
                                    <span class="amount" id="selected-plan-price">29</span>
                                    <span class="period" id="selected-plan-period">/month</span>
                                </div>
                            </div>
                        </div>

                        <!-- Billing Cycle -->
                        <div class="billing-cycle">
                            <h3>Choose Billing Cycle</h3>
                            <div class="cycle-options">
                                <div class="cycle-option active" data-cycle="monthly">
                                    <div class="cycle-header">
                                        <span class="cycle-name">Monthly</span>
                                        <span class="cycle-price">$<span id="monthly-price">29</span>/month</span>
                                    </div>
                                    <div class="cycle-description">Billed monthly</div>
                                </div>
                                <div class="cycle-option" data-cycle="yearly">
                                    <div class="cycle-header">
                                        <span class="cycle-name">Yearly</span>
                                        <span class="cycle-price">$<span id="yearly-price">290</span>/year</span>
                                        <span class="discount-badge">Save 17%</span>
                                    </div>
                                    <div class="cycle-description">Billed annually - 2 months free!</div>
                                </div>
                            </div>
                        </div>

                        <!-- Add-ons -->
                        <div class="addons-section">
                            <h3>Optional Add-ons</h3>
                            <div class="addons-list">
                                <div class="addon-item">
                                    <div class="addon-checkbox">
                                        <input type="checkbox" id="addon-analytics" data-price="9">
                                        <label for="addon-analytics">
                                            <div class="addon-info">
                                                <div class="addon-name">Advanced Analytics</div>
                                                <div class="addon-description">Detailed performance insights and reports</div>
                                            </div>
                                            <div class="addon-price">+$9/month</div>
                                        </label>
                                    </div>
                                </div>
                                <div class="addon-item">
                                    <div class="addon-checkbox">
                                        <input type="checkbox" id="addon-coaching" data-price="19">
                                        <label for="addon-coaching">
                                            <div class="addon-info">
                                                <div class="addon-name">1-on-1 Coaching Session</div>
                                                <div class="addon-description">Monthly personal coaching session</div>
                                            </div>
                                            <div class="addon-price">+$19/month</div>
                                        </label>
                                    </div>
                                </div>
                                <div class="addon-item">
                                    <div class="addon-checkbox">
                                        <input type="checkbox" id="addon-priority" data-price="5">
                                        <label for="addon-priority">
                                            <div class="addon-info">
                                                <div class="addon-name">Priority Support</div>
                                                <div class="addon-description">24/7 priority customer support</div>
                                            </div>
                                            <div class="addon-price">+$5/month</div>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="step-right">
                        <div class="order-summary">
                            <h3>Order Summary</h3>
                            <div class="summary-item">
                                <span>Plan</span>
                                <span id="summary-plan">$29/month</span>
                            </div>
                            <div class="summary-addons" id="summary-addons">
                                <!-- Add-ons will be populated here -->
                            </div>
                            <div class="summary-discount" id="summary-discount" style="display: none;">
                                <span>Yearly Discount</span>
                                <span class="discount-amount">-$58</span>
                            </div>
                            <div class="summary-divider"></div>
                            <div class="summary-total">
                                <span>Total</span>
                                <span id="summary-total">$29/month</span>
                            </div>
                            <button class="continue-btn" onclick="nextStep()">Continue to Payment</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 2: Payment Details -->
            <div class="step-content" id="step-2">
                <div class="step-container">
                    <div class="step-left">
                        <div class="step-header">
                            <h2>Payment Details</h2>
                            <p>Choose your payment method and enter details</p>
                        </div>

                        <!-- Payment Methods -->
                        <div class="payment-methods">
                            <h3>Payment Method</h3>
                            <div class="payment-options">
                                <div class="payment-option active" data-method="card">
                                    <div class="payment-header">
                                        <i class="fas fa-credit-card"></i>
                                        <span>Credit/Debit Card</span>
                                    </div>
                                    <div class="card-types">
                                        <img src="https://img.icons8.com/color/32/visa.png" alt="Visa">
                                        <img src="https://img.icons8.com/color/32/mastercard.png" alt="Mastercard">
                                        <img src="https://img.icons8.com/color/32/wise.png" alt="Wise">
                                    </div>
                                </div>
                                <div class="payment-option" data-method="paypal">
                                    <div class="payment-header">
                                        <i class="fab fa-paypal"></i>
                                        <span>PayPal</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Card Payment Form -->
                        <div class="payment-form" id="card-form">
                            <div class="form-group">
                                <label for="card-number">Card Number</label>
                                <div class="card-input">
                                    <div id="card-number-element"></div>
                                    <div class="card-type-icon" id="card-type-icon"></div>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="card-expiry">Expiry Date</label>
                                    <div id="card-expiry-element"></div>
                                </div>
                                <div class="form-group">
                                    <label for="card-cvc">CVC</label>
                                    <div id="card-cvc-element"></div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="cardholder-name">Cardholder Name</label>
                                <input type="text" id="cardholder-name" placeholder="John Doe">
                            </div>
                        </div>

                        <!-- PayPal Form -->
                        <div class="payment-form" id="paypal-form" style="display: none;">
                            <div class="paypal-container">
                                <div id="paypal-button-container"></div>
                            </div>
                        </div>

                        <!-- Billing Address -->
                        <div class="billing-address">
                            <h3>Billing Address</h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="first-name">First Name</label>
                                    <input type="text" id="first-name" placeholder="John">
                                </div>
                                <div class="form-group">
                                    <label for="last-name">Last Name</label>
                                    <input type="text" id="last-name" placeholder="Doe">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="email">Email</label>
                                <input type="email" id="email" placeholder="<EMAIL>">
                            </div>
                            <div class="form-group">
                                <label for="address">Address</label>
                                <input type="text" id="address" placeholder="123 Main Street">
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="city">City</label>
                                    <input type="text" id="city" placeholder="New York">
                                </div>
                                <div class="form-group">
                                    <label for="postal-code">Postal Code</label>
                                    <input type="text" id="postal-code" placeholder="10001">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="country">Country</label>
                                <select id="country">
                                    <option value="US">United States</option>
                                    <option value="CA">Canada</option>
                                    <option value="GB">United Kingdom</option>
                                    <option value="AU">Australia</option>
                                    <option value="DE">Germany</option>
                                    <option value="FR">France</option>
                                    <option value="ES">Spain</option>
                                    <option value="IT">Italy</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="step-right">
                        <div class="order-summary">
                            <h3>Order Summary</h3>
                            <div class="summary-item">
                                <span>Plan</span>
                                <span id="summary-plan-2">$29/month</span>
                            </div>
                            <div class="summary-addons" id="summary-addons-2">
                                <!-- Add-ons will be populated here -->
                            </div>
                            <div class="summary-discount" id="summary-discount-2" style="display: none;">
                                <span>Yearly Discount</span>
                                <span class="discount-amount">-$58</span>
                            </div>
                            <div class="summary-divider"></div>
                            <div class="summary-total">
                                <span>Total</span>
                                <span id="summary-total-2">$29/month</span>
                            </div>
                            <div class="security-info">
                                <div class="security-item">
                                    <i class="fas fa-lock"></i>
                                    <span>256-bit SSL encryption</span>
                                </div>
                                <div class="security-item">
                                    <i class="fas fa-shield-alt"></i>
                                    <span>PCI DSS compliant</span>
                                </div>
                            </div>
                            <button class="continue-btn" onclick="nextStep()" id="payment-btn">Review Order</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 3: Final Review -->
            <div class="step-content" id="step-3">
                <div class="step-container">
                    <div class="step-left">
                        <div class="step-header">
                            <h2>Final Review</h2>
                            <p>Review your order before completing the purchase</p>
                        </div>

                        <!-- Order Details -->
                        <div class="order-details">
                            <div class="detail-section">
                                <h3>Plan Details</h3>
                                <div class="detail-item">
                                    <span>Plan:</span>
                                    <span id="final-plan-name">Pro Player</span>
                                </div>
                                <div class="detail-item">
                                    <span>Billing Cycle:</span>
                                    <span id="final-billing-cycle">Monthly</span>
                                </div>
                                <div class="detail-item">
                                    <span>Price:</span>
                                    <span id="final-plan-price">$29/month</span>
                                </div>
                            </div>

                            <div class="detail-section" id="final-addons-section" style="display: none;">
                                <h3>Add-ons</h3>
                                <div id="final-addons-list">
                                    <!-- Add-ons will be populated here -->
                                </div>
                            </div>

                            <div class="detail-section">
                                <h3>Payment Method</h3>
                                <div class="detail-item">
                                    <span>Method:</span>
                                    <span id="final-payment-method">Credit Card</span>
                                </div>
                                <div class="detail-item" id="final-card-info">
                                    <span>Card:</span>
                                    <span>**** **** **** 1234</span>
                                </div>
                            </div>

                            <div class="detail-section">
                                <h3>Billing Information</h3>
                                <div class="detail-item">
                                    <span>Name:</span>
                                    <span id="final-customer-name">John Doe</span>
                                </div>
                                <div class="detail-item">
                                    <span>Email:</span>
                                    <span id="final-customer-email"><EMAIL></span>
                                </div>
                                <div class="detail-item">
                                    <span>Address:</span>
                                    <span id="final-customer-address">123 Main Street, New York, 10001</span>
                                </div>
                            </div>
                        </div>

                        <!-- Terms -->
                        <div class="terms-section">
                            <div class="checkbox-group">
                                <input type="checkbox" id="terms-checkbox" required>
                                <label for="terms-checkbox">
                                    I agree to the <a href="#" target="_blank">Terms of Service</a> and <a href="#" target="_blank">Privacy Policy</a>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="step-right">
                        <div class="order-summary">
                            <h3>Final Total</h3>
                            <div class="summary-item">
                                <span>Plan</span>
                                <span id="summary-plan-3">$29/month</span>
                            </div>
                            <div class="summary-addons" id="summary-addons-3">
                                <!-- Add-ons will be populated here -->
                            </div>
                            <div class="summary-discount" id="summary-discount-3" style="display: none;">
                                <span>Yearly Discount</span>
                                <span class="discount-amount">-$58</span>
                            </div>
                            <div class="summary-divider"></div>
                            <div class="summary-total">
                                <span>Total</span>
                                <span id="summary-total-3">$29/month</span>
                            </div>
                            <div class="money-back-guarantee">
                                <i class="fas fa-undo"></i>
                                <span>30-day money-back guarantee</span>
                            </div>
                            <button class="complete-btn" onclick="completeOrder()" id="complete-order-btn">
                                <i class="fas fa-lock"></i>
                                Complete Order
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Success Modal -->
    <div class="modal" id="success-modal">
        <div class="modal-content">
            <div class="success-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <h2>Payment Successful!</h2>
            <p>Welcome to Telyz! Your subscription has been activated.</p>
            <div class="modal-actions">
                <button class="primary-btn" onclick="goToDashboard()">Go to Dashboard</button>
                <button class="secondary-btn" onclick="closeModal()">Close</button>
            </div>
        </div>
    </div>

    <script src="checkout.js"></script>
</body>
</html>