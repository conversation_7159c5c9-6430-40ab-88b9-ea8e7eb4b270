/* Base styles from template */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: '<PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: #f0f2f5;
    color: #333;
}

.container {
    display: flex;
    min-height: 100vh;
    max-width: 1050px;
    margin: 0 auto;
    background-color: #f0f2f5;
}

/* ================ SIDEBAR STYLES ================ */
.sidebar-left {
    background-color: #f0f2f5;
    padding: 15px 0;
    position: sticky;
    top: 0;
    height: 100vh;
    width: 270px;
    overflow-y: auto;
}

.logo {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

.logo-link {
    text-decoration: none;
    cursor: pointer;
    transition: transform 0.2s ease;
    display: block;
}

.logo-link:hover {
    transform: scale(1.05);
}

.logo-container {
    width: 40px;
    height: 40px;
    background-color: #6a0dad;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(106, 13, 173, 0.3);
}

.sidebar-menu {
    padding: 0 10px;
    max-height: calc(100vh - 100px);
    width: 100%;
}

.sidebar-menu ul {
    list-style: none;
}

.sidebar-menu > ul > li {
    margin: 4px 0;
    padding: 8px 5px;
    border-radius: 10px;
    transition: all 0.3s;
}

.sidebar-menu li:hover {
    background-color: rgba(106, 13, 173, 0.05);
}

.has-submenu {
    position: relative;
}

.submenu {
    display: none;
    list-style: none;
    padding-left: 0;
    margin-top: 5px;
    margin-bottom: 5px;
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 8px;
    overflow: visible;
    max-height: 0;
    transition: max-height 0.3s ease-in-out;
    width: 100%;
    min-width: 260px;
}

.has-submenu:hover .submenu {
    display: block;
    max-height: 400px;
}

.submenu li {
    margin: 0;
    padding: 8px 12px 8px 5px;
    min-width: 240px;
    position: relative;
}

.submenu a {
    font-size: 14px;
    padding: 4px 0;
    white-space: normal;
    overflow: visible;
    text-overflow: clip;
    display: flex;
    align-items: center;
    width: 100%;
    line-height: 1.4;
}

.submenu i {
    font-size: 16px;
    margin-right: 10px;
    width: 20px;
    text-align: center;
    display: inline-flex;
    justify-content: center;
    align-items: center;
}

.ai-submenu {
    overflow: visible !important;
    width: 260px;
    padding: 8px 0;
}

.ai-submenu li {
    overflow: visible;
    padding: 12px 15px 12px 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.03);
}

.ai-submenu li:last-child {
    border-bottom: none;
}

.ai-submenu a {
    align-items: flex-start;
}

.ai-submenu a span {
    padding-top: 2px;
    font-weight: 500;
}

.ai-submenu i {
    font-size: 18px;
    margin-right: 12px;
}

/* Colores específicos para cada opción en el menú de AI Analysis */
.ai-submenu li:nth-child(1) i { color: #FF5722; }
.ai-submenu li:nth-child(2) i { color: #4CAF50; }
.ai-submenu li:nth-child(3) i { color: #2196F3; }
.ai-submenu li:nth-child(4) i { color: #FFC107; }
.ai-submenu li:nth-child(5) i { color: #9C27B0; }

/* Colores específicos para cada deporte */
.submenu li:nth-child(1) i { color: #8e44ad; }
.submenu li:nth-child(2) i { color: #9b59b6; }
.submenu li:nth-child(3) i { color: #6a0dad; }
.submenu li:nth-child(4) i { color: #5d3fd3; }
.submenu li:nth-child(5) i { color: #7d3c98; }
.submenu li:nth-child(6) i { color: #a569bd; }

.sidebar-menu a {
    text-decoration: none;
    color: #4a0080;
    display: flex;
    align-items: center;
    font-size: 16px;
    padding: 6px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 500;
    transition: all 0.3s;
    position: relative;
}

.sidebar-menu a:hover {
    background-color: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
    transform: translateX(2px);
}

.sidebar-menu i {
    margin-right: 15px;
    font-size: 20px;
    width: 25px;
    text-align: center;
    color: #6a0dad;
}

.badge {
    background-color: #ff4757;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 8px;
    font-weight: 600;
}

.new-badge {
    background: linear-gradient(45deg, #ff6b6b, #ff4757);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* User profile sidebar */
.user-profile-sidebar {
    display: flex;
    align-items: center;
    margin-top: 23px;
    padding: 12px 18px;
    border-radius: 10px;
    background-color: rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
}

.user-profile-sidebar:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.profile-pic-small {
    width: 46px;
    height: 46px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 12px;
    border: 2px solid #6a0dad;
}

.profile-pic-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-info-sidebar {
    flex: 1;
}

.profile-info-sidebar span:first-child {
    display: block;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.profile-info-sidebar .teams-clubs {
    font-size: 12px;
    color: #666;
    margin-top: 2px;
}

.profile-dropdown-toggle {
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color 0.2s;
    color: #666;
}

.profile-dropdown-toggle:hover {
    background-color: rgba(0, 0, 0, 0.1);
    color: #333;
}

.profile-dropdown-toggle i {
    font-size: 20px;
    color: #444;
}

.profile-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.2);
    width: 220px;
    z-index: 1000;
    overflow: hidden;
    display: none;
    border: 1px solid #eee;
    margin-top: 6px;
    max-height: 0;
    opacity: 0;
    transition: max-height 0.3s ease, opacity 0.3s ease;
    visibility: hidden;
    text-align: left;
}

.profile-dropdown-menu.show {
    display: block;
    max-height: 400px;
    opacity: 1;
    overflow-y: auto;
    z-index: 1001;
    visibility: visible;
}

.profile-dropdown-item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 14px 25px;
    text-decoration: none;
    color: #333;
    transition: all 0.2s ease;
    font-size: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.03);
    position: relative;
}

.profile-dropdown-item:last-child {
    border-bottom: none;
}

.profile-dropdown-item:hover {
    background-color: #f5f5f5;
    color: #6a0dad;
}

.profile-dropdown-item i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
    color: #6a0dad;
    font-size: 17px;
    display: inline-block;
}

.dropdown-divider {
    height: 1px;
    background-color: #e0e0e0;
    margin: 8px 0;
}

.logout-item {
    color: #e74c3c;
}

.logout-item:hover {
    background-color: rgba(231, 76, 60, 0.05);
    color: #e74c3c;
}

.js-tooltip {
    position: fixed;
    width: 220px;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px;
    border-radius: 6px;
    font-size: 12px;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
    z-index: 1001;
    line-height: 1.4;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    pointer-events: none;
    text-align: left;
}

.js-tooltip::before {
    content: "";
    position: absolute;
    top: 50%;
    left: -10px;
    transform: translateY(-50%);
    border-width: 5px;
    border-style: solid;
    border-color: transparent var(--tooltip-color, rgba(0, 0, 0, 0.8)) transparent transparent;
}

.js-tooltip.visible {
    opacity: 1;
    visibility: visible;
}

/* ================ MAIN CONTENT ================ */
.main-content {
    flex: 1;
    padding: 20px;
    background-color: white;
    border-radius: 15px;
    margin: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
    max-height: calc(100vh - 40px);
}

/* ================ PRIVACY HEADER ================ */
.privacy-header {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #f0f2f5;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-title h1 {
    color: #6a0dad;
    font-size: 28px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-title h1 i {
    color: #6a0dad;
    margin-right: 10px;
}

.header-title p {
    color: #666;
    font-size: 16px;
    margin: 0;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

.last-updated {
    background: #f8f9fa;
    padding: 8px 16px;
    border-radius: 20px;
    border: 1px solid #e9ecef;
    font-size: 14px;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
}

.last-updated i {
    color: #6a0dad;
}

.download-policy-btn {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.download-policy-btn:hover {
    background: linear-gradient(135deg, #229954, #27ae60);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
}

/* ================ PRIVACY NAVIGATION ================ */
.privacy-navigation {
    background: linear-gradient(135deg, #6a0dad, #8b4bcf);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 25px;
    border: 1px solid #6a0dad;
    box-shadow: 0 4px 15px rgba(106, 13, 173, 0.2);
}

.nav-header h3 {
    color: white;
    font-size: 18px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.nav-header h3 i {
    color: white;
}

.nav-links {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.nav-link {
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 10px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    backdrop-filter: blur(10px);
    text-decoration: none;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.nav-link.active {
    background: white;
    color: #6a0dad;
    border-color: white;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    font-weight: 600;
}

.nav-link.active i {
    color: #6a0dad;
}

/* ================ PRIVACY CONTENT ================ */
.privacy-content {
    background: #f8f9fa;
    border-radius: 10px;
    border: 1px solid #e9ecef;
    overflow: hidden;
}

.privacy-section {
    display: none;
    padding: 30px;
    background: white;
    border-bottom: 1px solid #e9ecef;
}

.privacy-section.active {
    display: block;
}

.privacy-section:last-child {
    border-bottom: none;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f0f2f5;
}

.section-header h2 {
    color: #2c3e50;
    font-size: 24px;
    display: flex;
    align-items: center;
    gap: 12px;
    margin: 0;
}

.section-header h2 i {
    color: #6a0dad;
    font-size: 22px;
}

.section-meta {
    display: flex;
    align-items: center;
    gap: 15px;
}

.reading-time {
    background: #e8f5e8;
    color: #27ae60;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 5px;
}

.reading-time i {
    font-size: 10px;
}

.section-content {
    line-height: 1.6;
}

.section-intro {
    font-size: 16px;
    color: #5d6d7e;
    margin-bottom: 25px;
    font-weight: 400;
    line-height: 1.6;
}

/* ================ INFO CARDS ================ */
.info-card {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    padding: 25px;
    margin: 25px 0;
    border-left: 4px solid #6a0dad;
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #6a0dad, #8b4bcf);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.card-icon i {
    color: white;
    font-size: 24px;
}

.card-content h4 {
    color: #2c3e50;
    font-size: 18px;
    margin-bottom: 10px;
    font-weight: 600;
}

.card-content p {
    color: #5d6d7e;
    margin: 0;
    line-height: 1.6;
}

/* ================ KEY POINTS ================ */
.key-points {
    background: #fff;
    border-radius: 10px;
    padding: 25px;
    margin: 25px 0;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.key-points h4 {
    color: #2c3e50;
    font-size: 18px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.key-points ul {
    list-style: none;
    padding: 0;
}

.key-points li {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 12px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.key-points li:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.key-points li::before {
    content: "✓";
    color: #27ae60;
    font-weight: bold;
    font-size: 16px;
    margin-top: 2px;
}

.key-points strong {
    color: #6a0dad;
}

/* ================ IMPORTANT NOTICE ================ */
.important-notice {
    background: linear-gradient(135deg, #fff9c4, #fff3cd);
    border: 1px solid #ffeaa7;
    border-radius: 10px;
    padding: 20px;
    margin: 25px 0;
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.important-notice i {
    color: #f39c12;
    font-size: 24px;
    margin-top: 2px;
}

.important-notice p {
    margin: 0;
    color: #2c3e50;
    line-height: 1.6;
}

.important-notice strong {
    color: #d68910;
}

/* ================ DATA TYPES ================ */
.data-types {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin: 25px 0;
}

.data-type-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.data-type-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.data-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    font-size: 24px;
    color: white;
}

.data-type-card:nth-child(1) .data-icon {
    background: linear-gradient(135deg, #3498db, #2980b9);
}

.data-type-card:nth-child(2) .data-icon {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.data-type-card:nth-child(3) .data-icon {
    background: linear-gradient(135deg, #f39c12, #e67e22);
}

.data-type-card:nth-child(4) .data-icon {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

.data-type-card h4 {
    color: #2c3e50;
    font-size: 18px;
    margin-bottom: 15px;
    font-weight: 600;
}

.data-type-card ul {
    list-style: none;
    padding: 0;
}

.data-type-card li {
    color: #5d6d7e;
    margin-bottom: 8px;
    padding-left: 20px;
    position: relative;
    line-height: 1.5;
}

.data-type-card li::before {
    content: "•";
    color: #6a0dad;
    font-weight: bold;
    position: absolute;
    left: 0;
}

/* ================ COLLECTION METHODS ================ */
.collection-methods {
    margin: 30px 0;
}

.collection-methods h4 {
    color: #2c3e50;
    font-size: 18px;
    margin-bottom: 20px;
}

.method-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.method-item {
    background: white;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.method-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.method-item i {
    font-size: 32px;
    color: #6a0dad;
    margin-bottom: 15px;
}

.method-item h5 {
    color: #2c3e50;
    font-size: 16px;
    margin-bottom: 10px;
    font-weight: 600;
}

.method-item p {
    color: #5d6d7e;
    font-size: 14px;
    line-height: 1.5;
    margin: 0;
}

/* ================ USAGE GRID ================ */
.usage-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 25px 0;
}

.usage-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.usage-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.usage-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    font-size: 24px;
    color: white;
}

.usage-card:nth-child(1) .usage-icon {
    background: linear-gradient(135deg, #3498db, #2980b9);
}

.usage-card:nth-child(2) .usage-icon {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.usage-card:nth-child(3) .usage-icon {
    background: linear-gradient(135deg, #f39c12, #e67e22);
}

.usage-card:nth-child(4) .usage-icon {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

.usage-card:nth-child(5) .usage-icon {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
}

.usage-card:nth-child(6) .usage-icon {
    background: linear-gradient(135deg, #34495e, #2c3e50);
}

.usage-card h4 {
    color: #2c3e50;
    font-size: 18px;
    margin-bottom: 10px;
    font-weight: 600;
}

.usage-card p {
    color: #5d6d7e;
    line-height: 1.5;
    margin: 0;
}

/* ================ LEGAL BASIS ================ */
.legal-basis {
    margin: 30px 0;
}

.legal-basis h4 {
    color: #2c3e50;
    font-size: 18px;
    margin-bottom: 20px;
}

.legal-items {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.legal-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    background: white;
    border-radius: 10px;
    padding: 20px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.legal-item:hover {
    transform: translateX(5px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.legal-item i {
    color: #6a0dad;
    font-size: 24px;
    margin-top: 2px;
}

.legal-item h5 {
    color: #2c3e50;
    font-size: 16px;
    margin-bottom: 5px;
    font-weight: 600;
}

.legal-item p {
    color: #5d6d7e;
    margin: 0;
    line-height: 1.5;
}

/* ================ SHARING SCENARIOS ================ */
.sharing-scenarios {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 25px;
    margin: 25px 0;
}

.scenario-card {
    border-radius: 12px;
    padding: 25px;
    border: 2px solid;
    transition: all 0.3s ease;
}

.scenario-card.allowed {
    border-color: #27ae60;
    background: linear-gradient(135deg, #e8f5e8, #d5f4e6);
}

.scenario-card.restricted {
    border-color: #e74c3c;
    background: linear-gradient(135deg, #fdf2f2, #faeaea);
}

.scenario-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
}

.scenario-card.allowed .scenario-header i {
    color: #27ae60;
    font-size: 24px;
}

.scenario-card.restricted .scenario-header i {
    color: #e74c3c;
    font-size: 24px;
}

.scenario-header h4 {
    color: #2c3e50;
    font-size: 18px;
    margin: 0;
    font-weight: 600;
}

.scenario-card ul {
    list-style: none;
    padding: 0;
}

.scenario-card li {
    margin-bottom: 12px;
    padding-left: 25px;
    position: relative;
    line-height: 1.5;
    color: #2c3e50;
}

.scenario-card.allowed li::before {
    content: "✓";
    color: #27ae60;
    font-weight: bold;
    position: absolute;
    left: 0;
    top: 0;
}

.scenario-card.restricted li::before {
    content: "✗";
    color: #e74c3c;
    font-weight: bold;
    position: absolute;
    left: 0;
    top: 0;
}

.scenario-card strong {
    font-weight: 600;
}

/* ================ PRIVACY CONTROLS ================ */
.privacy-controls {
    margin: 30px 0;
}

.privacy-controls h4 {
    color: #2c3e50;
    font-size: 18px;
    margin-bottom: 20px;
}

.control-options {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.control-option {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    background: white;
    border-radius: 12px;
    padding: 25px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.control-option:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.control-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #6a0dad, #8b4bcf);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.control-icon i {
    color: white;
    font-size: 24px;
}

.control-content h5 {
    color: #2c3e50;
    font-size: 16px;
    margin-bottom: 8px;
    font-weight: 600;
}

.control-content p {
    color: #5d6d7e;
    line-height: 1.5;
    margin: 0;
}

/* ================ SECURITY MEASURES ================ */
.security-measures {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 25px 0;
}

.security-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    text-align: center;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.security-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.security-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    font-size: 24px;
    color: white;
    background: linear-gradient(135deg, #27ae60, #2ecc71);
}

.security-card h4 {
    color: #2c3e50;
    font-size: 18px;
    margin-bottom: 10px;
    font-weight: 600;
}

.security-card p {
    color: #5d6d7e;
    line-height: 1.5;
    margin: 0;
}

/* ================ DATA RETENTION ================ */
.data-retention {
    margin: 30px 0;
}

.data-retention h4 {
    color: #2c3e50;
    font-size: 18px;
    margin-bottom: 20px;
}

.retention-info {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.retention-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    background: white;
    border-radius: 10px;
    padding: 20px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.retention-item:hover {
    transform: translateX(5px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.retention-item i {
    color: #6a0dad;
    font-size: 24px;
    margin-top: 2px;
}

.retention-item h5 {
    color: #2c3e50;
    font-size: 16px;
    margin-bottom: 5px;
    font-weight: 600;
}

.retention-item p {
    color: #5d6d7e;
    margin: 0;
    line-height: 1.5;
}

/* ================ SECURITY TIPS ================ */
.security-tips {
    margin: 30px 0;
}

.security-tips h4 {
    color: #2c3e50;
    font-size: 18px;
    margin-bottom: 20px;
}

.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.tip-item {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.tip-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.tip-item i {
    color: #27ae60;
    font-size: 24px;
    margin-bottom: 10px;
}

.tip-item p {
    color: #2c3e50;
    font-size: 14px;
    font-weight: 600;
    margin: 0;
}

/* ================ COOKIE TYPES ================ */
.cookie-types {
    margin: 25px 0;
}

.cookie-type {
    background: white;
    border-radius: 12px;
    padding: 25px;
    border: 1px solid #e9ecef;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.cookie-type:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.cookie-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
}

.cookie-header h4 {
    color: #2c3e50;
    font-size: 18px;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
}

.cookie-header i {
    color: #6a0dad;
    font-size: 20px;
}

.cookie-status {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
}

.cookie-status.required {
    background: #fdf2f2;
    color: #e74c3c;
    border: 1px solid #e74c3c;
}

.cookie-status.optional {
    background: #e8f5e8;
    color: #27ae60;
    border: 1px solid #27ae60;
}

.cookie-type p {
    color: #5d6d7e;
    line-height: 1.5;
    margin-bottom: 15px;
}

.cookie-type ul {
    list-style: none;
    padding: 0;
}

.cookie-type li {
    color: #5d6d7e;
    margin-bottom: 8px;
    padding-left: 20px;
    position: relative;
    line-height: 1.5;
}

.cookie-type li::before {
    content: "•";
    color: #6a0dad;
    font-weight: bold;
    position: absolute;
    left: 0;
}

/* ================ COOKIE MANAGEMENT ================ */
.cookie-management {
    margin: 30px 0;
}

.cookie-management h4 {
    color: #2c3e50;
    font-size: 18px;
    margin-bottom: 15px;
}

.management-options {
    background: white;
    border-radius: 10px;
    padding: 20px;
    border: 1px solid #e9ecef;
    text-align: center;
}

.cookie-settings-btn {
    background: linear-gradient(135deg, #6a0dad, #8b4bcf);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    margin-bottom: 15px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.cookie-settings-btn:hover {
    background: linear-gradient(135deg, #5a0b8a, #7a40b8);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(106, 13, 173, 0.3);
}

.management-options p {
    color: #5d6d7e;
    line-height: 1.5;
    margin: 0;
}

/* ================ RIGHTS GRID ================ */
.rights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 25px 0;
}

.right-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    text-align: center;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.right-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.right-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    font-size: 24px;
    color: white;
}

.right-card:nth-child(1) .right-icon {
    background: linear-gradient(135deg, #3498db, #2980b9);
}

.right-card:nth-child(2) .right-icon {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.right-card:nth-child(3) .right-icon {
    background: linear-gradient(135deg, #f39c12, #e67e22);
}

.right-card:nth-child(4) .right-icon {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

.right-card:nth-child(5) .right-icon {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
}

.right-card:nth-child(6) .right-icon {
    background: linear-gradient(135deg, #34495e, #2c3e50);
}

.right-card h4 {
    color: #2c3e50;
    font-size: 18px;
    margin-bottom: 10px;
    font-weight: 600;
}

.right-card p {
    color: #5d6d7e;
    line-height: 1.5;
    margin-bottom: 20px;
}

.right-action-btn {
    background: linear-gradient(135deg, #6a0dad, #8b4bcf);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    width: 100%;
}

.right-action-btn:hover {
    background: linear-gradient(135deg, #5a0b8a, #7a40b8);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(106, 13, 173, 0.3);
}

/* ================ RIGHTS NOTICE ================ */
.rights-notice {
    margin: 30px 0;
}

.notice-content {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    background: linear-gradient(135deg, #e8f4f8, #d6eaf8);
    border-radius: 12px;
    padding: 25px;
    border: 1px solid #3498db;
}

.notice-content i {
    color: #3498db;
    font-size: 24px;
    margin-top: 2px;
}

.notice-content h4 {
    color: #2c3e50;
    font-size: 18px;
    margin-bottom: 10px;
    font-weight: 600;
}

.notice-content p {
    color: #5d6d7e;
    line-height: 1.6;
    margin: 0;
}

/* ================ CONTACT METHODS ================ */
.contact-methods {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin: 25px 0;
}

.contact-method {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    background: white;
    border-radius: 12px;
    padding: 25px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.contact-method:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.contact-method:nth-child(1) .contact-icon {
    background: linear-gradient(135deg, #3498db, #2980b9);
}

.contact-method:nth-child(2) .contact-icon {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
}

.contact-method:nth-child(3) .contact-icon {
    background: linear-gradient(135deg, #f39c12, #e67e22);
}

.contact-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.contact-icon i {
    color: white;
    font-size: 24px;
}

.contact-info h4 {
    color: #2c3e50;
    font-size: 18px;
    margin-bottom: 10px;
    font-weight: 600;
}

.contact-info p {
    color: #5d6d7e;
    line-height: 1.5;
    margin-bottom: 8px;
}

.contact-info a {
    color: #6a0dad;
    text-decoration: none;
    font-weight: 600;
}

.contact-info a:hover {
    text-decoration: underline;
}

/* ================ POLICY UPDATES ================ */
.policy-updates {
    margin: 30px 0;
}

.policy-updates h4 {
    color: #2c3e50;
    font-size: 18px;
    margin-bottom: 15px;
}

.policy-updates p {
    color: #5d6d7e;
    line-height: 1.6;
    margin-bottom: 20px;
}

.notification-signup {
    background: white;
    border-radius: 10px;
    padding: 20px;
    border: 1px solid #e9ecef;
}

.notification-signup h5 {
    color: #2c3e50;
    font-size: 16px;
    margin-bottom: 10px;
    font-weight: 600;
}

.notification-signup p {
    color: #5d6d7e;
    margin-bottom: 15px;
}

.subscribe-form {
    display: flex;
    gap: 10px;
}

.subscribe-form input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
}

.subscribe-form input:focus {
    outline: none;
    border-color: #6a0dad;
    box-shadow: 0 0 0 3px rgba(106, 13, 173, 0.1);
}

.subscribe-form button {
    background: linear-gradient(135deg, #6a0dad, #8b4bcf);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.subscribe-form button:hover {
    background: linear-gradient(135deg, #5a0b8a, #7a40b8);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(106, 13, 173, 0.3);
}

/* ================ RESPONSIVE DESIGN ================ */
@media (max-width: 768px) {
    .container {
        flex-direction: column;
        max-width: 100%;
    }
    
    .sidebar-left {
        width: 100%;
        height: auto;
        position: relative;
    }
    
    .main-content {
        margin: 10px;
        border-radius: 10px;
        padding: 15px;
    }
    
    .header-content {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .header-actions {
        flex-direction: column;
        width: 100%;
    }
    
    .nav-links {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .privacy-section {
        padding: 20px;
    }
    
    .section-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .data-types {
        grid-template-columns: 1fr;
    }
    
    .usage-grid {
        grid-template-columns: 1fr;
    }
    
    .sharing-scenarios {
        grid-template-columns: 1fr;
    }
    
    .security-measures {
        grid-template-columns: 1fr;
    }
    
    .rights-grid {
        grid-template-columns: 1fr;
    }
    
    .contact-methods {
        grid-template-columns: 1fr;
    }
    
    .info-card {
        flex-direction: column;
        text-align: center;
    }
    
    .subscribe-form {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .main-content {
        padding: 10px;
    }
    
    .header-title h1 {
        font-size: 22px;
    }
    
    .nav-links {
        flex-direction: column;
    }
    
    .nav-link {
        width: 100%;
        justify-content: center;
    }
    
    .section-header h2 {
        font-size: 20px;
    }
    
    .method-grid {
        grid-template-columns: 1fr;
    }
    
    .tips-grid {
        grid-template-columns: 1fr;
    }
}