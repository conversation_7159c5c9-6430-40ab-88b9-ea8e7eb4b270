// Scout Dashboard JavaScript
class ScoutDashboard {
    constructor() {
        this.players = [];
        this.currentView = 'grid';
        this.filters = {
            sport: '',
            priority: '',
            age: '',
            status: '',
            search: ''
        };
        
        this.init();
    }

    init() {
        this.loadEventListeners();
        this.loadPlayerData();
        this.updateStats();
    }

    loadEventListeners() {
        // Modal controls
        const addPlayerBtn = document.getElementById('addPlayerBtn');
        const modal = document.getElementById('addPlayerModal');
        const closeBtn = document.querySelector('.modal-close');
        const cancelBtn = document.querySelector('.btn-cancel');
        const confirmBtn = document.querySelector('.btn-confirm');

        addPlayerBtn?.addEventListener('click', () => this.openModal());
        closeBtn?.addEventListener('click', () => this.closeModal());
        cancelBtn?.addEventListener('click', () => this.closeModal());
        confirmBtn?.addEventListener('click', () => this.addNewPlayer());

        // Close modal when clicking outside
        window.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeModal();
            }
        });

        // View toggle
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchView(e.target.dataset.view);
            });
        });

        // Filters
        document.getElementById('sportFilter')?.addEventListener('change', (e) => {
            this.filters.sport = e.target.value;
            this.filterPlayers();
        });

        document.getElementById('priorityFilter')?.addEventListener('change', (e) => {
            this.filters.priority = e.target.value;
            this.filterPlayers();
        });

        document.getElementById('ageFilter')?.addEventListener('change', (e) => {
            this.filters.age = e.target.value;
            this.filterPlayers();
        });

        document.getElementById('statusFilter')?.addEventListener('change', (e) => {
            this.filters.status = e.target.value;
            this.filterPlayers();
        });

        document.getElementById('searchInput')?.addEventListener('input', (e) => {
            this.filters.search = e.target.value.toLowerCase();
            this.filterPlayers();
        });

        // Export functionality
        document.getElementById('exportBtn')?.addEventListener('click', () => {
            this.exportPlayersList();
        });

        // Player action buttons
        this.attachPlayerActionListeners();
    }

    attachPlayerActionListeners() {
        // View Profile buttons
        document.querySelectorAll('.view-profile').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const playerCard = e.target.closest('.player-card');
                const playerName = playerCard.querySelector('h3').textContent;
                this.viewPlayerProfile(playerName);
            });
        });

        // Contact buttons
        document.querySelectorAll('.contact-player').forEach(btn => {
            btn.addEventListener('click', (e) => {
                if (!btn.classList.contains('contacted')) {
                    const playerCard = e.target.closest('.player-card');
                    const playerName = playerCard.querySelector('h3').textContent;
                    this.contactPlayer(playerName, btn);
                }
            });
        });

        // Remove watch buttons
        document.querySelectorAll('.remove-watch').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const playerCard = e.target.closest('.player-card');
                const playerName = playerCard.querySelector('h3').textContent;
                this.removeFromWatchList(playerName, playerCard);
            });
        });

        // Priority selectors
        document.querySelectorAll('.priority-selector select').forEach(select => {
            select.addEventListener('change', (e) => {
                const playerCard = e.target.closest('.player-card');
                const playerName = playerCard.querySelector('h3').textContent;
                this.updatePlayerPriority(playerName, e.target.value, playerCard);
            });
        });

        // Auto-save notes
        document.querySelectorAll('.player-notes textarea').forEach(textarea => {
            textarea.addEventListener('blur', (e) => {
                const playerCard = e.target.closest('.player-card');
                const playerName = playerCard.querySelector('h3').textContent;
                this.savePlayerNotes(playerName, e.target.value);
            });
        });
    }

    loadPlayerData() {
        // In a real application, this would load from an API
        this.players = [
            {
                name: 'Marcus Rodriguez',
                sport: 'football',
                priority: 'high',
                age: 19,
                status: 'watching',
                watchers: 15,
                position: 'Midfielder',
                metrics: { speed: 85, technique: 92, teamwork: 78 },
                notes: 'Excellent ball control and vision. Shows great potential for professional level. Need to watch his defensive positioning.',
                avatar: 'https://i.pravatar.cc/60?img=31'
            },
            {
                name: 'Sarah Johnson',
                sport: 'basketball',
                priority: 'medium',
                age: 21,
                status: 'contacted',
                watchers: 8,
                position: 'Point Guard',
                metrics: { shooting: 88, defense: 75, leadership: 94 },
                notes: 'Strong court vision and leadership. Already contacted - waiting for response. Consider offering training camp invitation.',
                avatar: 'https://i.pravatar.cc/60?img=25'
            },
            {
                name: 'Emma Wilson',
                sport: 'volleyball',
                priority: 'high',
                age: 17,
                status: 'watching',
                watchers: 22,
                position: 'Spiker',
                metrics: { power: 96, accuracy: 89, agility: 91 },
                notes: 'Exceptional talent! Very young but shows incredible promise. Recommend immediate contact - high competition from other scouts.',
                avatar: 'https://i.pravatar.cc/60?img=45'
            }
        ];
    }

    updateStats() {
        const totalWatched = this.players.length;
        const highPriority = this.players.filter(p => p.priority === 'high').length;
        const contacted = this.players.filter(p => p.status === 'contacted').length;
        const weeklyGrowth = '+12%'; // This would be calculated from historical data

        // Update stat cards
        const statCards = document.querySelectorAll('.stat-card');
        if (statCards[0]) statCards[0].querySelector('h3').textContent = totalWatched;
        if (statCards[1]) statCards[1].querySelector('h3').textContent = highPriority;
        if (statCards[2]) statCards[2].querySelector('h3').textContent = contacted;
        if (statCards[3]) statCards[3].querySelector('h3').textContent = weeklyGrowth;
    }

    openModal() {
        const modal = document.getElementById('addPlayerModal');
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }

    closeModal() {
        const modal = document.getElementById('addPlayerModal');
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
        
        // Clear form
        document.getElementById('playerSearch').value = '';
        document.getElementById('newPlayerPriority').value = 'medium';
        document.getElementById('initialNotes').value = '';
    }

    addNewPlayer() {
        const playerName = document.getElementById('playerSearch').value.trim();
        const priority = document.getElementById('newPlayerPriority').value;
        const notes = document.getElementById('initialNotes').value.trim();

        if (!playerName) {
            alert('Please enter a player name');
            return;
        }

        // Check if player already exists
        const existingPlayer = this.players.find(p => 
            p.name.toLowerCase() === playerName.toLowerCase()
        );

        if (existingPlayer) {
            alert('This player is already in your watch list');
            return;
        }

        // Add new player
        const newPlayer = {
            name: playerName,
            sport: 'football', // Default sport
            priority: priority,
            age: 20, // Default age
            status: 'watching',
            watchers: 1,
            position: 'Unknown',
            metrics: { skill1: 75, skill2: 80, skill3: 70 },
            notes: notes || 'No notes added yet.',
            avatar: `https://i.pravatar.cc/60?img=${Math.floor(Math.random() * 70)}`
        };

        this.players.push(newPlayer);
        this.renderPlayers();
        this.updateStats();
        this.closeModal();

        // Show success message
        this.showNotification('Player added to watch list successfully!', 'success');
    }

    switchView(view) {
        this.currentView = view;
        
        // Update active button
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-view="${view}"]`).classList.add('active');

        // Update container layout
        const container = document.getElementById('playersContainer');
        if (view === 'list') {
            container.style.gridTemplateColumns = '1fr';
        } else {
            container.style.gridTemplateColumns = 'repeat(auto-fit, minmax(400px, 1fr))';
        }
    }

    filterPlayers() {
        const playerCards = document.querySelectorAll('.player-card');
        
        playerCards.forEach(card => {
            const sport = card.dataset.sport;
            const priority = card.dataset.priority;
            const age = parseInt(card.dataset.age);
            const status = card.dataset.status;
            const playerName = card.querySelector('h3').textContent.toLowerCase();

            let shouldShow = true;

            // Sport filter
            if (this.filters.sport && sport !== this.filters.sport) {
                shouldShow = false;
            }

            // Priority filter
            if (this.filters.priority && priority !== this.filters.priority) {
                shouldShow = false;
            }

            // Age filter
            if (this.filters.age) {
                const [min, max] = this.filters.age.includes('+') 
                    ? [parseInt(this.filters.age), 100]
                    : this.filters.age.split('-').map(Number);
                
                if (age < min || age > max) {
                    shouldShow = false;
                }
            }

            // Status filter
            if (this.filters.status && status !== this.filters.status) {
                shouldShow = false;
            }

            // Search filter
            if (this.filters.search && !playerName.includes(this.filters.search)) {
                shouldShow = false;
            }

            // Show/hide card
            card.style.display = shouldShow ? 'block' : 'none';
        });
    }

    viewPlayerProfile(playerName) {
        // In a real application, this would navigate to the player's profile page
        this.showNotification(`Opening profile for ${playerName}...`, 'info');
        
        // Simulate navigation delay
        setTimeout(() => {
            window.location.href = `../Profile/athletic_profile.html?player=${encodeURIComponent(playerName)}`;
        }, 1000);
    }

    contactPlayer(playerName, buttonElement) {
        // Show confirmation dialog
        if (confirm(`Do you want to contact ${playerName}? This will send them a notification about your interest.`)) {
            // Update button state
            buttonElement.innerHTML = '<i class="fas fa-check"></i> Contacted';
            buttonElement.classList.add('contacted');
            
            // Update player status
            const playerCard = buttonElement.closest('.player-card');
            playerCard.dataset.status = 'contacted';
            
            // Update data
            const player = this.players.find(p => p.name === playerName);
            if (player) {
                player.status = 'contacted';
            }
            
            this.updateStats();
            this.showNotification(`Contact request sent to ${playerName}!`, 'success');
        }
    }

    removeFromWatchList(playerName, playerCard) {
        if (confirm(`Are you sure you want to remove ${playerName} from your watch list?`)) {
            // Remove from UI
            playerCard.style.animation = 'fadeOut 0.3s ease';
            setTimeout(() => {
                playerCard.remove();
            }, 300);
            
            // Remove from data
            this.players = this.players.filter(p => p.name !== playerName);
            
            this.updateStats();
            this.showNotification(`${playerName} removed from watch list`, 'info');
        }
    }

    updatePlayerPriority(playerName, newPriority, playerCard) {
        // Update UI
        const priorityBadge = playerCard.querySelector('.priority-badge');
        priorityBadge.className = `priority-badge ${newPriority}`;
        priorityBadge.innerHTML = `<i class="fas fa-star"></i> ${newPriority.charAt(0).toUpperCase() + newPriority.slice(1)} Priority`;
        
        // Update data
        const player = this.players.find(p => p.name === playerName);
        if (player) {
            player.priority = newPriority;
        }
        
        // Update card dataset
        playerCard.dataset.priority = newPriority;
        
        this.updateStats();
        this.showNotification(`Priority updated for ${playerName}`, 'success');
    }

    savePlayerNotes(playerName, notes) {
        const player = this.players.find(p => p.name === playerName);
        if (player) {
            player.notes = notes;
            this.showNotification('Notes saved', 'success');
        }
    }

    exportPlayersList() {
        const exportData = this.players.map(player => ({
            Name: player.name,
            Sport: player.sport,
            Priority: player.priority,
            Age: player.age,
            Status: player.status,
            Watchers: player.watchers,
            Position: player.position,
            Notes: player.notes
        }));

        // Convert to CSV
        const csvContent = this.convertToCSV(exportData);
        
        // Download file
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `scout_watchlist_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        this.showNotification('Watch list exported successfully!', 'success');
    }

    convertToCSV(data) {
        if (!data.length) return '';
        
        const headers = Object.keys(data[0]);
        const csvHeaders = headers.join(',');
        
        const csvRows = data.map(row => 
            headers.map(header => {
                const value = row[header];
                return typeof value === 'string' && value.includes(',') 
                    ? `"${value}"` 
                    : value;
            }).join(',')
        );
        
        return [csvHeaders, ...csvRows].join('\n');
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;
        
        // Add styles
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${this.getNotificationColor(type)};
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
            z-index: 1001;
            animation: slideInRight 0.3s ease;
            max-width: 350px;
        `;
        
        document.body.appendChild(notification);
        
        // Auto remove after 3 seconds
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    getNotificationIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    getNotificationColor(type) {
        const colors = {
            success: 'linear-gradient(135deg, #28a745, #20c997)',
            error: 'linear-gradient(135deg, #dc3545, #e74c3c)',
            warning: 'linear-gradient(135deg, #ffc107, #ff8c00)',
            info: 'linear-gradient(135deg, #667eea, #764ba2)'
        };
        return colors[type] || colors.info;
    }

    renderPlayers() {
        // This would be used to re-render the players list after adding new ones
        // For now, we'll just refresh the event listeners
        this.attachPlayerActionListeners();
    }
}

// Add CSS animations for notifications
const notificationStyles = document.createElement('style');
notificationStyles.textContent = `
    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(100%);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
    
    @keyframes slideOutRight {
        from {
            opacity: 1;
            transform: translateX(0);
        }
        to {
            opacity: 0;
            transform: translateX(100%);
        }
    }
    
    @keyframes fadeOut {
        from {
            opacity: 1;
            transform: scale(1);
        }
        to {
            opacity: 0;
            transform: scale(0.8);
        }
    }
    
    .notification-content {
        display: flex;
        align-items: center;
        gap: 10px;
    }
`;
document.head.appendChild(notificationStyles);

// Initialize the dashboard when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new ScoutDashboard();
});

// Add some global utility functions
window.ScoutDashboardUtils = {
    formatDate: (date) => {
        return new Date(date).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    },
    
    calculateAge: (birthDate) => {
        const today = new Date();
        const birth = new Date(birthDate);
        let age = today.getFullYear() - birth.getFullYear();
        const monthDiff = today.getMonth() - birth.getMonth();
        
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
            age--;
        }
        
        return age;
    },
    
    generatePlayerSlug: (playerName) => {
        return playerName.toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/^-+|-+$/g, '');
    }
};