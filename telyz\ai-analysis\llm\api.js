/**
 * LLM API
 * 
 * API for interacting with the LLM service.
 */

const express = require('express');
const router = express.Router();
const llmService = require('./index');

// Initialize LLM service
let llmInitialized = false;
let initializationPromise = null;

const ensureInitialized = async () => {
  if (!llmInitialized && !initializationPromise) {
    initializationPromise = llmService.initialize()
      .then(() => {
        llmInitialized = true;
        initializationPromise = null;
        console.log('LLM service initialized successfully');
      })
      .catch(err => {
        console.error('Failed to initialize LLM service:', err);
        initializationPromise = null;
        throw err;
      });
  }
  
  if (initializationPromise) {
    await initializationPromise;
  }
  
  if (!llmInitialized) {
    throw new Error('LLM service not initialized');
  }
};

/**
 * @route POST /api/llm/chat
 * @description Chat with the LLM
 * @access Private
 */
router.post('/chat', async (req, res) => {
  try {
    await ensureInitialized();
    
    const { message, history, context } = req.body;
    
    if (!message) {
      return res.status(400).json({ message: 'Message is required' });
    }
    
    const response = await llmService.chat(message, history || [], context || {});
    
    res.json(response);
  } catch (error) {
    console.error('Error in LLM chat:', error);
    res.status(500).json({ message: 'Error processing chat request', error: error.message });
  }
});

/**
 * @route POST /api/llm/analyze
 * @description Analyze video with LLM assistance
 * @access Private
 */
router.post('/analyze', async (req, res) => {
  try {
    await ensureInitialized();
    
    const { videoAnalysis, options } = req.body;
    
    if (!videoAnalysis) {
      return res.status(400).json({ message: 'Video analysis data is required' });
    }
    
    const enhancedAnalysis = await llmService.analyzeVideoWithLLM(videoAnalysis, options || {});
    
    res.json(enhancedAnalysis);
  } catch (error) {
    console.error('Error in LLM analysis:', error);
    res.status(500).json({ message: 'Error processing analysis request', error: error.message });
  }
});

/**
 * @route GET /api/llm/status
 * @description Get LLM service status
 * @access Private
 */
router.get('/status', async (req, res) => {
  try {
    const initialized = llmInitialized;
    
    res.json({
      initialized,
      status: initialized ? 'ready' : 'not_initialized',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error getting LLM status:', error);
    res.status(500).json({ message: 'Error getting LLM status', error: error.message });
  }
});

module.exports = router;