/**
 * Validators Utility
 * 
 * Client-side utility functions for validating data.
 */

/**
 * Validate an email address
 * 
 * @param {string} email - The email address to validate
 * @returns {boolean} Whether the email is valid
 */
function validateEmail(email) {
  if (!email) return false;
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate a password
 * 
 * @param {string} password - The password to validate
 * @param {Object} options - Validation options
 * @param {number} options.minLength - Minimum length (default: 8)
 * @param {boolean} options.requireUppercase - Require uppercase letter (default: true)
 * @param {boolean} options.requireLowercase - Require lowercase letter (default: true)
 * @param {boolean} options.requireNumber - Require number (default: true)
 * @param {boolean} options.requireSpecial - Require special character (default: true)
 * @returns {Object} Validation result with isValid and errors
 */
function validatePassword(password, options = {}) {
  const {
    minLength = 8,
    requireUppercase = true,
    requireLowercase = true,
    requireNumber = true,
    requireSpecial = true,
  } = options;
  
  const errors = [];
  
  if (!password) {
    errors.push('Password is required');
    return { isValid: false, errors };
  }
  
  if (password.length < minLength) {
    errors.push(`Password must be at least ${minLength} characters long`);
  }
  
  if (requireUppercase && !/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (requireLowercase && !/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (requireNumber && !/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  if (requireSpecial && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Validate a URL
 * 
 * @param {string} url - The URL to validate
 * @returns {boolean} Whether the URL is valid
 */
function validateUrl(url) {
  if (!url) return false;
  
  try {
    new URL(url);
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Validate a date
 * 
 * @param {Date|string|number} date - The date to validate
 * @param {Object} options - Validation options
 * @param {Date|string|number} options.minDate - Minimum date
 * @param {Date|string|number} options.maxDate - Maximum date
 * @returns {boolean} Whether the date is valid
 */
function validateDate(date, options = {}) {
  const { minDate, maxDate } = options;
  
  const d = new Date(date);
  
  if (isNaN(d.getTime())) {
    return false;
  }
  
  if (minDate && d < new Date(minDate)) {
    return false;
  }
  
  if (maxDate && d > new Date(maxDate)) {
    return false;
  }
  
  return true;
}

/**
 * Validate a phone number
 * 
 * @param {string} phoneNumber - The phone number to validate
 * @returns {boolean} Whether the phone number is valid
 */
function validatePhoneNumber(phoneNumber) {
  if (!phoneNumber) return false;
  
  // Remove all non-digit characters
  const digits = phoneNumber.replace(/\D/g, '');
  
  // Check if the number of digits is valid (most countries have 7-15 digits)
  return digits.length >= 7 && digits.length <= 15;
}

/**
 * Validate a file type
 * 
 * @param {string} fileName - The file name
 * @param {string[]} allowedTypes - Array of allowed file extensions
 * @returns {boolean} Whether the file type is valid
 */
function validateFileType(fileName, allowedTypes) {
  if (!fileName || !allowedTypes || !allowedTypes.length) return false;
  
  const extension = fileName.split('.').pop().toLowerCase();
  return allowedTypes.includes(extension);
}

/**
 * Validate a file size
 * 
 * @param {number} fileSize - The file size in bytes
 * @param {number} maxSize - Maximum allowed size in bytes
 * @returns {boolean} Whether the file size is valid
 */
function validateFileSize(fileSize, maxSize) {
  if (typeof fileSize !== 'number' || typeof maxSize !== 'number') return false;
  
  return fileSize <= maxSize;
}

/**
 * Validate a name (first name, last name, etc.)
 * 
 * @param {string} name - The name to validate
 * @param {Object} options - Validation options
 * @param {number} options.minLength - Minimum length (default: 2)
 * @param {number} options.maxLength - Maximum length (default: 50)
 * @returns {boolean} Whether the name is valid
 */
function validateName(name, options = {}) {
  const { minLength = 2, maxLength = 50 } = options;
  
  if (!name) return false;
  
  return name.length >= minLength && name.length <= maxLength;
}

/**
 * Validate a form
 * 
 * @param {Object} formData - Form data
 * @param {Object} validations - Validation rules
 * @returns {Object} Validation result with isValid and errors
 */
function validateForm(formData, validations) {
  const errors = {};
  let isValid = true;
  
  for (const field in validations) {
    const value = formData[field];
    const rules = validations[field];
    
    // Required validation
    if (rules.required && (value === undefined || value === null || value === '')) {
      errors[field] = rules.requiredMessage || 'This field is required';
      isValid = false;
      continue;
    }
    
    // Skip other validations if value is empty and not required
    if (value === undefined || value === null || value === '') {
      continue;
    }
    
    // Email validation
    if (rules.email && !validateEmail(value)) {
      errors[field] = rules.emailMessage || 'Please enter a valid email address';
      isValid = false;
      continue;
    }
    
    // Min length validation
    if (rules.minLength !== undefined && value.length < rules.minLength) {
      errors[field] = rules.minLengthMessage || `Must be at least ${rules.minLength} characters`;
      isValid = false;
      continue;
    }
    
    // Max length validation
    if (rules.maxLength !== undefined && value.length > rules.maxLength) {
      errors[field] = rules.maxLengthMessage || `Must be at most ${rules.maxLength} characters`;
      isValid = false;
      continue;
    }
    
    // Pattern validation
    if (rules.pattern && !rules.pattern.test(value)) {
      errors[field] = rules.patternMessage || 'Invalid format';
      isValid = false;
      continue;
    }
    
    // Custom validation
    if (rules.validate && typeof rules.validate === 'function') {
      const customValidation = rules.validate(value, formData);
      if (customValidation !== true) {
        errors[field] = customValidation || 'Invalid value';
        isValid = false;
        continue;
      }
    }
  }
  
  return { isValid, errors };
}

// Export validators
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    validateEmail,
    validatePassword,
    validateUrl,
    validateDate,
    validatePhoneNumber,
    validateFileType,
    validateFileSize,
    validateName,
    validateForm,
  };
}
