<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الخادم - منصة Telyz</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .breadcrumb {
            background: #f8f9fa;
            padding: 15px 30px;
            border-bottom: 1px solid #e9ecef;
        }

        .breadcrumb a {
            color: #007bff;
            text-decoration: none;
            margin-left: 5px;
        }

        .content {
            padding: 40px;
        }

        .section {
            margin-bottom: 40px;
        }

        .section h2 {
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #28a745;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            text-decoration: none;
            color: #333;
            transition: all 0.3s ease;
            position: relative;
        }

        .card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.1);
            border-color: #28a745;
        }

        .card h3 {
            color: #28a745;
            margin-bottom: 10px;
        }

        .card p {
            color: #666;
            font-size: 0.9em;
            line-height: 1.5;
        }

        .file-list {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 15px;
        }

        .file-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .file-item:last-child {
            border-bottom: none;
        }

        .file-icon {
            margin-left: 10px;
            font-size: 1.2em;
        }

        .file-link {
            color: #007bff;
            text-decoration: none;
        }

        .file-link:hover {
            text-decoration: underline;
        }

        .back-btn {
            display: inline-block;
            background: #6c757d;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            margin-bottom: 20px;
        }

        .back-btn:hover {
            background: #5a6268;
        }

        .api-endpoint {
            background: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 10px 15px;
            margin: 10px 0;
            border-radius: 0 5px 5px 0;
        }

        .method {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 0.8em;
            font-weight: bold;
            margin-left: 10px;
        }

        .method.get { background: #d4edda; color: #155724; }
        .method.post { background: #d1ecf1; color: #0c5460; }
        .method.put { background: #fff3cd; color: #856404; }
        .method.delete { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🖥️ الخادم (Server)</h1>
            <p>استكشف API، قواعد البيانات، المتحكمات والخدمات الخلفية</p>
        </div>

        <div class="breadcrumb">
            <a href="../index.html">🏠 الرئيسية</a> / الخادم
        </div>

        <div class="content">
            <a href="../index.html" class="back-btn">← العودة للرئيسية</a>

            <div class="section">
                <h2>🚀 ملفات التشغيل الرئيسية</h2>
                <div class="grid">
                    <div class="card">
                        <h3>ملفات الخادم الأساسية</h3>
                        <p>ملفات بدء تشغيل الخادم والتطبيق</p>
                        <div class="file-list">
                            <div class="file-item">
                                <span class="file-icon">🚀</span>
                                <a href="index.js" class="file-link">index.js</a>
                            </div>
                            <div class="file-item">
                                <span class="file-icon">⚙️</span>
                                <a href="app.js" class="file-link">app.js</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>🌐 واجهة برمجة التطبيقات (API)</h2>
                <div class="grid">
                    <div class="card">
                        <h3>المتحكمات (Controllers)</h3>
                        <p>منطق معالجة الطلبات والاستجابات</p>
                        <div class="file-list">
                            <div class="file-item">
                                <span class="file-icon">🔐</span>
                                <a href="api/controllers/authController.js" class="file-link">authController.js</a>
                            </div>
                            <div class="file-item">
                                <span class="file-icon">👤</span>
                                <a href="api/controllers/userController.js" class="file-link">userController.js</a>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <h3>المسارات (Routes)</h3>
                        <p>تعريف نقاط النهاية للAPI</p>
                        <div class="file-list">
                            <div class="file-item">
                                <span class="file-icon">🔐</span>
                                <a href="api/routes/auth.js" class="file-link">auth.js</a>
                            </div>
                            <div class="file-item">
                                <span class="file-icon">👥</span>
                                <a href="api/routes/users.js" class="file-link">users.js</a>
                            </div>
                            <div class="file-item">
                                <span class="file-icon">🤖</span>
                                <a href="api/routes/ai-analysis.js" class="file-link">ai-analysis.js</a>
                            </div>
                            <div class="file-item">
                                <span class="file-icon">🎯</span>
                                <a href="api/routes/opportunities.js" class="file-link">opportunities.js</a>
                            </div>
                        </div>
                        
                        <div class="api-endpoint">
                            <strong>نقاط النهاية الرئيسية:</strong><br>
                            <span class="method post">POST</span> /api/auth/login<br>
                            <span class="method post">POST</span> /api/auth/register<br>
                            <span class="method get">GET</span> /api/users/profile<br>
                            <span class="method post">POST</span> /api/ai-analysis/upload<br>
                            <span class="method get">GET</span> /api/opportunities
                        </div>
                    </div>

                    <div class="card">
                        <h3>الوسطاء (Middlewares)</h3>
                        <p>معالجة الطلبات والمصادقة</p>
                        <div class="file-list">
                            <div class="file-item">
                                <span class="file-icon">🛡️</span>
                                <a href="api/middlewares/authMiddleware.js" class="file-link">authMiddleware.js</a>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <h3>المدققات (Validators)</h3>
                        <p>التحقق من صحة البيانات</p>
                        <div class="file-list">
                            <div class="file-item">
                                <span class="file-icon">✅</span>
                                <a href="api/validators/authValidator.js" class="file-link">authValidator.js</a>
                            </div>
                            <div class="file-item">
                                <span class="file-icon">✅</span>
                                <a href="api/validators/userValidator.js" class="file-link">userValidator.js</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>🗄️ قواعد البيانات والنماذج</h2>
                <div class="grid">
                    <div class="card">
                        <h3>النماذج (Models)</h3>
                        <p>هياكل البيانات وقواعد العمل</p>
                        <div class="file-list">
                            <div class="file-item">
                                <span class="file-icon">👤</span>
                                <a href="models/User.js" class="file-link">User.js</a>
                            </div>
                            <div class="file-item">
                                <span class="file-icon">🏃‍♂️</span>
                                <a href="models/AthleteProfile.js" class="file-link">AthleteProfile.js</a>
                            </div>
                            <div class="file-item">
                                <span class="file-icon">🎯</span>
                                <a href="models/Opportunity.js" class="file-link">Opportunity.js</a>
                            </div>
                            <div class="file-item">
                                <span class="file-icon">🤖</span>
                                <a href="models/AIAnalysis.js" class="file-link">AIAnalysis.js</a>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <h3>خدمات قاعدة البيانات</h3>
                        <p>إدارة الاتصال بقاعدة البيانات</p>
                        <div class="file-list">
                            <div class="file-item">
                                <span class="file-icon">🗄️</span>
                                <a href="services/database/index.js" class="file-link">database/index.js</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>⚙️ الإعدادات والتكوين</h2>
                <div class="grid">
                    <div class="card">
                        <h3>ملفات التكوين</h3>
                        <p>إعدادات الخادم والخدمات</p>
                        <div class="file-list">
                            <div class="file-item">
                                <span class="file-icon">⚙️</span>
                                <a href="config/index.js" class="file-link">index.js</a>
                            </div>
                            <div class="file-item">
                                <span class="file-icon">🗄️</span>
                                <a href="config/database.js" class="file-link">database.js</a>
                            </div>
                            <div class="file-item">
                                <span class="file-icon">📧</span>
                                <a href="config/email.js" class="file-link">email.js</a>
                            </div>
                            <div class="file-item">
                                <span class="file-icon">💾</span>
                                <a href="config/storage.js" class="file-link">storage.js</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>📊 معلومات تقنية</h2>
                <div class="grid">
                    <div class="card">
                        <h3>تقنيات الخادم</h3>
                        <p>التقنيات المستخدمة في الخادم</p>
                        <div class="file-list">
                            <div class="file-item">
                                <span class="file-icon">🟢</span>
                                <span>Node.js - بيئة تشغيل JavaScript</span>
                            </div>
                            <div class="file-item">
                                <span class="file-icon">🚀</span>
                                <span>Express.js - إطار عمل الويب</span>
                            </div>
                            <div class="file-item">
                                <span class="file-icon">🗄️</span>
                                <span>MongoDB/MySQL - قاعدة البيانات</span>
                            </div>
                            <div class="file-item">
                                <span class="file-icon">🔐</span>
                                <span>JWT - المصادقة والتوكن</span>
                            </div>
                            <div class="file-item">
                                <span class="file-icon">📧</span>
                                <span>Nodemailer - إرسال الإيميل</span>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <h3>هيكل API</h3>
                        <p>تنظيم واجهة برمجة التطبيقات</p>
                        <div class="file-list">
                            <div class="file-item">
                                <span class="file-icon">🔗</span>
                                <span>RESTful API Design</span>
                            </div>
                            <div class="file-item">
                                <span class="file-icon">🛡️</span>
                                <span>Middleware Authentication</span>
                            </div>
                            <div class="file-item">
                                <span class="file-icon">✅</span>
                                <span>Input Validation</span>
                            </div>
                            <div class="file-item">
                                <span class="file-icon">📝</span>
                                <span>Error Handling</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>