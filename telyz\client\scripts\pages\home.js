// JavaScript específico para la página de inicio (Home)

document.addEventListener('DOMContentLoaded', function() {
    // Eye icon functionality
    const watchingEyeIcon = document.getElementById('watchingEyeIcon');
    if (watchingEyeIcon) {
        watchingEyeIcon.addEventListener('click', function() {
            // Create notification for premium feature
            showNotification('Talent Monitoring System', '5 scouts and clubs are actively monitoring your performance and career development. Complete an AI analysis and subscribe to premium to enhance your visibility to top clubs.');
        });
    }

    // Function to show notifications
    function showNotification(title, message) {
        // Create a notification element
        const notification = document.createElement('div');
        notification.className = 'feature-notification';
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-eye"></i>
                <div class="notification-text">
                    <strong>${title}</strong>
                    <p>${message}</p>
                </div>
                <button class="notification-close"><i class="fas fa-times"></i></button>
            </div>
        `;

        // Add inline styles for the notification
        notification.style.position = 'fixed';
        notification.style.bottom = '20px';
        notification.style.right = '20px';
        notification.style.backgroundColor = 'white';
        notification.style.borderRadius = '8px';
        notification.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
        notification.style.zIndex = '9999';
        notification.style.overflow = 'hidden';
        notification.style.maxWidth = '350px';
        notification.style.animation = 'slideIn 0.3s forwards';

        // Styles for the notification content
        const notificationContent = notification.querySelector('.notification-content');
        notificationContent.style.display = 'flex';
        notificationContent.style.padding = '15px';
        notificationContent.style.alignItems = 'center';

        // Styles for the icon
        const icon = notification.querySelector('.fas.fa-eye');
        icon.style.color = '#2ecc71';
        icon.style.fontSize = '24px';
        icon.style.marginRight = '15px';
        icon.style.textShadow = '0 0 5px rgba(46, 204, 113, 0.5)';

        // Styles for the text
        const notificationText = notification.querySelector('.notification-text');
        notificationText.style.flex = '1';

        // Styles for the close button
        const closeButton = notification.querySelector('.notification-close');
        closeButton.style.background = 'none';
        closeButton.style.border = 'none';
        closeButton.style.cursor = 'pointer';
        closeButton.style.color = '#999';
        closeButton.style.fontSize = '16px';
        closeButton.style.padding = '5px';

        // Add the notification to the DOM
        document.body.appendChild(notification);

        // Close button functionality
        closeButton.addEventListener('click', function() {
            notification.style.animation = 'slideOut 0.3s forwards';
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                }
            }, 300);
        });

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (document.body.contains(notification)) {
                notification.style.animation = 'slideOut 0.3s forwards';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }
        }, 5000);
    }

    // Add CSS animations for notifications
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        @keyframes slideOut {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);

    // Initialize tooltips for AI Analysis menu
    initializeAITooltips();

    function initializeAITooltips() {
        const aiMenuItems = document.querySelectorAll('.ai-submenu a[data-description]');
        const tooltip = document.getElementById('feature-tooltip');
        
        if (!tooltip) return;
        
        aiMenuItems.forEach(item => {
            item.addEventListener('mouseenter', function(e) {
                const description = this.getAttribute('data-description');
                const color = this.getAttribute('data-color') || 'rgba(0, 0, 0, 0.8)';
                
                tooltip.textContent = description;
                tooltip.style.setProperty('--tooltip-color', color);
                tooltip.style.backgroundColor = color;
                
                // Position the tooltip
                const rect = this.getBoundingClientRect();
                tooltip.style.left = (rect.right + 10) + 'px';
                tooltip.style.top = (rect.top + rect.height / 2 - tooltip.offsetHeight / 2) + 'px';
                
                tooltip.classList.add('visible');
            });
            
            item.addEventListener('mouseleave', function() {
                tooltip.classList.remove('visible');
            });
        });
    }

    // Initialize post creation functionality
    initializePostCreation();

    function initializePostCreation() {
        const textarea = document.querySelector('.simple-textarea');
        const postButton = document.querySelector('.post-button');
        const postIcons = document.querySelectorAll('.post-icon-btn');
        
        if (textarea) {
            textarea.addEventListener('focus', function() {
                this.style.minHeight = '80px';
            });
            
            textarea.addEventListener('blur', function() {
                if (!this.value.trim()) {
                    this.style.minHeight = '50px';
                }
            });
            
            textarea.addEventListener('input', function() {
                if (postButton) {
                    postButton.disabled = !this.value.trim();
                }
            });
        }
        
        if (postButton) {
            postButton.addEventListener('click', function() {
                if (textarea && textarea.value.trim()) {
                    // Here would go the logic to create the post
                    console.log('Create post:', textarea.value);
                    showNotification('Post Created', 'Your post has been shared successfully!');
                    textarea.value = '';
                    this.disabled = true;
                }
            });
        }
        
        // Events for post icons
        postIcons.forEach(icon => {
            icon.addEventListener('click', function() {
                const title = this.getAttribute('title');
                console.log('Action:', title);
                // Here would go the specific logic for each content type
            });
        });
    }

    // Initialize post interactions
    initializePostInteractions();

    function initializePostInteractions() {
        // Like buttons
        const likeButtons = document.querySelectorAll('.action-like');
        likeButtons.forEach(button => {
            button.addEventListener('click', function() {
                this.classList.toggle('liked');
                const icon = this.querySelector('i');
                if (this.classList.contains('liked')) {
                    icon.classList.remove('far');
                    icon.classList.add('fas');
                    this.style.color = '#1877f2';
                } else {
                    icon.classList.remove('fas');
                    icon.classList.add('far');
                    this.style.color = '';
                }
            });
        });

        // Follow buttons
        const followButtons = document.querySelectorAll('.follow-button');
        followButtons.forEach(button => {
            button.addEventListener('click', function() {
                if (this.textContent === 'Follow') {
                    this.textContent = 'Following';
                    this.style.backgroundColor = '#42b883';
                } else {
                    this.textContent = 'Follow';
                    this.style.backgroundColor = '';
                }
            });
        });

        // More actions dropdown
        const moreButtons = document.querySelectorAll('.action-more');
        moreButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.stopPropagation();
                const dropdown = this.parentElement.querySelector('.action-dropdown');
                dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
            });
        });

        // Close dropdowns when clicking outside
        document.addEventListener('click', function() {
            const dropdowns = document.querySelectorAll('.action-dropdown');
            dropdowns.forEach(dropdown => {
                dropdown.style.display = 'none';
            });
        });
    }

    // Initialize follow requests functionality
    initializeFollowRequests();

    function initializeFollowRequests() {
        const acceptButtons = document.querySelectorAll('.accept-button');
        const declineButtons = document.querySelectorAll('.decline-button');

        acceptButtons.forEach(button => {
            button.addEventListener('click', function() {
                const request = this.closest('.follow-request');
                request.style.animation = 'fadeOut 0.3s forwards';
                setTimeout(() => {
                    request.remove();
                    showNotification('Request Accepted', 'Follow request has been accepted successfully!');
                }, 300);
            });
        });

        declineButtons.forEach(button => {
            button.addEventListener('click', function() {
                const request = this.closest('.follow-request');
                request.style.animation = 'fadeOut 0.3s forwards';
                setTimeout(() => {
                    request.remove();
                    showNotification('Request Declined', 'Follow request has been declined.');
                }, 300);
            });
        });
    }

    // Add fade out animation
    const fadeStyle = document.createElement('style');
    fadeStyle.textContent = `
        @keyframes fadeOut {
            from { opacity: 1; transform: translateX(0); }
            to { opacity: 0; transform: translateX(-100%); }
        }
    `;
    document.head.appendChild(fadeStyle);
});
