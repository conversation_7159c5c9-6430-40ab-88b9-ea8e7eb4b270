// JavaScript for the Opportunities page

document.addEventListener('DOMContentLoaded', function() {
    initializeOpportunitiesPage();
});

function initializeOpportunitiesPage() {
    initializeFilters();
    initializeOpportunityActions();
    initializeLoadMore();
    initializeProfileDropdown();
}

// Initialize filters functionality
function initializeFilters() {
    const applyFiltersBtn = document.getElementById('applyFilters');
    const sportFilter = document.getElementById('sportFilter');
    const typeFilter = document.getElementById('typeFilter');
    const locationFilter = document.getElementById('locationFilter');

    if (applyFiltersBtn) {
        applyFiltersBtn.addEventListener('click', function() {
            applyFilters();
        });
    }

    // Apply filters on change
    [sportFilter, typeFilter, locationFilter].forEach(filter => {
        if (filter) {
            filter.addEventListener('change', function() {
                applyFilters();
            });
        }
    });
}

// Apply filters to opportunity cards
function applyFilters() {
    const sportFilter = document.getElementById('sportFilter').value.toLowerCase();
    const typeFilter = document.getElementById('typeFilter').value.toLowerCase();
    const locationFilter = document.getElementById('locationFilter').value.toLowerCase();
    
    const opportunityCards = document.querySelectorAll('.opportunity-card');
    
    opportunityCards.forEach(card => {
        const cardSport = card.getAttribute('data-sport') || '';
        const cardType = card.getAttribute('data-type') || '';
        const cardLocation = card.querySelector('.detail-item span').textContent.toLowerCase();
        
        const matchesSport = !sportFilter || cardSport.includes(sportFilter);
        const matchesType = !typeFilter || cardType.includes(typeFilter);
        const matchesLocation = !locationFilter || cardLocation.includes(locationFilter);
        
        if (matchesSport && matchesType && matchesLocation) {
            card.style.display = 'block';
            card.style.animation = 'fadeIn 0.3s ease-in-out';
        } else {
            card.style.display = 'none';
        }
    });
    
    // Show message if no results
    const visibleCards = document.querySelectorAll('.opportunity-card[style*="block"]');
    if (visibleCards.length === 0) {
        showNoResultsMessage();
    } else {
        hideNoResultsMessage();
    }
}

// Initialize opportunity actions (apply, save, share)
function initializeOpportunityActions() {
    // Apply buttons
    const applyButtons = document.querySelectorAll('.apply-btn');
    applyButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const opportunityCard = this.closest('.opportunity-card');
            const opportunityTitle = opportunityCard.querySelector('.opportunity-title').textContent;
            handleApplyToOpportunity(opportunityTitle);
        });
    });

    // Save buttons
    const saveButtons = document.querySelectorAll('.save-btn');
    saveButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const opportunityCard = this.closest('.opportunity-card');
            const opportunityTitle = opportunityCard.querySelector('.opportunity-title').textContent;
            handleSaveOpportunity(opportunityTitle, this);
        });
    });

    // Share buttons
    const shareButtons = document.querySelectorAll('.share-btn');
    shareButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const opportunityCard = this.closest('.opportunity-card');
            const opportunityTitle = opportunityCard.querySelector('.opportunity-title').textContent;
            handleShareOpportunity(opportunityTitle);
        });
    });

    // Post opportunity button
    const postOpportunityBtn = document.querySelector('.post-opportunity-btn');
    if (postOpportunityBtn) {
        postOpportunityBtn.addEventListener('click', function() {
            showPostOpportunityModal();
        });
    }

    // My applications button
    const myApplicationsBtn = document.querySelector('.my-applications-btn');
    if (myApplicationsBtn) {
        myApplicationsBtn.addEventListener('click', function() {
            showMyApplications();
        });
    }
}

// Handle apply to opportunity
function handleApplyToOpportunity(opportunityTitle) {
    // Create application modal
    const modal = document.createElement('div');
    modal.className = 'opportunity-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h2>Apply to Opportunity</h2>
                <button class="close-modal-btn"><i class="fas fa-times"></i></button>
            </div>
            <div class="modal-body">
                <h3>${opportunityTitle}</h3>
                <div class="application-form">
                    <div class="form-group">
                        <label>Cover Letter:</label>
                        <textarea placeholder="Write your cover letter..." rows="6"></textarea>
                    </div>
                    <div class="form-group">
                        <label>Upload Resume:</label>
                        <input type="file" accept=".pdf,.doc,.docx">
                    </div>
                    <div class="form-group">
                        <label>Upload Video Highlights:</label>
                        <input type="file" accept="video/*">
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="secondary-button cancel-btn">Cancel</button>
                <button class="primary-button submit-application-btn">Submit Application</button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Close modal functionality
    const closeBtn = modal.querySelector('.close-modal-btn');
    const cancelBtn = modal.querySelector('.cancel-btn');
    [closeBtn, cancelBtn].forEach(btn => {
        btn.addEventListener('click', () => {
            document.body.removeChild(modal);
        });
    });
    
    // Submit application
    const submitBtn = modal.querySelector('.submit-application-btn');
    submitBtn.addEventListener('click', () => {
        showSuccessMessage('Application submitted successfully!');
        document.body.removeChild(modal);
    });
}

// Handle save opportunity
function handleSaveOpportunity(opportunityTitle, button) {
    const icon = button.querySelector('i');
    const isSaved = icon.classList.contains('fas');
    
    if (isSaved) {
        icon.classList.remove('fas');
        icon.classList.add('far');
        showInfoMessage('Opportunity removed from saved list');
    } else {
        icon.classList.remove('far');
        icon.classList.add('fas');
        showSuccessMessage('Opportunity saved successfully!');
    }
}

// Handle share opportunity
function handleShareOpportunity(opportunityTitle) {
    if (navigator.share) {
        navigator.share({
            title: opportunityTitle,
            text: `Check out this opportunity: ${opportunityTitle}`,
            url: window.location.href
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(window.location.href).then(() => {
            showSuccessMessage('Link copied to clipboard!');
        });
    }
}

// Initialize load more functionality
function initializeLoadMore() {
    const loadMoreBtn = document.querySelector('.load-more-btn');
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', function() {
            loadMoreOpportunities();
        });
    }
}

// Load more opportunities
function loadMoreOpportunities() {
    // Simulate loading more opportunities
    const opportunitiesList = document.querySelector('.opportunities-list');
    const loadMoreBtn = document.querySelector('.load-more-btn');
    
    loadMoreBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
    loadMoreBtn.disabled = true;
    
    setTimeout(() => {
        // Add more opportunity cards here
        showInfoMessage('No more opportunities to load');
        loadMoreBtn.innerHTML = '<i class="fas fa-plus"></i> Load More Opportunities';
        loadMoreBtn.disabled = false;
    }, 1500);
}

// Show post opportunity modal
function showPostOpportunityModal() {
    showInfoMessage('Post opportunity feature will be implemented soon');
}

// Show my applications
function showMyApplications() {
    showInfoMessage('My applications feature will be implemented soon');
}

// Show/hide no results message
function showNoResultsMessage() {
    let noResultsMsg = document.querySelector('.no-results-message');
    if (!noResultsMsg) {
        noResultsMsg = document.createElement('div');
        noResultsMsg.className = 'no-results-message';
        noResultsMsg.innerHTML = `
            <div class="no-results-content">
                <i class="fas fa-search"></i>
                <h3>No opportunities found</h3>
                <p>Try adjusting your filters to see more results</p>
            </div>
        `;
        document.querySelector('.opportunities-list').appendChild(noResultsMsg);
    }
    noResultsMsg.style.display = 'block';
}

function hideNoResultsMessage() {
    const noResultsMsg = document.querySelector('.no-results-message');
    if (noResultsMsg) {
        noResultsMsg.style.display = 'none';
    }
}

// Initialize profile dropdown
function initializeProfileDropdown() {
    const toggleButton = document.getElementById('profileDropdownToggle');
    const dropdownMenu = document.getElementById('profileDropdownMenu');
    if (!toggleButton || !dropdownMenu) return;
    
    let isMenuOpen = false;
    
    toggleButton.addEventListener('click', function(e) {
        e.stopPropagation();
        isMenuOpen = !isMenuOpen;
        dropdownMenu.classList.toggle('show', isMenuOpen);
    });
    
    // Close dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!toggleButton.contains(e.target) && !dropdownMenu.contains(e.target)) {
            isMenuOpen = false;
            dropdownMenu.classList.remove('show');
        }
    });
}

// Utility functions for messages
function showSuccessMessage(message) {
    showMessage(message, 'success');
}

function showInfoMessage(message) {
    showMessage(message, 'info');
}

function showMessage(message, type) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.textContent = message;
    
    Object.assign(messageDiv.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        padding: '12px 20px',
        borderRadius: '6px',
        color: 'white',
        backgroundColor: type === 'success' ? '#4caf50' : '#2196f3',
        zIndex: '10000',
        animation: 'slideIn 0.3s ease-out'
    });
    
    document.body.appendChild(messageDiv);
    
    setTimeout(() => {
        messageDiv.style.animation = 'slideOut 0.3s ease-in';
        setTimeout(() => {
            if (document.body.contains(messageDiv)) {
                document.body.removeChild(messageDiv);
            }
        }, 300);
    }, 3000);
}
