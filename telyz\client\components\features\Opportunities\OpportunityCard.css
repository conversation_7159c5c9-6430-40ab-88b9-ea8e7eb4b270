/* Opportunity Card Component Styles */

.opportunity-card {
  background-color: var(--card-color);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-light);
  overflow: hidden;
  transition: all var(--transition-normal);
  width: 100%;
  display: flex;
  flex-direction: column;
}

/* Clickable Card */

.opportunity-card-clickable {
  cursor: pointer;
}

.opportunity-card-clickable:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-md);
}

/* Featured Card */

.opportunity-card-featured {
  border: 2px solid var(--primary-color);
  position: relative;
}

.opportunity-card-featured::before {
  content: "Featured";
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: var(--primary-color);
  color: white;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  padding: 4px 8px;
  border-radius: var(--border-radius-sm);
  z-index: 1;
}

/* Card Header */

.opportunity-header {
  display: flex;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--border-light);
}

.opportunity-logo {
  width: 50px;
  height: 50px;
  border-radius: var(--border-radius-md);
  overflow: hidden;
  margin-right: 15px;
  border: 1px solid var(--border-light);
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.opportunity-logo img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.opportunity-title-container {
  flex: 1;
}

.opportunity-title {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
  margin: 0 0 5px 0;
}

.opportunity-org {
  font-size: var(--font-size-sm);
  color: var(--text-light);
  display: flex;
  align-items: center;
}

.verified-icon {
  color: var(--secondary-color);
  margin-left: 5px;
  font-size: 14px;
}

.opportunity-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-left: 10px;
}

.opportunity-badge {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  padding: 4px 8px;
  border-radius: var(--border-radius-sm);
  white-space: nowrap;
}

.badge-primary {
  background-color: rgba(106, 13, 173, 0.1);
  color: var(--primary-color);
}

.badge-success {
  background-color: rgba(46, 204, 113, 0.1);
  color: var(--secondary-color);
}

.badge-info {
  background-color: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.badge-warning {
  background-color: rgba(241, 196, 15, 0.1);
  color: #f1c40f;
}

.badge-danger {
  background-color: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.badge-secondary {
  background-color: rgba(149, 165, 166, 0.1);
  color: #95a5a6;
}

.badge-filled {
  background-color: rgba(46, 204, 113, 0.1);
  color: var(--secondary-color);
}

.badge-closed {
  background-color: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

/* Card Details */

.opportunity-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  padding: 20px;
}

.opportunity-detail {
  display: flex;
  align-items: center;
}

.opportunity-detail i {
  color: var(--primary-color);
  margin-right: 10px;
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.opportunity-detail span {
  font-size: var(--font-size-sm);
  color: var(--text-color);
}

/* Card Footer */

.opportunity-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-top: 1px solid var(--border-light);
  margin-top: auto;
}

.opportunity-stats {
  font-size: var(--font-size-xs);
  color: var(--text-light);
}

.opportunity-stats i {
  margin-right: 5px;
}

.opportunity-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.opportunity-save-button,
.opportunity-share-button {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: transparent;
  border: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.opportunity-save-button i,
.opportunity-share-button i {
  color: var(--text-light);
  font-size: 16px;
}

.opportunity-save-button:hover,
.opportunity-share-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.opportunity-save-button.saved {
  background-color: rgba(106, 13, 173, 0.1);
  border-color: var(--primary-color);
}

.opportunity-save-button.saved i {
  color: var(--primary-color);
}

.opportunity-apply-button {
  padding: 8px 16px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: background-color var(--transition-normal);
}

.opportunity-apply-button:hover:not(:disabled) {
  background-color: var(--primary-dark);
}

.opportunity-apply-button:disabled {
  background-color: var(--text-muted);
  cursor: not-allowed;
  opacity: 0.7;
}

/* Compact Card */

.opportunity-card-compact {
  flex-direction: row;
  align-items: center;
  padding: 15px;
}

.opportunity-card-compact .opportunity-logo {
  width: 40px;
  height: 40px;
  margin-right: 15px;
}

.opportunity-card-compact .opportunity-content {
  flex: 1;
}

.opportunity-card-compact .opportunity-title {
  font-size: var(--font-size-sm);
  margin-bottom: 3px;
}

.opportunity-card-compact .opportunity-org {
  font-size: var(--font-size-xs);
  margin-bottom: 5px;
}

.opportunity-card-compact .opportunity-meta {
  display: flex;
  gap: 15px;
  font-size: var(--font-size-xs);
  color: var(--text-light);
}

.opportunity-card-compact .opportunity-meta i {
  margin-right: 5px;
}

.opportunity-card-compact .opportunity-actions {
  margin-left: 15px;
}

.opportunity-card-compact .opportunity-apply-button {
  padding: 6px 12px;
  font-size: var(--font-size-xs);
}

/* Card Grid */

.opportunity-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

/* Responsive */

@media (max-width: 768px) {
  .opportunity-details {
    grid-template-columns: 1fr;
  }
  
  .opportunity-header {
    flex-wrap: wrap;
  }
  
  .opportunity-badges {
    margin-left: 0;
    margin-top: 10px;
    width: 100%;
  }
}
