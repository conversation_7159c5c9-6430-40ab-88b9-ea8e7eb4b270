/**
 * Opportunity Card Component
 * 
 * A card component for displaying opportunity information.
 */

class OpportunityCard {
  /**
   * Create an opportunity card component
   * 
   * @param {Object} opportunity - Opportunity data
   * @param {Object} options - Component options
   * @param {boolean} options.compact - Whether to show compact view
   * @param {Function} options.onClick - Click event handler
   * @param {Function} options.onApply - Apply button click handler
   * @param {Function} options.onSave - Save button click handler
   * @param {Function} options.onShare - Share button click handler
   */
  constructor(opportunity, options = {}) {
    this.opportunity = opportunity;
    this.compact = options.compact || false;
    this.onClick = options.onClick || null;
    this.onApply = options.onApply || null;
    this.onSave = options.onSave || null;
    this.onShare = options.onShare || null;
    
    this.element = this.createCardElement();
  }
  
  /**
   * Create the card element
   * 
   * @returns {HTMLElement} Card element
   */
  createCardElement() {
    const card = document.createElement('div');
    card.classList.add('opportunity-card');
    
    if (this.compact) {
      card.classList.add('opportunity-card-compact');
    }
    
    if (this.opportunity.featured) {
      card.classList.add('opportunity-card-featured');
    }
    
    // Add click event handler
    if (this.onClick) {
      card.classList.add('opportunity-card-clickable');
      card.addEventListener('click', (e) => {
        // Don't trigger click if clicking on a button
        if (!e.target.closest('button')) {
          this.onClick(this.opportunity);
        }
      });
    }
    
    // Create card content
    card.innerHTML = this.createCardContent();
    
    // Add event listeners for buttons
    if (this.onApply) {
      const applyButton = card.querySelector('.opportunity-apply-button');
      applyButton.addEventListener('click', (e) => {
        e.stopPropagation();
        this.onApply(this.opportunity);
      });
    }
    
    if (this.onSave) {
      const saveButton = card.querySelector('.opportunity-save-button');
      saveButton.addEventListener('click', (e) => {
        e.stopPropagation();
        this.onSave(this.opportunity);
        
        // Toggle saved state
        saveButton.classList.toggle('saved');
        const icon = saveButton.querySelector('i');
        
        if (saveButton.classList.contains('saved')) {
          icon.className = 'fas fa-bookmark';
        } else {
          icon.className = 'far fa-bookmark';
        }
      });
    }
    
    if (this.onShare) {
      const shareButton = card.querySelector('.opportunity-share-button');
      shareButton.addEventListener('click', (e) => {
        e.stopPropagation();
        this.onShare(this.opportunity);
      });
    }
    
    return card;
  }
  
  /**
   * Create the card content HTML
   * 
   * @returns {string} Card content HTML
   */
  createCardContent() {
    const { 
      id, 
      title, 
      organization, 
      type, 
      sport, 
      positions, 
      location, 
      dates, 
      status, 
      hasSaved, 
      hasApplied 
    } = this.opportunity;
    
    // Format deadline date
    const deadline = new Date(dates.applicationDeadline);
    const now = new Date();
    const daysLeft = Math.ceil((deadline - now) / (1000 * 60 * 60 * 24));
    
    let deadlineText = '';
    if (daysLeft > 0) {
      deadlineText = `${daysLeft} day${daysLeft !== 1 ? 's' : ''} left`;
    } else if (daysLeft === 0) {
      deadlineText = 'Last day to apply';
    } else {
      deadlineText = 'Deadline passed';
    }
    
    // Create badge based on type
    let typeBadgeClass = '';
    switch (type) {
      case 'tryout':
        typeBadgeClass = 'badge-primary';
        break;
      case 'contract':
        typeBadgeClass = 'badge-success';
        break;
      case 'scholarship':
        typeBadgeClass = 'badge-info';
        break;
      case 'training':
        typeBadgeClass = 'badge-warning';
        break;
      case 'coaching':
        typeBadgeClass = 'badge-danger';
        break;
      default:
        typeBadgeClass = 'badge-secondary';
    }
    
    // Create HTML for compact view
    if (this.compact) {
      return `
        <div class="opportunity-logo">
          <img src="${organization.logo || 'https://via.placeholder.com/50'}" alt="${organization.name}">
        </div>
        <div class="opportunity-content">
          <h3 class="opportunity-title">${title}</h3>
          <div class="opportunity-org">${organization.name}</div>
          <div class="opportunity-meta">
            <span class="opportunity-location"><i class="fas fa-map-marker-alt"></i> ${location.city}, ${location.country}</span>
            <span class="opportunity-deadline"><i class="far fa-clock"></i> ${deadlineText}</span>
          </div>
        </div>
        <div class="opportunity-actions">
          <button class="opportunity-apply-button" ${hasApplied ? 'disabled' : ''}>
            ${hasApplied ? 'Applied' : 'Apply'}
          </button>
        </div>
      `;
    }
    
    // Create HTML for full view
    return `
      <div class="opportunity-header">
        <div class="opportunity-logo">
          <img src="${organization.logo || 'https://via.placeholder.com/50'}" alt="${organization.name}">
        </div>
        <div class="opportunity-title-container">
          <h3 class="opportunity-title">${title}</h3>
          <div class="opportunity-org">
            ${organization.name}
            ${organization.verified ? '<i class="fas fa-check-circle verified-icon"></i>' : ''}
          </div>
        </div>
        <div class="opportunity-badges">
          <span class="opportunity-badge ${typeBadgeClass}">${type.charAt(0).toUpperCase() + type.slice(1)}</span>
          ${status === 'filled' ? '<span class="opportunity-badge badge-filled">Filled</span>' : ''}
          ${status === 'closed' ? '<span class="opportunity-badge badge-closed">Closed</span>' : ''}
        </div>
      </div>
      
      <div class="opportunity-details">
        <div class="opportunity-detail">
          <i class="fas fa-running"></i>
          <span>${sport.charAt(0).toUpperCase() + sport.slice(1)}</span>
        </div>
        <div class="opportunity-detail">
          <i class="fas fa-user-tag"></i>
          <span>${positions.join(', ')}</span>
        </div>
        <div class="opportunity-detail">
          <i class="fas fa-map-marker-alt"></i>
          <span>${location.city}, ${location.country}</span>
        </div>
        <div class="opportunity-detail">
          <i class="far fa-clock"></i>
          <span>${deadlineText}</span>
        </div>
      </div>
      
      <div class="opportunity-footer">
        <div class="opportunity-stats">
          <span><i class="fas fa-user-friends"></i> ${this.opportunity.stats?.applications || 0} applicants</span>
        </div>
        <div class="opportunity-actions">
          <button class="opportunity-save-button ${hasSaved ? 'saved' : ''}">
            <i class="${hasSaved ? 'fas' : 'far'} fa-bookmark"></i>
          </button>
          <button class="opportunity-share-button">
            <i class="fas fa-share-alt"></i>
          </button>
          <button class="opportunity-apply-button" ${hasApplied || status !== 'published' ? 'disabled' : ''}>
            ${hasApplied ? 'Applied' : 'Apply Now'}
          </button>
        </div>
      </div>
    `;
  }
  
  /**
   * Render the card to a container
   * 
   * @param {HTMLElement} container - Container element
   */
  render(container) {
    if (container) {
      container.appendChild(this.element);
    }
  }
  
  /**
   * Update the opportunity data
   * 
   * @param {Object} opportunity - New opportunity data
   */
  update(opportunity) {
    this.opportunity = opportunity;
    
    // Update card content
    this.element.innerHTML = this.createCardContent();
    
    // Re-add event listeners
    if (this.onApply) {
      const applyButton = this.element.querySelector('.opportunity-apply-button');
      applyButton.addEventListener('click', (e) => {
        e.stopPropagation();
        this.onApply(this.opportunity);
      });
    }
    
    if (this.onSave) {
      const saveButton = this.element.querySelector('.opportunity-save-button');
      saveButton.addEventListener('click', (e) => {
        e.stopPropagation();
        this.onSave(this.opportunity);
        
        // Toggle saved state
        saveButton.classList.toggle('saved');
        const icon = saveButton.querySelector('i');
        
        if (saveButton.classList.contains('saved')) {
          icon.className = 'fas fa-bookmark';
        } else {
          icon.className = 'far fa-bookmark';
        }
      });
    }
    
    if (this.onShare) {
      const shareButton = this.element.querySelector('.opportunity-share-button');
      shareButton.addEventListener('click', (e) => {
        e.stopPropagation();
        this.onShare(this.opportunity);
      });
    }
  }
}

// Export the OpportunityCard class
if (typeof module !== 'undefined' && module.exports) {
  module.exports = OpportunityCard;
}
