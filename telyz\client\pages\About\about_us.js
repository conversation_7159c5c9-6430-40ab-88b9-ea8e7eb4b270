// About Us page JavaScript functionality
document.addEventListener('DOMContentLoaded', function() {
    
    // ================ PROFILE DROPDOWN FUNCTIONALITY ================ //
    const profileToggle = document.getElementById('profileDropdownToggle');
    const profileMenu = document.getElementById('profileDropdownMenu');
    const userProfile = document.querySelector('.user-profile-sidebar');

    if (profileToggle && profileMenu) {
        let isMenuOpen = false;

        profileToggle.addEventListener('click', function(e) {
            e.stopPropagation();
            isMenuOpen = !isMenuOpen;
            profileMenu.classList.toggle('show', isMenuOpen);
        });

        document.addEventListener('click', function(event) {
            if (!userProfile.contains(event.target)) {
                isMenuOpen = false;
                profileMenu.classList.remove('show');
            }
        });
    }

    // ================ SUBMENU FUNCTIONALITY ================ //
    const aiMenuItem = document.getElementById('aiMenuItem');
    const sportsMenuItem = document.getElementById('sportsMenuItem');

    function handleSubmenuHover(menuItem, submenuSelector) {
        if (menuItem) {
            const submenu = menuItem.querySelector(submenuSelector);
            
            menuItem.addEventListener('mouseenter', function() {
                if (submenu) {
                    submenu.style.display = 'block';
                    submenu.style.maxHeight = '400px';
                }
            });

            menuItem.addEventListener('mouseleave', function() {
                if (submenu) {
                    submenu.style.maxHeight = '0';
                    setTimeout(() => {
                        submenu.style.display = 'none';
                    }, 300);
                }
            });
        }
    }

    handleSubmenuHover(aiMenuItem, '.ai-submenu');
    handleSubmenuHover(sportsMenuItem, '.sports-submenu');

    // ================ SIMPLE ANIMATIONS ================ //
    // Add hover effects to cards
    const cards = document.querySelectorAll('.service-item, .team-member, .value-item, .contact-item');
    
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px)';
            this.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.15)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';
        });
    });

    // ================ SMOOTH SCROLLING ================ //
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });

    // ================ EMAIL LINKS ================ //
    const contactItems = document.querySelectorAll('.contact-item');
    
    contactItems.forEach(item => {
        item.addEventListener('click', function() {
            const email = this.querySelector('p').textContent;
            window.location.href = `mailto:${email}`;
        });
    });

    console.log('About Us page loaded successfully');
});