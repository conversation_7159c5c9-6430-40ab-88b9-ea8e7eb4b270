// Checkout JavaScript

// Global variables
let currentStep = 1;
let selectedPlan = {
    name: 'Pro Player',
    category: 'Player & Amateur',
    basePrice: 29,
    billingCycle: 'monthly'
};
let selectedAddons = [];
let paymentMethod = 'card';
let stripe;
let cardNumber, cardExpiry, cardCvc;

// Initialize checkout
document.addEventListener('DOMContentLoaded', function() {
    initializeCheckout();
    setupEventListeners();
    initializeStripe();
    updateOrderSummary();
});

// Initialize checkout with URL parameters
function initializeCheckout() {
    const urlParams = new URLSearchParams(window.location.search);
    
    // Get plan details from URL parameters
    if (urlParams.get('plan')) {
        selectedPlan.name = urlParams.get('plan');
    }
    if (urlParams.get('category')) {
        selectedPlan.category = urlParams.get('category');
    }
    if (urlParams.get('price')) {
        selectedPlan.basePrice = parseInt(urlParams.get('price'));
    }
    
    // Update UI with plan details
    updatePlanDisplay();
}

// Setup event listeners
function setupEventListeners() {
    // Billing cycle selection
    document.querySelectorAll('.cycle-option').forEach(option => {
        option.addEventListener('click', function() {
            document.querySelectorAll('.cycle-option').forEach(o => o.classList.remove('active'));
            this.classList.add('active');
            selectedPlan.billingCycle = this.dataset.cycle;
            updateOrderSummary();
        });
    });
    
    // Add-on selection
    document.querySelectorAll('.addon-checkbox input').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const addonPrice = parseInt(this.dataset.price);
            const addonName = this.nextElementSibling.querySelector('.addon-name').textContent;
            
            if (this.checked) {
                selectedAddons.push({
                    name: addonName,
                    price: addonPrice
                });
            } else {
                selectedAddons = selectedAddons.filter(addon => addon.name !== addonName);
            }
            updateOrderSummary();
        });
    });
    
    // Payment method selection
    document.querySelectorAll('.payment-option').forEach(option => {
        option.addEventListener('click', function() {
            document.querySelectorAll('.payment-option').forEach(o => o.classList.remove('active'));
            this.classList.add('active');
            paymentMethod = this.dataset.method;
            togglePaymentForms();
        });
    });
    
    // Form validation
    document.querySelectorAll('input').forEach(input => {
        input.addEventListener('blur', validateField);
    });
}

// Initialize Stripe
function initializeStripe() {
    // Replace with your actual Stripe publishable key
    stripe = Stripe('pk_test_51234567890abcdef...');
    
    const elements = stripe.elements();
    
    // Card number element
    cardNumber = elements.create('cardNumber', {
        style: {
            base: {
                fontSize: '16px',
                color: '#374151',
                '::placeholder': {
                    color: '#9ca3af',
                },
            },
        },
    });
    cardNumber.mount('#card-number-element');
    
    // Card expiry element
    cardExpiry = elements.create('cardExpiry', {
        style: {
            base: {
                fontSize: '16px',
                color: '#374151',
                '::placeholder': {
                    color: '#9ca3af',
                },
            },
        },
    });
    cardExpiry.mount('#card-expiry-element');
    
    // Card CVC element
    cardCvc = elements.create('cardCvc', {
        style: {
            base: {
                fontSize: '16px',
                color: '#374151',
                '::placeholder': {
                    color: '#9ca3af',
                },
            },
        },
    });
    cardCvc.mount('#card-cvc-element');
    
    // Handle card brand changes
    cardNumber.on('change', function(event) {
        const cardIcon = document.getElementById('card-type-icon');
        if (event.brand) {
            switch(event.brand) {
                case 'visa':
                    cardIcon.style.backgroundImage = "url('https://img.icons8.com/color/32/visa.png')";
                    break;
                case 'mastercard':
                    cardIcon.style.backgroundImage = "url('https://img.icons8.com/color/32/mastercard.png')";
                    break;
                default:
                    cardIcon.style.backgroundImage = '';
            }
        }
    });
}

// Initialize PayPal
function initializePayPal() {
    if (typeof paypal !== 'undefined') {
        paypal.Buttons({
            createOrder: function(data, actions) {
                return actions.order.create({
                    purchase_units: [{
                        amount: {
                            value: calculateTotal().toFixed(2)
                        }
                    }]
                });
            },
            onApprove: function(data, actions) {
                return actions.order.capture().then(function(details) {
                    console.log('PayPal payment successful:', details);
                    showSuccessModal();
                });
            },
            onError: function(err) {
                console.error('PayPal payment error:', err);
                alert('Payment failed. Please try again.');
            }
        }).render('#paypal-button-container');
    }
}

// Update plan display
function updatePlanDisplay() {
    document.getElementById('selected-plan-name').textContent = selectedPlan.name;
    document.getElementById('selected-plan-category').textContent = selectedPlan.category;
    document.getElementById('selected-plan-price').textContent = selectedPlan.basePrice;
    
    // Update pricing in cycle options
    document.getElementById('monthly-price').textContent = selectedPlan.basePrice;
    document.getElementById('yearly-price').textContent = selectedPlan.basePrice * 10; // 10 months for yearly (2 months free)
}

// Update order summary
function updateOrderSummary() {
    const basePrice = selectedPlan.basePrice;
    const isYearly = selectedPlan.billingCycle === 'yearly';
    const planPrice = isYearly ? basePrice * 10 : basePrice;
    const addonsTotal = selectedAddons.reduce((total, addon) => total + (isYearly ? addon.price * 10 : addon.price), 0);
    const discount = isYearly ? basePrice * 2 + selectedAddons.reduce((total, addon) => total + addon.price * 2, 0) : 0;
    const total = planPrice + addonsTotal - discount;
    
    // Update all summary sections
    const summarySelectors = ['summary-plan', 'summary-plan-2', 'summary-plan-3'];
    const addonsSelectors = ['summary-addons', 'summary-addons-2', 'summary-addons-3'];
    const discountSelectors = ['summary-discount', 'summary-discount-2', 'summary-discount-3'];
    const totalSelectors = ['summary-total', 'summary-total-2', 'summary-total-3'];
    
    // Update plan price
    summarySelectors.forEach(selector => {
        const element = document.getElementById(selector);
        if (element) {
            element.textContent = `$${planPrice}${isYearly ? '/year' : '/month'}`;
        }
    });
    
    // Update add-ons
    addonsSelectors.forEach(selector => {
        const element = document.getElementById(selector);
        if (element) {
            element.innerHTML = '';
            selectedAddons.forEach(addon => {
                const addonDiv = document.createElement('div');
                addonDiv.className = 'addon-summary-item';
                addonDiv.innerHTML = `
                    <span>${addon.name}</span>
                    <span>+$${isYearly ? addon.price * 10 : addon.price}${isYearly ? '/year' : '/month'}</span>
                `;
                element.appendChild(addonDiv);
            });
        }
    });
    
    // Update discount
    discountSelectors.forEach(selector => {
        const element = document.getElementById(selector);
        if (element) {
            if (discount > 0) {
                element.style.display = 'flex';
                element.querySelector('.discount-amount').textContent = `-$${discount}`;
            } else {
                element.style.display = 'none';
            }
        }
    });
    
    // Update total
    totalSelectors.forEach(selector => {
        const element = document.getElementById(selector);
        if (element) {
            element.textContent = `$${total}${isYearly ? '/year' : '/month'}`;
        }
    });
}

// Calculate total price
function calculateTotal() {
    const basePrice = selectedPlan.basePrice;
    const isYearly = selectedPlan.billingCycle === 'yearly';
    const planPrice = isYearly ? basePrice * 10 : basePrice;
    const addonsTotal = selectedAddons.reduce((total, addon) => total + (isYearly ? addon.price * 10 : addon.price), 0);
    const discount = isYearly ? basePrice * 2 + selectedAddons.reduce((total, addon) => total + addon.price * 2, 0) : 0;
    
    return planPrice + addonsTotal - discount;
}

// Toggle payment forms
function togglePaymentForms() {
    const cardForm = document.getElementById('card-form');
    const paypalForm = document.getElementById('paypal-form');
    
    if (paymentMethod === 'card') {
        cardForm.style.display = 'block';
        paypalForm.style.display = 'none';
    } else if (paymentMethod === 'paypal') {
        cardForm.style.display = 'none';
        paypalForm.style.display = 'block';
        initializePayPal();
    }
}

// Navigation functions
function nextStep() {
    if (validateCurrentStep()) {
        if (currentStep < 3) {
            currentStep++;
            updateStepDisplay();
            updateFinalReview();
        }
    }
}

function previousStep() {
    if (currentStep > 1) {
        currentStep--;
        updateStepDisplay();
    }
}

function goBack() {
    if (currentStep > 1) {
        previousStep();
    } else {
        // Go back to subscription page
        window.location.href = 'subscription.html';
    }
}

// Update step display
function updateStepDisplay() {
    // Update progress bar
    const progressBar = document.querySelector('.progress-bar');
    progressBar.className = `progress-bar step-${currentStep}`;
    
    // Update progress steps
    document.querySelectorAll('.progress-step').forEach((step, index) => {
        step.classList.remove('active', 'completed');
        if (index + 1 < currentStep) {
            step.classList.add('completed');
        } else if (index + 1 === currentStep) {
            step.classList.add('active');
        }
    });
    
    // Update step content
    document.querySelectorAll('.step-content').forEach((content, index) => {
        content.classList.remove('active');
        if (index + 1 === currentStep) {
            content.classList.add('active');
        }
    });
}

// Update final review
function updateFinalReview() {
    if (currentStep === 3) {
        // Plan details
        document.getElementById('final-plan-name').textContent = selectedPlan.name;
        document.getElementById('final-billing-cycle').textContent = selectedPlan.billingCycle === 'yearly' ? 'Yearly' : 'Monthly';
        document.getElementById('final-plan-price').textContent = document.getElementById('summary-plan-3').textContent;
        
        // Add-ons
        const addonsSection = document.getElementById('final-addons-section');
        const addonsList = document.getElementById('final-addons-list');
        
        if (selectedAddons.length > 0) {
            addonsSection.style.display = 'block';
            addonsList.innerHTML = '';
            selectedAddons.forEach(addon => {
                const addonDiv = document.createElement('div');
                addonDiv.className = 'detail-item';
                addonDiv.innerHTML = `
                    <span>${addon.name}:</span>
                    <span>+$${selectedPlan.billingCycle === 'yearly' ? addon.price * 10 : addon.price}${selectedPlan.billingCycle === 'yearly' ? '/year' : '/month'}</span>
                `;
                addonsList.appendChild(addonDiv);
            });
        } else {
            addonsSection.style.display = 'none';
        }
        
        // Payment method
        document.getElementById('final-payment-method').textContent = paymentMethod === 'card' ? 'Credit Card' : 'PayPal';
        
        // Customer info
        const firstName = document.getElementById('first-name').value;
        const lastName = document.getElementById('last-name').value;
        const email = document.getElementById('email').value;
        const address = document.getElementById('address').value;
        const city = document.getElementById('city').value;
        const postalCode = document.getElementById('postal-code').value;
        
        document.getElementById('final-customer-name').textContent = `${firstName} ${lastName}`;
        document.getElementById('final-customer-email').textContent = email;
        document.getElementById('final-customer-address').textContent = `${address}, ${city}, ${postalCode}`;
    }
}

// Validation
function validateCurrentStep() {
    switch (currentStep) {
        case 1:
            return true; // Plan selection is always valid
        case 2:
            return validatePaymentStep();
        case 3:
            return validateFinalStep();
        default:
            return true;
    }
}

function validatePaymentStep() {
    const requiredFields = ['first-name', 'last-name', 'email', 'address', 'city', 'postal-code'];
    let isValid = true;
    
    requiredFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (!field.value.trim()) {
            showFieldError(field, 'This field is required');
            isValid = false;
        } else {
            clearFieldError(field);
        }
    });
    
    // Email validation
    const email = document.getElementById('email');
    if (email.value && !isValidEmail(email.value)) {
        showFieldError(email, 'Please enter a valid email address');
        isValid = false;
    }
    
    return isValid;
}

function validateFinalStep() {
    const termsCheckbox = document.getElementById('terms-checkbox');
    if (!termsCheckbox.checked) {
        alert('Please accept the Terms of Service and Privacy Policy to continue.');
        return false;
    }
    return true;
}

function validateField(event) {
    const field = event.target;
    const value = field.value.trim();
    
    if (field.hasAttribute('required') && !value) {
        showFieldError(field, 'This field is required');
    } else if (field.type === 'email' && value && !isValidEmail(value)) {
        showFieldError(field, 'Please enter a valid email address');
    } else {
        clearFieldError(field);
    }
}

function showFieldError(field, message) {
    clearFieldError(field);
    field.style.borderColor = '#ef4444';
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error';
    errorDiv.style.color = '#ef4444';
    errorDiv.style.fontSize = '0.875rem';
    errorDiv.style.marginTop = '0.25rem';
    errorDiv.textContent = message;
    
    field.parentNode.appendChild(errorDiv);
}

function clearFieldError(field) {
    field.style.borderColor = '';
    const existingError = field.parentNode.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Complete order
async function completeOrder() {
    if (!validateFinalStep()) {
        return;
    }
    
    const completeBtn = document.getElementById('complete-order-btn');
    completeBtn.disabled = true;
    completeBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
    
    try {
        if (paymentMethod === 'card') {
            await processStripePayment();
        } else if (paymentMethod === 'paypal') {
            // PayPal payment is handled by the PayPal button
            showSuccessModal();
        }
    } catch (error) {
        console.error('Payment error:', error);
        alert('Payment failed. Please try again.');
        completeBtn.disabled = false;
        completeBtn.innerHTML = '<i class="fas fa-lock"></i> Complete Order';
    }
}

// Process Stripe payment
async function processStripePayment() {
    const { token, error } = await stripe.createToken(cardNumber, {
        name: document.getElementById('cardholder-name').value,
        address_line1: document.getElementById('address').value,
        address_city: document.getElementById('city').value,
        address_zip: document.getElementById('postal-code').value,
        address_country: document.getElementById('country').value,
    });
    
    if (error) {
        throw error;
    }
    
    // Send token to your server
    const response = await fetch('/api/process-payment', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            token: token.id,
            amount: Math.round(calculateTotal() * 100), // Amount in cents
            currency: 'usd',
            plan: selectedPlan,
            addons: selectedAddons,
            customer: {
                name: `${document.getElementById('first-name').value} ${document.getElementById('last-name').value}`,
                email: document.getElementById('email').value,
                address: {
                    line1: document.getElementById('address').value,
                    city: document.getElementById('city').value,
                    postal_code: document.getElementById('postal-code').value,
                    country: document.getElementById('country').value,
                }
            }
        }),
    });
    
    const result = await response.json();
    
    if (result.success) {
        showSuccessModal();
    } else {
        throw new Error(result.error || 'Payment failed');
    }
}

// Show success modal
function showSuccessModal() {
    const modal = document.getElementById('success-modal');
    modal.classList.add('active');
}

// Close modal
function closeModal() {
    const modal = document.getElementById('success-modal');
    modal.classList.remove('active');
}

// Go to dashboard
function goToDashboard() {
    // Redirect to dashboard or profile page
    window.location.href = '../Dashboard/dashboard.html';
}

// Handle browser back button
window.addEventListener('popstate', function() {
    goBack();
});

// Add keyboard navigation
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeModal();
    }
});

// Auto-save form data to localStorage
function saveFormData() {
    const formData = {
        selectedPlan,
        selectedAddons,
        paymentMethod,
        customerInfo: {
            firstName: document.getElementById('first-name').value,
            lastName: document.getElementById('last-name').value,
            email: document.getElementById('email').value,
            address: document.getElementById('address').value,
            city: document.getElementById('city').value,
            postalCode: document.getElementById('postal-code').value,
            country: document.getElementById('country').value,
        }
    };
    
    localStorage.setItem('telyz-checkout-data', JSON.stringify(formData));
}

// Load saved form data
function loadFormData() {
    const savedData = localStorage.getItem('telyz-checkout-data');
    if (savedData) {
        const data = JSON.parse(savedData);
        
        // Restore customer info
        if (data.customerInfo) {
            Object.keys(data.customerInfo).forEach(key => {
                const field = document.getElementById(key.replace(/([A-Z])/g, '-$1').toLowerCase());
                if (field) {
                    field.value = data.customerInfo[key];
                }
            });
        }
    }
}

// Auto-save on form changes
document.addEventListener('input', function() {
    saveFormData();
});

// Load saved data on page load
document.addEventListener('DOMContentLoaded', function() {
    loadFormData();
});