/**
 * AI Analysis Model
 * 
 * Defines the schema for AI analysis results in the Telyz platform.
 */

const mongoose = require('mongoose');

const AIAnalysisSchema = new mongoose.Schema({
  // Reference to user
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  
  // Analysis metadata
  title: {
    type: String,
    required: true,
    trim: true,
  },
  description: {
    type: String,
    trim: true,
  },
  sport: {
    type: String,
    enum: ['football', 'basketball', 'volleyball', 'baseball', 'cricket', 'hockey'],
    required: true,
  },
  position: {
    type: String,
    required: true,
    trim: true,
  },
  
  // Video information
  originalVideo: {
    url: {
      type: String,
      required: true,
    },
    duration: {
      type: Number, // in seconds
      required: true,
    },
    size: {
      type: Number, // in bytes
      required: true,
    },
    format: {
      type: String,
      required: true,
    },
  },
  analyzedVideo: {
    url: {
      type: String,
    },
    annotations: {
      type: Boolean,
      default: false,
    },
    heatmap: {
      type: Boolean,
      default: false,
    },
  },
  
  // Analysis results
  results: {
    // General metrics
    metrics: {
      passAccuracy: {
        type: Number, // percentage
        min: 0,
        max: 100,
      },
      distance: {
        type: Number, // in km
        min: 0,
      },
      keyActions: {
        type: Number,
        min: 0,
      },
      // Additional metrics based on sport
    },
    
    // Detailed analysis
    technicalAnalysis: [{
      category: {
        type: String,
        required: true,
        trim: true,
      },
      score: {
        type: Number,
        min: 0,
        max: 100,
        required: true,
      },
      strengths: [{
        type: String,
        trim: true,
      }],
      weaknesses: [{
        type: String,
        trim: true,
      }],
      recommendations: [{
        type: String,
        trim: true,
      }],
    }],
    
    // Positional analysis
    positionalAnalysis: {
      heatmap: {
        type: String, // URL to heatmap image
      },
      positioning: {
        type: Number,
        min: 0,
        max: 100,
      },
      movement: {
        type: Number,
        min: 0,
        max: 100,
      },
      spatialAwareness: {
        type: Number,
        min: 0,
        max: 100,
      },
    },
    
    // Key moments
    keyMoments: [{
      timestamp: {
        type: Number, // in seconds
        required: true,
      },
      title: {
        type: String,
        required: true,
        trim: true,
      },
      description: {
        type: String,
        trim: true,
      },
      type: {
        type: String,
        enum: ['highlight', 'mistake', 'technique', 'tactical'],
        required: true,
      },
      thumbnailUrl: {
        type: String,
      },
    }],
    
    // Comparison with professionals
    professionalComparison: [{
      playerName: {
        type: String,
        required: true,
        trim: true,
      },
      playerTeam: {
        type: String,
        trim: true,
      },
      playerImage: {
        type: String,
      },
      similarityScore: {
        type: Number,
        min: 0,
        max: 100,
        required: true,
      },
      similarities: [{
        type: String,
        trim: true,
      }],
      differences: [{
        type: String,
        trim: true,
      }],
    }],
    
    // Overall assessment
    overallAssessment: {
      rating: {
        type: Number,
        min: 0,
        max: 10,
        required: true,
      },
      summary: {
        type: String,
        required: true,
        trim: true,
      },
      potential: {
        type: Number,
        min: 0,
        max: 100,
      },
      recommendations: [{
        type: String,
        trim: true,
      }],
    },
  },
  
  // Report information
  report: {
    pdfUrl: {
      type: String,
    },
    jsonUrl: {
      type: String,
    },
    generatedAt: {
      type: Date,
    },
  },
  
  // Processing status
  status: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'failed'],
    default: 'pending',
  },
  progress: {
    type: Number,
    min: 0,
    max: 100,
    default: 0,
  },
  error: {
    message: {
      type: String,
    },
    code: {
      type: String,
    },
    timestamp: {
      type: Date,
    },
  },
  
  // Sharing and visibility
  isPublic: {
    type: Boolean,
    default: false,
  },
  sharedWith: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    sharedAt: {
      type: Date,
      default: Date.now,
    },
  }],
  
  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
  completedAt: {
    type: Date,
  },
});

// Pre-save middleware to update timestamps
AIAnalysisSchema.pre('save', function(next) {
  // Update updatedAt
  this.updatedAt = Date.now();
  
  // Update completedAt if status changed to completed
  if (this.isModified('status') && this.status === 'completed') {
    this.completedAt = Date.now();
  }
  
  next();
});

module.exports = mongoose.model('AIAnalysis', AIAnalysisSchema);
