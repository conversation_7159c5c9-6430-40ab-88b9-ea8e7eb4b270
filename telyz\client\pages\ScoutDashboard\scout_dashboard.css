* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.dashboard-container {
    max-width: 1400px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.95);
    min-height: 100vh;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
}

/* Header */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.header-left .logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo-container {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    border: 3px solid rgba(255, 255, 255, 0.3);
}

.logo-text {
    font-size: 22px;
    font-weight: 600;
}

.header-center h1 {
    font-size: 28px;
    font-weight: 600;
    text-align: center;
}

.header-center i {
    margin-right: 10px;
    color: #ffd700;
}

.scout-profile {
    display: flex;
    align-items: center;
    gap: 12px;
}

.scout-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    border: 3px solid rgba(255, 255, 255, 0.3);
}

.scout-name {
    font-weight: 600;
    font-size: 16px;
}

.scout-role {
    font-size: 14px;
    opacity: 0.8;
}

/* Main Content */
.dashboard-main {
    padding: 30px;
}

/* Stats Section */
.stats-section {
    margin-bottom: 30px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
}

.stat-card:nth-child(1) .stat-icon { background: linear-gradient(135deg, #667eea, #764ba2); }
.stat-card:nth-child(2) .stat-icon { background: linear-gradient(135deg, #f093fb, #f5576c); }
.stat-card:nth-child(3) .stat-icon { background: linear-gradient(135deg, #4facfe, #00f2fe); }
.stat-card:nth-child(4) .stat-icon { background: linear-gradient(135deg, #43e97b, #38f9d7); }

.stat-content h3 {
    font-size: 32px;
    font-weight: 700;
    color: #333;
    margin-bottom: 5px;
}

.stat-content p {
    color: #666;
    font-weight: 500;
}

/* Control Panel */
.control-panel {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.panel-header h2 {
    color: #333;
    font-size: 24px;
    font-weight: 600;
}

.panel-header i {
    color: #667eea;
    margin-right: 10px;
}

.panel-actions {
    display: flex;
    gap: 15px;
}

.btn-action {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

/* Filters */
.filters-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: center;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-weight: 500;
    color: #555;
    font-size: 14px;
}

.filter-group select {
    padding: 10px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    background: white;
    color: #333;
    font-size: 14px;
    min-width: 140px;
    transition: border-color 0.3s ease;
}

.filter-group select:focus {
    outline: none;
    border-color: #667eea;
}

.search-box {
    position: relative;
    margin-left: auto;
}

.search-box i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
}

.search-box input {
    padding: 12px 12px 12px 40px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    width: 250px;
    transition: border-color 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #667eea;
}

/* Players Section */
.players-section {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.section-header h2 {
    color: #333;
    font-size: 24px;
    font-weight: 600;
}

.section-header i {
    color: #667eea;
    margin-right: 10px;
}

.view-toggle {
    display: flex;
    gap: 5px;
}

.view-btn {
    width: 40px;
    height: 40px;
    border: 2px solid #e1e5e9;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.view-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.view-btn:hover {
    border-color: #667eea;
}

/* Players Container */
.players-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 25px;
}

.player-card {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 25px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.player-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border-color: #667eea;
}

.player-header {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 20px;
}

.player-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 3px solid #667eea;
}

.player-basic-info {
    flex: 1;
}

.player-basic-info h3 {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.player-position {
    color: #667eea;
    font-weight: 500;
    margin-bottom: 8px;
}

.player-details {
    display: flex;
    gap: 15px;
    color: #666;
    font-size: 14px;
}

.player-stats {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #667eea;
    font-weight: 600;
}

.priority-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 5px;
}

.priority-badge.high {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
}

.priority-badge.medium {
    background: linear-gradient(135deg, #feca57, #ff9ff3);
    color: white;
}

.priority-badge.low {
    background: linear-gradient(135deg, #48dbfb, #0abde3);
    color: white;
}

/* Player Metrics */
.player-metrics {
    margin-bottom: 20px;
}

.metric {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 12px;
}

.metric-label {
    min-width: 80px;
    font-weight: 500;
    color: #555;
    font-size: 14px;
}

.metric-bar {
    flex: 1;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    position: relative;
    overflow: hidden;
}

.metric-fill {
    height: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 4px;
    transition: width 0.5s ease;
}

.metric-value {
    min-width: 40px;
    text-align: right;
    font-weight: 600;
    color: #667eea;
    font-size: 14px;
}

/* Player Notes */
.player-notes {
    margin-bottom: 20px;
}

.player-notes h4 {
    color: #333;
    font-size: 16px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.player-notes i {
    color: #ffc107;
}

.player-notes textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    font-family: inherit;
    resize: vertical;
    transition: border-color 0.3s ease;
}

.player-notes textarea:focus {
    outline: none;
    border-color: #667eea;
}

/* Player Actions */
.player-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
}

.action-btn {
    padding: 10px 15px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.action-btn.view-profile {
    background: #667eea;
    color: white;
}

.action-btn.contact-player {
    background: #28a745;
    color: white;
}

.action-btn.contact-player.contacted {
    background: #6c757d;
    cursor: default;
}

.action-btn.contact-player.urgent {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    animation: pulse 2s infinite;
}

.action-btn.remove-watch {
    background: #dc3545;
    color: white;
}

.action-btn:hover:not(.contacted) {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.priority-selector {
    margin-left: auto;
}

.priority-selector select {
    padding: 8px 12px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    background: white;
    font-size: 13px;
    cursor: pointer;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 20px 25px;
    border-radius: 15px 15px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 20px;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 28px;
    cursor: pointer;
    line-height: 1;
}

.modal-body {
    padding: 25px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    font-family: inherit;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
}

.modal-footer {
    padding: 20px 25px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

.btn-cancel,
.btn-confirm {
    padding: 12px 25px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-cancel {
    background: #6c757d;
    color: white;
}

.btn-confirm {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-cancel:hover,
.btn-confirm:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Animations */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 107, 107, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 107, 107, 0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .dashboard-main {
        padding: 20px;
    }
    
    .filters-row {
        justify-content: center;
    }
    
    .search-box input {
        width: 200px;
    }
    
    .players-container {
        grid-template-columns: 1fr;
    }
    
    .player-actions {
        justify-content: center;
    }
    
    .priority-selector {
        margin-left: 0;
        margin-top: 10px;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .panel-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .filters-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-box {
        margin-left: 0;
    }
    
    .search-box input {
        width: 100%;
    }
}