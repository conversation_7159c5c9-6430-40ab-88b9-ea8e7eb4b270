document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const chatMessages = document.getElementById('chatMessages');
    const chatInput = document.getElementById('chatInput');
    const sendMessageBtn = document.getElementById('sendMessageBtn');
    const toolsBtn = document.getElementById('toolsBtn');
    const toolsDropdown = document.getElementById('toolsDropdown');
    const uploadVideoBtn = document.getElementById('uploadVideoBtn');
    const uploadDocBtn = document.getElementById('uploadDocBtn');
    const recordAudioBtn = document.getElementById('recordAudioBtn');
    const screenshotBtn = document.getElementById('screenshotBtn');
    const videoOverlay = document.querySelector('.video-overlay');
    const playButton = document.querySelector('.play-button');
    const mainVideo = document.getElementById('mainVideo');
    const themeToggle = document.querySelector('.theme-toggle');
    const themePopup = document.querySelector('.theme-popup');
    const themeOptions = document.querySelectorAll('.theme-option');
    
    // Video action buttons
    const uploadVideoButton = document.getElementById('mainUploadVideoBtn');
    const downloadPdfButton = document.getElementById('mainDownloadPdfBtn');
    const shareButton = document.getElementById('mainShareBtn');
    const videoDuration = document.querySelector('.video-duration');
    
    // Initialize
    initializeChat();
    
    // Event Listeners
    if (sendMessageBtn) {
        sendMessageBtn.addEventListener('click', sendMessage);
    }
    
    if (chatInput) {
        chatInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
        
        // Auto-resize textarea
        chatInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
        });
    }
    
    // Tools dropdown toggle
    if (toolsBtn && toolsDropdown) {
        toolsBtn.addEventListener('click', function(e) {
            e.preventDefault();
            toolsDropdown.classList.toggle('active');
            console.log('Tools button clicked, dropdown toggled');
        });
    }
    
    // Close dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (toolsBtn && toolsDropdown && !toolsBtn.contains(e.target) && !toolsDropdown.contains(e.target)) {
            toolsDropdown.classList.remove('active');
        }
    });
    
    // Tool button event listeners
    if (uploadVideoBtn) {
        uploadVideoBtn.addEventListener('click', function() {
            toolsDropdown.classList.remove('active');
            
            // Simulate file upload dialog
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'video/*';
            input.onchange = function(e) {
                if (e.target.files.length > 0) {
                    const file = e.target.files[0];
                    const fileName = file.name;
                    
                    // Create video URL
                    const videoURL = URL.createObjectURL(file);
                    
                    // Add message about the upload with embedded video
                    const videoMessage = `
                        <div>I've uploaded a video: ${fileName}</div>
                        <div class="chat-video-preview">
                            <video controls width="100%">
                                <source src="${videoURL}" type="${file.type}">
                                Your browser does not support the video tag.
                            </video>
                        </div>
                    `;
                    
                    // Add the message to chat
                    addMessage({
                        role: 'user',
                        content: videoMessage
                    });
                    
                    // Update the main video player with the uploaded video
                    if (mainVideo) {
                        // Update source
                        const source = mainVideo.querySelector('source') || document.createElement('source');
                        source.src = videoURL;
                        source.type = file.type;
                        
                        if (!mainVideo.querySelector('source')) {
                            mainVideo.appendChild(source);
                        }
                        
                        // Reset and load the video
                        mainVideo.load();
                        
                        // Update video title
                        const videoTitleElement = document.querySelector('.video-title');
                        if (videoTitleElement) {
                            videoTitleElement.textContent = fileName;
                        }
                        
                        // Show overlay
                        if (videoOverlay) {
                            videoOverlay.style.display = 'flex';
                            videoOverlay.style.opacity = '1';
                        }
                    }
                    
                    // Get AI response
                    getResponseFromAI("I've uploaded a video: " + fileName);
                }
            };
            input.click();
        });
    }
    
    if (uploadDocBtn) {
        uploadDocBtn.addEventListener('click', function() {
            toolsDropdown.classList.remove('active');
            alert('Document upload functionality will be implemented soon.');
        });
    }
    
    if (recordAudioBtn) {
        recordAudioBtn.addEventListener('click', function() {
            toolsDropdown.classList.remove('active');
            alert('Audio recording functionality will be implemented soon.');
        });
    }
    
    if (screenshotBtn) {
        screenshotBtn.addEventListener('click', function() {
            toolsDropdown.classList.remove('active');
            alert('Screenshot functionality will be implemented soon.');
        });
    }
    
    // Functions
    function initializeChat() {
        console.log('Initializing chat interface...');
        
        // Simple video play functionality
        if (videoOverlay && mainVideo) {
            videoOverlay.addEventListener('click', function() {
                mainVideo.play();
                videoOverlay.style.display = 'none';
            });
            
            // Show overlay when video is paused
            mainVideo.addEventListener('pause', function() {
                videoOverlay.style.display = 'flex';
            });
            
            // Update video duration display
            mainVideo.addEventListener('loadedmetadata', function() {
                updateVideoDuration();
            });
            
            // Update progress bar and time
            mainVideo.addEventListener('timeupdate', function() {
                updateVideoProgress();
            });
            
            // Make progress bar clickable to seek
            const progressBar = document.querySelector('.progress-bar');
            if (progressBar) {
                progressBar.addEventListener('click', function(e) {
                    const rect = this.getBoundingClientRect();
                    const pos = (e.clientX - rect.left) / rect.width;
                    mainVideo.currentTime = pos * mainVideo.duration;
                    updateVideoProgress();
                });
            }
        }
        
        // Video action buttons functionality
        if (uploadVideoButton) {
            uploadVideoButton.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Upload video button clicked');
                
                // Create file input and trigger click
                const input = document.createElement('input');
                input.type = 'file';
                input.accept = 'video/*';
                input.onchange = function(e) {
                    if (e.target.files.length > 0) {
                        const file = e.target.files[0];
                        const fileName = file.name;
                        console.log('File selected:', fileName);
                        
                        // Create video URL
                        const videoURL = URL.createObjectURL(file);
                        
                        // Update the main video player with the uploaded video
                        if (mainVideo) {
                            // Update source
                            const source = mainVideo.querySelector('source') || document.createElement('source');
                            source.src = videoURL;
                            source.type = file.type;
                            
                            if (!mainVideo.querySelector('source')) {
                                mainVideo.appendChild(source);
                            }
                            
                            // Reset and load the video
                            mainVideo.load();
                            
                            // Update video title
                            const videoTitleElement = document.querySelector('.video-title');
                            if (videoTitleElement) {
                                videoTitleElement.textContent = fileName;
                            }
                            
                            // Add success message in chat
                            addMessage({
                                role: 'assistant',
                                content: `<p>Video "${fileName}" has been loaded successfully.</p><p>Click the play button to start watching.</p>`
                            });
                        }
                    }
                };
                input.click();
            });
        }
        
        if (downloadPdfButton) {
            downloadPdfButton.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Download PDF button clicked');
                
                // Simulate PDF download
                alert('Downloading Guardiola Tactical Analysis Report...');
                
                // In a real application, this would trigger a download
                // For demonstration, we'll just show an alert
                setTimeout(() => {
                    alert('Download complete!');
                }, 1500);
            });
        }
        
        if (shareButton) {
            shareButton.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Share button clicked');
                
                // Simulate sharing functionality
                alert('Sharing options will be implemented soon.');
            });
        }
        
        // Theme toggle functionality
        if (themeToggle) {
            themeToggle.addEventListener('click', function(e) {
                themePopup.classList.toggle('active');
                e.stopPropagation();
            });
            
            // Close theme popup when clicking outside
            document.addEventListener('click', function(e) {
                if (!themeToggle.contains(e.target) && !themePopup.contains(e.target)) {
                    themePopup.classList.remove('active');
                }
            });
        }
        
        // Theme options
        if (themeOptions) {
            themeOptions.forEach(option => {
                option.addEventListener('click', function() {
                    const theme = this.getAttribute('data-theme');
                    changeTheme(theme);
                    themePopup.classList.remove('active');
                });
            });
        }
    }
    
    // Function to change theme
    function changeTheme(theme) {
        const root = document.documentElement;
        
        // Remove any existing theme classes
        document.body.classList.remove('theme-light', 'theme-dark', 'theme-current');
        
        // Add the selected theme class
        document.body.classList.add('theme-' + theme);
        
        // Update the theme icon
        if (themeToggle) {
            const icon = themeToggle.querySelector('i');
            
            if (theme === 'light') {
                icon.className = 'fas fa-sun';
            } else if (theme === 'dark') {
                icon.className = 'fas fa-moon';
            } else {
                icon.className = 'fas fa-adjust';
            }
        }
        
        console.log('Theme changed to:', theme);
    }
    
    // Function to update video duration display
    function updateVideoDuration() {
        if (mainVideo && videoDuration) {
            const duration = formatTime(mainVideo.duration);
            const currentTime = formatTime(mainVideo.currentTime);
            videoDuration.textContent = `${currentTime} / ${duration}`;
        }
    }
    
    // Function to update video progress
    function updateVideoProgress() {
        if (mainVideo) {
            // Update progress bar
            const progressFill = document.querySelector('.progress-fill');
            if (progressFill && mainVideo.duration) {
                const percentage = (mainVideo.currentTime / mainVideo.duration) * 100;
                progressFill.style.width = `${percentage}%`;
            }
            
            // Update time display
            if (videoDuration) {
                const duration = formatTime(mainVideo.duration);
                const currentTime = formatTime(mainVideo.currentTime);
                videoDuration.textContent = `${currentTime} / ${duration}`;
            }
        }
    }
    
    // Helper function to format time (seconds to MM:SS)
    function formatTime(seconds) {
        if (isNaN(seconds) || !isFinite(seconds)) {
            return '00:00';
        }
        
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        
        const formattedMinutes = String(minutes).padStart(2, '0');
        const formattedSeconds = String(remainingSeconds).padStart(2, '0');
        
        return `${formattedMinutes}:${formattedSeconds}`;
    }
    
    function sendMessage() {
        const message = chatInput.value.trim();
        
        if (!message) return;
        
        // Add user message to chat
        addMessage({
            role: 'user',
            content: message
        });
        
        // Clear input
        chatInput.value = '';
        chatInput.style.height = 'auto';
        
        // Get AI response
        getResponseFromAI(message);
    }
    
    function addMessage(message) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${message.role}`;
        
        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        contentDiv.innerHTML = message.content;
        
        const timeDiv = document.createElement('div');
        timeDiv.className = 'message-time';
        timeDiv.textContent = 'Now';
        
        messageDiv.appendChild(contentDiv);
        messageDiv.appendChild(timeDiv);
        
        chatMessages.appendChild(messageDiv);
        
        // Scroll to bottom
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
    
    function getResponseFromAI(message) {
        // Simulate AI thinking
        setTimeout(() => {
            let response;
            
            if (message.includes('uploaded a video') || message.includes('رفعت فيديو')) {
                response = `
                    <p>ممتاز! لقد استلمت الفيديو وأحلله الآن...</p>
                    <div class="analysis-preview">
                        <p><strong>الملاحظات الأولية:</strong></p>
                        <ul>
                            <li>دقة التمرير: 87% - ممتازة!</li>
                            <li>التموضع بين الخطوط جيد جداً</li>
                            <li>يمكن تحسين الوعي الدفاعي عند الانتقال</li>
                            <li>حركة بدون كرة ذكية</li>
                        </ul>
                    </div>
                    <p>هل تريد أن أركز على جانب معين من لعبك؟</p>
                `;
            } else if (message.includes('تقرير') || message.includes('report') || message.includes('تحليل')) {
                response = `
                    <p>بناءً على تحليل الفيديو، إليك ملخص سريع:</p>
                    <div class="quick-analysis">
                        <p><strong>نقاط القوة:</strong></p>
                        <ul>
                            <li>تمرير دقيق ومتنوع</li>
                            <li>رؤية جيدة للملعب</li>
                            <li>تحكم ممتاز في الكرة</li>
                        </ul>
                        <p><strong>مجالات التحسين:</strong></p>
                        <ul>
                            <li>زيادة السرعة في اتخاذ القرارات</li>
                            <li>تحسين التموضع الدفاعي</li>
                            <li>العمل على القوة البدنية</li>
                        </ul>
                    </div>
                    <p>هل تريد تفاصيل أكثر حول أي نقطة معينة؟</p>
                `;
            } else if (message.includes('تكتيك') || message.includes('tactical') || message.includes('strategy')) {
                response = `
                    <p>في كرة القدم، الاستحواذ ليس مجرد امتلاك الكرة. إنه التموضع وخلق المساحات والتحكم في إيقاع اللعبة.</p>
                    <p>عندما نملك الكرة، نحتاج جميع اللاعبين للمشاركة في البناء، وخلق المثلثات وتوفير خيارات التمرير.</p>
                    <p>هكذا نهيمن على المباريات ونخلق الفرص. هل تريد تحليلاً تكتيكياً مفصلاً لأدائك؟</p>
                `;
            } else if (message.includes('لاعب') || message.includes('player') || message.includes('موهبة')) {
                response = `
                    <p>عندما أقيم اللاعبين، أنظر إلى ما هو أبعد من المهارات التقنية.</p>
                    <p>أبحث عن الذكاء واتخاذ القرارات والقدرة على فهم اللعبة.</p>
                    <p>اللاعب الذي يستطيع قراءة اللعبة جيداً واتخاذ قرارات سريعة لا يُقدر بثمن لبنية الفريق وتدفقه.</p>
                `;
            } else if (message.includes('تدريب') || message.includes('training') || message.includes('practice')) {
                response = `
                    <p>جلسات التدريب يجب أن تكون مكثفة ومركزة.</p>
                    <p>نعمل على اللعب الموضعي والتمرير السريع والحركة بدون كرة.</p>
                    <p>كل تمرين له غرض مرتبط بنموذج لعبنا. ملعب التدريب هو المكان الذي نبني فيه هويتنا ونتحضر لسيناريوهات مختلفة للمباراة.</p>
                `;
            } else {
                response = `
                    <p>كرة القدم لعبة معقدة من المساحة والزمن. يجب أن نفهم كيفية استخدام كليهما بفعالية.</p>
                    <p>عندما نلعب بشجاعة وإيمان بأفكارنا، يمكننا التغلب على أي خصم.</p>
                    <p>المفتاح هو البقاء مخلصين لمبادئنا مع التكيف مع التحديات المختلفة.</p>
                `;
            }
            
            addMessage({
                role: 'assistant',
                content: response
            });
        }, 1000);
    }



});