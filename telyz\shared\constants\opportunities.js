/**
 * Opportunities Constants
 * 
 * Defines constants related to opportunities in the Telyz platform.
 */

/**
 * Opportunity types
 */
const OPPORTUNITY_TYPES = {
  TRYOUT: 'tryout',
  CONTRACT: 'contract',
  SCHOLARSHIP: 'scholarship',
  TRAINING: 'training',
  COACHING: 'coaching',
  OTH<PERSON>: 'other',
};

/**
 * Opportunity status values
 */
const OPPORTUNITY_STATUS = {
  DRAFT: 'draft',
  PUBLISHED: 'published',
  CLOSED: 'closed',
  FILLED: 'filled',
  CANCELLED: 'cancelled',
};

/**
 * Opportunity visibility options
 */
const OPPORTUNITY_VISIBILITY = {
  PUBLIC: 'public',
  PRIVATE: 'private',
  INVITED: 'invited',
};

/**
 * Application process types
 */
const APPLICATION_PROCESS = {
  DIRECT: 'direct',
  EXTERNAL: 'external',
  EMAIL: 'email',
  TRYOUT: 'tryout',
};

/**
 * Application status values
 */
const APPLICATION_STATUS = {
  PENDING: 'pending',
  REVIEWING: 'reviewing',
  SHORTLISTED: 'shortlisted',
  REJECTED: 'rejected',
  ACCEPTED: 'accepted',
};

/**
 * Compensation types
 */
const COMPENSATION_TYPES = {
  PAID: 'paid',
  UNPAID: 'unpaid',
  SCHOLARSHIP: 'scholarship',
  VARIABLE: 'variable',
  OTHER: 'other',
};

/**
 * Compensation periods
 */
const COMPENSATION_PERIODS = {
  HOURLY: 'hourly',
  DAILY: 'daily',
  WEEKLY: 'weekly',
  MONTHLY: 'monthly',
  YEARLY: 'yearly',
  ONE_TIME: 'one-time',
  OTHER: 'other',
};

/**
 * Experience levels
 */
const EXPERIENCE_LEVELS = {
  BEGINNER: 'beginner',
  INTERMEDIATE: 'intermediate',
  ADVANCED: 'advanced',
  PROFESSIONAL: 'professional',
  ANY: 'any',
};

/**
 * Common required documents for applications
 */
const REQUIRED_DOCUMENTS = {
  RESUME: 'resume',
  COVER_LETTER: 'cover_letter',
  PORTFOLIO: 'portfolio',
  REFERENCES: 'references',
  CERTIFICATES: 'certificates',
  MEDICAL_CLEARANCE: 'medical_clearance',
  ID_PROOF: 'id_proof',
  PERFORMANCE_STATS: 'performance_stats',
  VIDEO_HIGHLIGHTS: 'video_highlights',
  AI_ANALYSIS: 'ai_analysis',
};

/**
 * Opportunity categories by sport
 */
const OPPORTUNITY_CATEGORIES = {
  FOOTBALL: {
    PROFESSIONAL_CLUB: 'professional_club',
    AMATEUR_CLUB: 'amateur_club',
    ACADEMY: 'academy',
    NATIONAL_TEAM: 'national_team',
    UNIVERSITY: 'university',
    TRAINING_CAMP: 'training_camp',
  },
  BASKETBALL: {
    PROFESSIONAL_TEAM: 'professional_team',
    COLLEGE: 'college',
    DEVELOPMENT_LEAGUE: 'development_league',
    INTERNATIONAL: 'international',
    SUMMER_LEAGUE: 'summer_league',
  },
  // Add categories for other sports as needed
};

/**
 * Opportunity benefits
 */
const OPPORTUNITY_BENEFITS = {
  ACCOMMODATION: 'accommodation',
  TRANSPORTATION: 'transportation',
  MEALS: 'meals',
  EQUIPMENT: 'equipment',
  MEDICAL_INSURANCE: 'medical_insurance',
  EDUCATION: 'education',
  TRAINING_FACILITIES: 'training_facilities',
  CAREER_DEVELOPMENT: 'career_development',
  EXPOSURE: 'exposure',
  NETWORKING: 'networking',
};

module.exports = {
  OPPORTUNITY_TYPES,
  OPPORTUNITY_STATUS,
  OPPORTUNITY_VISIBILITY,
  APPLICATION_PROCESS,
  APPLICATION_STATUS,
  COMPENSATION_TYPES,
  COMPENSATION_PERIODS,
  EXPERIENCE_LEVELS,
  REQUIRED_DOCUMENTS,
  OPPORTUNITY_CATEGORIES,
  OPPORTUNITY_BENEFITS,
};
