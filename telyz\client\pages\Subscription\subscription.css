/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: white;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    padding: 15px 0;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
    padding: 15px 20px;
}

.logo-section {
    display: flex;
    align-items: center;
}

.logo-link {
    display: flex;
    align-items: center;
    text-decoration: none;
    gap: 12px;
}

.logo-container {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, #6a0dad, #8e44ad);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    font-weight: 700;
    box-shadow: 0 4px 15px rgba(106, 13, 173, 0.3);
}

.logo-text {
    font-size: 24px;
    font-weight: 700;
    color: #333;
}

.nav-menu {
    display: flex;
    gap: 30px;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    color: #666;
    font-weight: 500;
    padding: 10px 16px;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
    color: #6a0dad;
    background: rgba(106, 13, 173, 0.1);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

.notification-btn {
    position: relative;
    background: none;
    border: none;
    font-size: 20px;
    color: #666;
    cursor: pointer;
    padding: 10px;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.notification-btn:hover {
    background: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
}

.notification-badge {
    position: absolute;
    top: 5px;
    right: 5px;
    background: #ff4757;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 11px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    padding: 8px 15px;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.user-menu:hover {
    background: rgba(106, 13, 173, 0.1);
}

.user-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #6a0dad;
}

.user-name {
    font-weight: 500;
    color: #333;
}

/* Main Content */
.main-content {
    padding: 40px 0;
}

/* Page Header */
.page-header {
    text-align: center;
    margin-bottom: 60px;
}

.page-title-section {
    margin-bottom: 40px;
}

.page-title {
    font-size: 3rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.page-title i {
    color: #6a0dad;
    font-size: 2.5rem;
}

.page-subtitle {
    font-size: 1.2rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

/* Category Tabs */
.category-tabs {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
    max-width: 1000px;
    margin: 0 auto;
}

.tab-btn {
    background: white;
    border: 2px solid #f0f0f0;
    border-radius: 12px;
    padding: 12px 18px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-family: inherit;
    font-weight: 500;
    color: #666;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    font-size: 14px;
}

.tab-btn:hover {
    border-color: #6a0dad;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(106, 13, 173, 0.2);
}

.tab-btn.active {
    background: linear-gradient(135deg, #6a0dad, #8e44ad);
    border-color: #6a0dad;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(106, 13, 173, 0.3);
}

.tab-btn i {
    font-size: 18px;
}

/* Plans Container */
.plans-container {
    max-width: 1200px;
    margin: 0 auto;
}

.plans-section {
    display: none;
}

.plans-section.active {
    display: block;
}

.section-header {
    text-align: center;
    margin-bottom: 50px;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 15px;
}

.section-subtitle {
    font-size: 1.1rem;
    color: #666;
    max-width: 500px;
    margin: 0 auto;
}

/* Plans Grid */
.plans-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 80px;
}

/* Plan Card */
.plan-card {
    background: white;
    border-radius: 20px;
    padding: 40px 30px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    border: 2px solid #f0f0f0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.plan-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    border-color: #6a0dad;
}

.plan-card.popular {
    border-color: #6a0dad;
    transform: scale(1.05);
}

.plan-card.popular:hover {
    transform: scale(1.05) translateY(-10px);
}

/* Free Plan Styles */
.free-plan {
    border-color: #28a745;
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.05), rgba(40, 167, 69, 0.1));
}

.free-plan:hover {
    border-color: #28a745;
    box-shadow: 0 20px 60px rgba(40, 167, 69, 0.2);
}

.free-plan .plan-icon {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.free-plan .currency {
    color: #28a745;
}

.free-btn {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3) !important;
}

.free-btn:hover {
    background: linear-gradient(135deg, #218838, #1e7e34) !important;
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4) !important;
}

.popular-badge {
    position: absolute;
    top: 20px;
    right: -30px;
    background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
    color: white;
    padding: 8px 40px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transform: rotate(45deg);
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

/* Plan Header */
.plan-header {
    text-align: center;
    margin-bottom: 30px;
}

.plan-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #6a0dad, #8e44ad);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    box-shadow: 0 8px 25px rgba(106, 13, 173, 0.3);
}

.plan-icon i {
    font-size: 35px;
    color: white;
}

.plan-name {
    font-size: 1.8rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 10px;
}

.plan-description {
    color: #666;
    font-size: 1rem;
}

/* Plan Price */
.plan-price {
    text-align: center;
    margin-bottom: 40px;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    gap: 5px;
}

.currency {
    font-size: 1.5rem;
    font-weight: 600;
    color: #6a0dad;
}

.amount {
    font-size: 4rem;
    font-weight: 800;
    color: #333;
    line-height: 1;
}

.period {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 8px;
}

/* Plan Features */
.plan-features {
    margin-bottom: 40px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
    padding: 12px 0;
}

.feature-item i {
    font-size: 16px;
    width: 20px;
    flex-shrink: 0;
}

.feature-item.disabled {
    opacity: 0.5;
}

.feature-item .fa-check {
    color: #28a745;
}

.feature-item .fa-times {
    color: #dc3545;
}

.feature-item span {
    font-size: 15px;
    color: #333;
}

/* Plan Button */
.plan-btn {
    width: 100%;
    padding: 15px 30px;
    background: linear-gradient(135deg, #6a0dad, #8e44ad);
    color: white;
    border: none;
    border-radius: 12px;
    font-family: inherit;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(106, 13, 173, 0.3);
}

.plan-btn:hover {
    background: linear-gradient(135deg, #5a0a8a, #7a3d98);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(106, 13, 173, 0.4);
}

.plan-btn:active {
    transform: translateY(0);
}

/* FAQ Section */
.faq-section {
    max-width: 800px;
    margin: 0 auto;
    padding-top: 60px;
    border-top: 1px solid #f0f0f0;
}

.faq-grid {
    display: grid;
    gap: 25px;
}

.faq-item {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid #f0f0f0;
}

.faq-question {
    display: flex;
    align-items: center;
    gap: 15px;
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
}

.faq-question i {
    color: #6a0dad;
    font-size: 1.1rem;
}

.faq-answer {
    color: #666;
    line-height: 1.6;
    font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .plans-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 25px;
    }
    
    .plan-card.popular {
        transform: none;
    }
    
    .plan-card.popular:hover {
        transform: translateY(-10px);
    }
}

@media (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: 20px;
        padding: 20px;
    }
    
    .nav-menu {
        gap: 15px;
    }
    
    .page-title {
        font-size: 2.5rem;
        flex-direction: column;
        gap: 10px;
    }
    
    .category-tabs {
        gap: 10px;
    }
    
    .tab-btn {
        padding: 10px 15px;
        font-size: 13px;
        gap: 6px;
    }
    
    .tab-btn span {
        display: none;
    }
    
    .tab-btn i {
        font-size: 16px;
    }
    
    .plans-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .plan-card {
        padding: 30px 25px;
    }
    
    .section-title {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }
    
    .main-content {
        padding: 30px 0;
    }
    
    .page-title {
        font-size: 2rem;
    }
    
    .page-subtitle {
        font-size: 1rem;
    }
    
    .category-tabs {
        flex-direction: column;
        align-items: center;
    }
    
    .tab-btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
    
    .plan-card {
        padding: 25px 20px;
    }
    
    .amount {
        font-size: 3rem;
    }
    
    .section-title {
        font-size: 1.8rem;
    }
    
    .faq-item {
        padding: 25px 20px;
    }
}

/* Loading and Animation Effects */
.plans-section {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Hover Effects */
.plan-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(106, 13, 173, 0.05), rgba(142, 68, 173, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.plan-card:hover::before {
    opacity: 1;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #6a0dad, #8e44ad);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a0a8a, #7a3d98);
}