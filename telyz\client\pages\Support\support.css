/* Base styles from template */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: '<PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: #f0f2f5;
    color: #333;
}

.container {
    display: flex;
    min-height: 100vh;
    max-width: 1050px;
    margin: 0 auto;
    background-color: #f0f2f5;
}

/* ================ SIDEBAR STYLES ================ */
.sidebar-left {
    background-color: #f0f2f5;
    padding: 15px 0;
    position: sticky;
    top: 0;
    height: 100vh;
    width: 270px;
    overflow-y: auto;
}

.logo {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

.logo-link {
    text-decoration: none;
    cursor: pointer;
    transition: transform 0.2s ease;
    display: block;
}

.logo-link:hover {
    transform: scale(1.05);
}

.logo-container {
    width: 40px;
    height: 40px;
    background-color: #6a0dad;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(106, 13, 173, 0.3);
}

.sidebar-menu {
    padding: 0 10px;
    max-height: calc(100vh - 100px);
    width: 100%;
}

.sidebar-menu ul {
    list-style: none;
}

.sidebar-menu > ul > li {
    margin: 4px 0;
    padding: 8px 5px;
    border-radius: 10px;
    transition: all 0.3s;
}

.sidebar-menu li:hover {
    background-color: rgba(106, 13, 173, 0.05);
}

.has-submenu {
    position: relative;
}

.submenu {
    display: none;
    list-style: none;
    padding-left: 0;
    margin-top: 5px;
    margin-bottom: 5px;
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 8px;
    overflow: visible;
    max-height: 0;
    transition: max-height 0.3s ease-in-out;
    width: 100%;
    min-width: 260px;
}

.has-submenu:hover .submenu {
    display: block;
    max-height: 400px;
}

.submenu li {
    margin: 0;
    padding: 8px 12px 8px 5px;
    min-width: 240px;
    position: relative;
}

.submenu a {
    font-size: 14px;
    padding: 4px 0;
    white-space: normal;
    overflow: visible;
    text-overflow: clip;
    display: flex;
    align-items: center;
    width: 100%;
    line-height: 1.4;
}

.submenu i {
    font-size: 16px;
    margin-right: 10px;
    width: 20px;
    text-align: center;
    display: inline-flex;
    justify-content: center;
    align-items: center;
}

.ai-submenu {
    overflow: visible !important;
    width: 260px;
    padding: 8px 0;
}

.ai-submenu li {
    overflow: visible;
    padding: 12px 15px 12px 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.03);
}

.ai-submenu li:last-child {
    border-bottom: none;
}

.ai-submenu a {
    align-items: flex-start;
}

.ai-submenu a span {
    padding-top: 2px;
    font-weight: 500;
}

.ai-submenu i {
    font-size: 18px;
    margin-right: 12px;
}

/* Colores específicos para cada opción en el menú de AI Analysis */
.ai-submenu li:nth-child(1) i { color: #FF5722; }
.ai-submenu li:nth-child(2) i { color: #4CAF50; }
.ai-submenu li:nth-child(3) i { color: #2196F3; }
.ai-submenu li:nth-child(4) i { color: #FFC107; }
.ai-submenu li:nth-child(5) i { color: #9C27B0; }

/* Colores específicos para cada deporte */
.submenu li:nth-child(1) i { color: #8e44ad; }
.submenu li:nth-child(2) i { color: #9b59b6; }
.submenu li:nth-child(3) i { color: #6a0dad; }
.submenu li:nth-child(4) i { color: #5d3fd3; }
.submenu li:nth-child(5) i { color: #7d3c98; }
.submenu li:nth-child(6) i { color: #a569bd; }

.sidebar-menu a {
    text-decoration: none;
    color: #4a0080;
    display: flex;
    align-items: center;
    font-size: 16px;
    padding: 6px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 500;
    transition: all 0.3s;
    position: relative;
}

.sidebar-menu a:hover {
    background-color: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
    transform: translateX(2px);
}

.sidebar-menu i {
    margin-right: 15px;
    font-size: 20px;
    width: 25px;
    text-align: center;
    color: #6a0dad;
}

.badge {
    background-color: #ff4757;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 8px;
    font-weight: 600;
}

.new-badge {
    background: linear-gradient(45deg, #ff6b6b, #ff4757);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* User profile sidebar */
.user-profile-sidebar {
    display: flex;
    align-items: center;
    margin-top: 23px;
    padding: 12px 18px;
    border-radius: 10px;
    background-color: rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
}

.user-profile-sidebar:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.profile-pic-small {
    width: 46px;
    height: 46px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 12px;
    border: 2px solid #6a0dad;
}

.profile-pic-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-info-sidebar {
    flex: 1;
}

.profile-info-sidebar span:first-child {
    display: block;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.profile-info-sidebar .teams-clubs {
    font-size: 12px;
    color: #666;
    margin-top: 2px;
}

.profile-dropdown-toggle {
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color 0.2s;
    color: #666;
}

.profile-dropdown-toggle:hover {
    background-color: rgba(0, 0, 0, 0.1);
    color: #333;
}

.profile-dropdown-toggle i {
    font-size: 20px;
    color: #444;
}

.profile-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.2);
    width: 220px;
    z-index: 1000;
    overflow: hidden;
    display: none;
    border: 1px solid #eee;
    margin-top: 6px;
    max-height: 0;
    opacity: 0;
    transition: max-height 0.3s ease, opacity 0.3s ease;
    visibility: hidden;
    text-align: left;
}

.profile-dropdown-menu.show {
    display: block;
    max-height: 400px;
    opacity: 1;
    overflow-y: auto;
    z-index: 1001;
    visibility: visible;
}

.profile-dropdown-item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 14px 25px;
    text-decoration: none;
    color: #333;
    transition: all 0.2s ease;
    font-size: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.03);
    position: relative;
}

.profile-dropdown-item:last-child {
    border-bottom: none;
}

.profile-dropdown-item:hover {
    background-color: #f5f5f5;
    color: #6a0dad;
}

.profile-dropdown-item i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
    color: #6a0dad;
    font-size: 17px;
    display: inline-block;
}

.dropdown-divider {
    height: 1px;
    background-color: #e0e0e0;
    margin: 8px 0;
}

.logout-item {
    color: #e74c3c;
}

.logout-item:hover {
    background-color: rgba(231, 76, 60, 0.05);
    color: #e74c3c;
}

.js-tooltip {
    position: fixed;
    width: 220px;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px;
    border-radius: 6px;
    font-size: 12px;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
    z-index: 1001;
    line-height: 1.4;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    pointer-events: none;
    text-align: left;
}

.js-tooltip::before {
    content: "";
    position: absolute;
    top: 50%;
    left: -10px;
    transform: translateY(-50%);
    border-width: 5px;
    border-style: solid;
    border-color: transparent var(--tooltip-color, rgba(0, 0, 0, 0.8)) transparent transparent;
}

.js-tooltip.visible {
    opacity: 1;
    visibility: visible;
}

/* ================ MAIN CONTENT ================ */
.main-content {
    flex: 1;
    padding: 20px;
    background-color: white;
    border-radius: 15px;
    margin: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
    max-height: calc(100vh - 40px);
}

/* ================ SUPPORT HEADER ================ */
.support-header {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #f0f2f5;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-title h1 {
    color: #6a0dad;
    font-size: 28px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-title h1 i {
    color: #6a0dad;
    margin-right: 10px;
}

.header-title p {
    color: #666;
    font-size: 16px;
    margin: 0;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

.contact-support-btn {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.contact-support-btn:hover {
    background: linear-gradient(135deg, #229954, #27ae60);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
}

.support-status {
    background: #f8f9fa;
    padding: 8px 16px;
    border-radius: 20px;
    border: 1px solid #e9ecef;
    font-size: 14px;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-online {
    color: #27ae60;
    font-size: 10px;
    animation: pulse-green 2s infinite;
}

@keyframes pulse-green {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* ================ SUPPORT FILTERS ================ */
.support-filters {
    background: linear-gradient(135deg, #6a0dad, #8b4bcf);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 25px;
    border: 1px solid #6a0dad;
    box-shadow: 0 4px 15px rgba(106, 13, 173, 0.2);
}

.filters-header h3 {
    color: white;
    font-size: 18px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.filters-header h3 i {
    color: white;
}

.filter-tabs {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.filter-tab {
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 10px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    backdrop-filter: blur(10px);
}

.filter-tab:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.filter-tab.active {
    background: white;
    color: #6a0dad;
    border-color: white;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    font-weight: 600;
}

.filter-tab.active i {
    color: #6a0dad;
}

/* ================ QUICK ACTIONS SECTION ================ */
.quick-actions-section {
    margin-bottom: 40px;
}

.quick-actions-section h3 {
    color: #2c3e50;
    font-size: 20px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.quick-action-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.quick-action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.action-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    font-size: 24px;
    color: white;
}

.quick-action-card:nth-child(1) .action-icon {
    background: linear-gradient(135deg, #3498db, #2980b9);
}

.quick-action-card:nth-child(2) .action-icon {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.quick-action-card:nth-child(3) .action-icon {
    background: linear-gradient(135deg, #f39c12, #e67e22);
}

.quick-action-card:nth-child(4) .action-icon {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

.quick-action-card h4 {
    color: #2c3e50;
    font-size: 18px;
    margin-bottom: 10px;
    font-weight: 600;
}

.quick-action-card p {
    color: #7f8c8d;
    font-size: 14px;
    margin-bottom: 20px;
    line-height: 1.5;
}

.action-btn {
    background: linear-gradient(135deg, #6a0dad, #8b4bcf);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.action-btn:hover {
    background: linear-gradient(135deg, #5a0b8a, #7a40b8);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(106, 13, 173, 0.3);
}

/* ================ FAQ SECTION ================ */
.faq-section {
    margin-bottom: 40px;
}

.faq-section h3 {
    color: #2c3e50;
    font-size: 20px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.faq-container {
    background: #f8f9fa;
    border-radius: 10px;
    border: 1px solid #e9ecef;
    overflow: hidden;
}

.faq-item {
    border-bottom: 1px solid #e9ecef;
}

.faq-item:last-child {
    border-bottom: none;
}

.faq-question {
    background: white;
    padding: 20px 25px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.faq-question:hover {
    background: #f8f9fa;
    border-left: 4px solid #6a0dad;
}

.faq-question h4 {
    color: #2c3e50;
    font-size: 16px;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.faq-question h4 i {
    color: #6a0dad;
    font-size: 14px;
}

.toggle-icon {
    color: #7f8c8d;
    transition: transform 0.3s ease;
}

.faq-item.active .toggle-icon {
    transform: rotate(180deg);
}

.faq-answer {
    padding: 0 25px;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.faq-item.active .faq-answer {
    max-height: 500px;
    padding: 20px 25px;
}

.faq-answer p {
    color: #5d6d7e;
    line-height: 1.6;
    margin-bottom: 15px;
}

.faq-answer ul, .faq-answer ol {
    color: #5d6d7e;
    margin-left: 20px;
    margin-bottom: 15px;
}

.faq-answer li {
    margin-bottom: 8px;
    line-height: 1.5;
}

.faq-answer strong {
    color: #2c3e50;
}

.faq-actions {
    margin-top: 15px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn-primary {
    background: linear-gradient(135deg, #6a0dad, #8b4bcf);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    font-size: 13px;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a0b8a, #7a40b8);
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(106, 13, 173, 0.3);
}

.btn-secondary {
    background: #ecf0f1;
    color: #2c3e50;
    border: 1px solid #bdc3c7;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    font-size: 13px;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: #d5dbdb;
    border-color: #95a5a6;
}

/* ================ CONTACT SECTION ================ */
.contact-section {
    margin-bottom: 40px;
}

.contact-section h3 {
    color: #2c3e50;
    font-size: 20px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.contact-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

.contact-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.contact-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.contact-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    font-size: 24px;
    color: white;
}

.contact-card:nth-child(1) .contact-icon {
    background: linear-gradient(135deg, #3498db, #2980b9);
}

.contact-card:nth-child(2) .contact-icon {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
}

.contact-card:nth-child(3) .contact-icon {
    background: linear-gradient(135deg, #f39c12, #e67e22);
}

.contact-card h4 {
    color: #2c3e50;
    font-size: 18px;
    margin-bottom: 10px;
    font-weight: 600;
}

.contact-card p {
    color: #7f8c8d;
    font-size: 14px;
    margin-bottom: 10px;
    line-height: 1.5;
}

.contact-info {
    color: #6a0dad !important;
    font-weight: 600 !important;
    font-size: 16px !important;
    margin-bottom: 20px !important;
}

.contact-btn {
    background: linear-gradient(135deg, #6a0dad, #8b4bcf);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    width: 100%;
}

.contact-btn:hover {
    background: linear-gradient(135deg, #5a0b8a, #7a40b8);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(106, 13, 173, 0.3);
}

/* ================ RESPONSIVE DESIGN ================ */
@media (max-width: 768px) {
    .container {
        flex-direction: column;
        max-width: 100%;
    }
    
    .sidebar-left {
        width: 100%;
        height: auto;
        position: relative;
    }
    
    .main-content {
        margin: 10px;
        border-radius: 10px;
    }
    
    .header-content {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .header-actions {
        flex-direction: column;
        width: 100%;
    }
    
    .filter-tabs {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .quick-actions-grid {
        grid-template-columns: 1fr;
    }
    
    .contact-options {
        grid-template-columns: 1fr;
    }
    
    .faq-question h4 {
        font-size: 14px;
    }
    
    .faq-answer {
        padding: 0 20px;
    }
    
    .faq-item.active .faq-answer {
        padding: 15px 20px;
    }
}

@media (max-width: 480px) {
    .main-content {
        padding: 15px;
    }
    
    .header-title h1 {
        font-size: 22px;
    }
    
    .filter-tabs {
        flex-direction: column;
    }
    
    .filter-tab {
        width: 100%;
        justify-content: center;
    }
}