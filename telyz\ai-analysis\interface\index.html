<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sports Analysis AI System | Telyz</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="ai-container">
        <!-- <PERSON>a lateral con logo para volver -->
        <div class="ai-sidebar">
            <a href="../../client/pages/Home/index.html" class="back-button">
                <div class="logo-container">T</div>
            </a>
        </div>

        <!-- Contenido principal del sistema de análisis -->
        <div class="ai-main-content">
            <!-- Pantalla de inicio -->
            <div class="ai-welcome-screen" id="welcomeScreen">
                <div class="ai-header">
                    <h1>AI Sports <span>Analysis</span></h1>
                    <p class="ai-subtitle">Advanced player performance analysis using artificial intelligence and computer vision technology</p>
                </div>

                <div class="ai-illustration">
                    <img src="ai_illustration.svg" alt="AI Analysis Illustration" id="aiIllustration">
                </div>

                <div class="ai-actions">
                    <button class="ai-button learn-more">Learn More</button>
                    <button class="ai-button upload-video" id="uploadVideoBtn">Upload Video</button>
                </div>
            </div>

            <!-- Pantalla de carga de video -->
            <div class="ai-upload-screen" id="uploadScreen" style="display: none;">
                <div class="ai-upload-area" id="dropArea">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <p>Drag and drop video file here</p>
                    <p class="ai-upload-subtitle">or click to select from your device</p>
                    <input type="file" id="videoFileInput" accept="video/*" style="display: none;">
                </div>

                <div class="ai-analysis-options">
                    <div class="ai-option-row">
                        <div class="ai-option">
                            <label>Sport</label>
                            <select id="sportSelect">
                                <option value="football" selected>Football</option>
                                <option value="basketball">Basketball</option>
                                <option value="volleyball">Volleyball</option>
                                <option value="baseball">Baseball</option>
                                <option value="cricket">Cricket</option>
                                <option value="hockey">Field Hockey</option>
                            </select>
                        </div>

                        <div class="ai-option">
                            <label>Position</label>
                            <select id="positionSelect">
                                <option value="striker" selected>Striker</option>
                                <option value="midfielder">Midfielder</option>
                                <option value="defender">Defender</option>
                                <option value="goalkeeper">Goalkeeper</option>
                            </select>
                        </div>

                        <div class="ai-option">
                            <label>Player Selection Method</label>
                            <select id="selectionMethodSelect">
                                <option value="manual" selected>Manual</option>
                                <option value="automatic">Automatic</option>
                            </select>
                        </div>

                        <div class="ai-option">
                            <label>Analysis Accuracy</label>
                            <select id="accuracySelect">
                                <option value="standard" selected>Select Accuracy</option>
                                <option value="high">High (Slower)</option>
                                <option value="medium">Medium</option>
                                <option value="low">Low (Faster)</option>
                            </select>
                        </div>
                    </div>

                    <div class="ai-option-row">
                        <div class="ai-playback-speed">
                            <label>Video Playback Speed</label>
                            <div class="ai-speed-options">
                                <button class="ai-speed-button" data-speed="1">1x</button>
                                <button class="ai-speed-button active" data-speed="2">2x</button>
                                <button class="ai-speed-button" data-speed="3">3x</button>
                            </div>
                        </div>
                    </div>

                    <div class="ai-option-row ai-action-buttons">
                        <button class="ai-button start-analysis" id="startAnalysisBtn">Start Analysis</button>
                        <button class="ai-button reset">Reset</button>
                        <button class="ai-button advanced-options">Advanced Options</button>
                    </div>
                </div>
            </div>

            <!-- Pantalla de resultados (inicialmente oculta) -->
            <div class="ai-results-screen" id="resultsScreen" style="display: none;">
                <div class="ai-results-container">
                    <div class="ai-result-card">
                        <h2>Performance Report</h2>
                        <p class="ai-result-description">The video has been successfully analyzed and a detailed player performance report has been created.</p>

                        <div class="ai-metrics">
                            <div class="ai-metric">
                                <span class="ai-metric-value">87%</span>
                                <span class="ai-metric-label">Pass Accuracy</span>
                            </div>
                            <div class="ai-metric">
                                <span class="ai-metric-value">12.4</span>
                                <span class="ai-metric-label">Km Distance</span>
                            </div>
                            <div class="ai-metric">
                                <span class="ai-metric-value">8</span>
                                <span class="ai-metric-label">Key Actions</span>
                            </div>
                        </div>

                        <div class="ai-result-actions">
                            <button class="ai-button view-report">View Report</button>
                            <button class="ai-button download-json">Download JSON</button>
                            <button class="ai-button share">Share</button>
                        </div>

                        <div class="ai-share-options">
                            <span>Share via:</span>
                            <div class="ai-share-buttons">
                                <button class="ai-share-button"><i class="fab fa-twitter"></i></button>
                                <button class="ai-share-button"><i class="fab fa-facebook"></i></button>
                                <button class="ai-share-button"><i class="fab fa-whatsapp"></i></button>
                                <button class="ai-share-button"><i class="fas fa-envelope"></i></button>
                            </div>
                        </div>
                    </div>

                    <div class="ai-result-card">
                        <h2>Analyzed Video</h2>
                        <p class="ai-result-description">You can download the analyzed video with annotations and statistics.</p>

                        <div class="ai-video-preview">
                            <div class="ai-video-placeholder">
                                <i class="fas fa-play-circle"></i>
                                <p>Preview available after download</p>
                            </div>
                        </div>

                        <button class="ai-button download-video">Download Video</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
