/**
 * Opportunity Routes
 * 
 * API routes for opportunity management.
 */

const express = require('express');
const router = express.Router();
const opportunityController = require('../controllers/opportunityController');
const authMiddleware = require('../middlewares/authMiddleware');
const { validateOpportunity } = require('../validators/opportunityValidator');

/**
 * @route   GET /api/opportunities
 * @desc    Get all opportunities
 * @access  Public
 */
router.get('/', opportunityController.getAllOpportunities);

/**
 * @route   GET /api/opportunities/:id
 * @desc    Get opportunity by ID
 * @access  Public
 */
router.get('/:id', opportunityController.getOpportunityById);

/**
 * @route   POST /api/opportunities
 * @desc    Create a new opportunity
 * @access  Private
 */
router.post(
  '/',
  authMiddleware.authenticate,
  validateOpportunity,
  opportunityController.createOpportunity
);

/**
 * @route   PUT /api/opportunities/:id
 * @desc    Update opportunity
 * @access  Private
 */
router.put(
  '/:id',
  authMiddleware.authenticate,
  validateOpportunity,
  opportunityController.updateOpportunity
);

/**
 * @route   DELETE /api/opportunities/:id
 * @desc    Delete opportunity
 * @access  Private
 */
router.delete(
  '/:id',
  authMiddleware.authenticate,
  opportunityController.deleteOpportunity
);

/**
 * @route   POST /api/opportunities/:id/apply
 * @desc    Apply for an opportunity
 * @access  Private
 */
router.post(
  '/:id/apply',
  authMiddleware.authenticate,
  opportunityController.applyForOpportunity
);

/**
 * @route   PUT /api/opportunities/:id/applications/:userId
 * @desc    Update application status
 * @access  Private
 */
router.put(
  '/:id/applications/:userId',
  authMiddleware.authenticate,
  opportunityController.updateApplicationStatus
);

/**
 * @route   GET /api/opportunities/search
 * @desc    Search opportunities
 * @access  Public
 */
router.get('/search', opportunityController.searchOpportunities);

/**
 * @route   GET /api/opportunities/featured
 * @desc    Get featured opportunities
 * @access  Public
 */
router.get('/featured', opportunityController.getFeaturedOpportunities);

/**
 * @route   GET /api/opportunities/recommended
 * @desc    Get recommended opportunities for user
 * @access  Private
 */
router.get(
  '/recommended',
  authMiddleware.authenticate,
  opportunityController.getRecommendedOpportunities
);

/**
 * @route   POST /api/opportunities/:id/share
 * @desc    Share an opportunity
 * @access  Private
 */
router.post(
  '/:id/share',
  authMiddleware.authenticate,
  opportunityController.shareOpportunity
);

/**
 * @route   POST /api/opportunities/:id/save
 * @desc    Save an opportunity
 * @access  Private
 */
router.post(
  '/:id/save',
  authMiddleware.authenticate,
  opportunityController.saveOpportunity
);

/**
 * @route   DELETE /api/opportunities/:id/save
 * @desc    Unsave an opportunity
 * @access  Private
 */
router.delete(
  '/:id/save',
  authMiddleware.authenticate,
  opportunityController.unsaveOpportunity
);

module.exports = router;
