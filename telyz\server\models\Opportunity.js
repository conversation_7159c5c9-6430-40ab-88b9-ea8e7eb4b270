/**
 * Opportunity Model
 * 
 * Defines the schema for opportunities in the Telyz platform.
 */

const mongoose = require('mongoose');

const OpportunitySchema = new mongoose.Schema({
  // Creator information
  creator: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  organization: {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    logo: {
      type: String,
    },
    website: {
      type: String,
      trim: true,
    },
    verified: {
      type: Boolean,
      default: false,
    },
  },
  
  // Opportunity details
  title: {
    type: String,
    required: true,
    trim: true,
  },
  description: {
    type: String,
    required: true,
    trim: true,
  },
  type: {
    type: String,
    enum: ['tryout', 'contract', 'scholarship', 'training', 'coaching', 'other'],
    required: true,
  },
  sport: {
    type: String,
    enum: ['football', 'basketball', 'volleyball', 'baseball', 'cricket', 'hockey'],
    required: true,
  },
  positions: [{
    type: String,
    required: true,
    trim: true,
  }],
  
  // Location information
  location: {
    city: {
      type: String,
      required: true,
      trim: true,
    },
    country: {
      type: String,
      required: true,
      trim: true,
    },
    remote: {
      type: <PERSON>olean,
      default: false,
    },
    address: {
      type: String,
      trim: true,
    },
    coordinates: {
      latitude: {
        type: Number,
      },
      longitude: {
        type: Number,
      },
    },
  },
  
  // Timing information
  dates: {
    published: {
      type: Date,
      default: Date.now,
    },
    applicationDeadline: {
      type: Date,
      required: true,
    },
    startDate: {
      type: Date,
      required: true,
    },
    endDate: {
      type: Date,
    },
  },
  
  // Requirements
  requirements: {
    ageRange: {
      min: {
        type: Number,
      },
      max: {
        type: Number,
      },
    },
    experience: {
      type: String,
      enum: ['beginner', 'intermediate', 'advanced', 'professional', 'any'],
      default: 'any',
    },
    skills: [{
      type: String,
      trim: true,
    }],
    education: {
      type: String,
      trim: true,
    },
    additionalRequirements: [{
      type: String,
      trim: true,
    }],
  },
  
  // Compensation and benefits
  compensation: {
    type: {
      type: String,
      enum: ['paid', 'unpaid', 'scholarship', 'variable', 'other'],
      required: true,
    },
    amount: {
      type: String,
      trim: true,
    },
    currency: {
      type: String,
      trim: true,
    },
    period: {
      type: String,
      enum: ['hourly', 'daily', 'weekly', 'monthly', 'yearly', 'one-time', 'other'],
    },
    benefits: [{
      type: String,
      trim: true,
    }],
  },
  
  // Application process
  application: {
    process: {
      type: String,
      enum: ['direct', 'external', 'email', 'tryout'],
      required: true,
    },
    url: {
      type: String,
      trim: true,
    },
    email: {
      type: String,
      trim: true,
    },
    instructions: {
      type: String,
      trim: true,
    },
    requiredDocuments: [{
      type: String,
      trim: true,
    }],
  },
  
  // Status and visibility
  status: {
    type: String,
    enum: ['draft', 'published', 'closed', 'filled', 'cancelled'],
    default: 'published',
  },
  visibility: {
    type: String,
    enum: ['public', 'private', 'invited'],
    default: 'public',
  },
  featured: {
    type: Boolean,
    default: false,
  },
  
  // Statistics
  stats: {
    views: {
      type: Number,
      default: 0,
    },
    applications: {
      type: Number,
      default: 0,
    },
    shares: {
      type: Number,
      default: 0,
    },
    saves: {
      type: Number,
      default: 0,
    },
  },
  
  // Applicants
  applicants: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    status: {
      type: String,
      enum: ['pending', 'reviewing', 'shortlisted', 'rejected', 'accepted'],
      default: 'pending',
    },
    appliedAt: {
      type: Date,
      default: Date.now,
    },
    notes: {
      type: String,
      trim: true,
    },
  }],
  
  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

// Pre-save middleware to update timestamps
OpportunitySchema.pre('save', function(next) {
  // Update updatedAt
  this.updatedAt = Date.now();
  
  // Update stats.applications
  if (this.isModified('applicants')) {
    this.stats.applications = this.applicants.length;
  }
  
  next();
});

// Method to check if opportunity is active
OpportunitySchema.methods.isActive = function() {
  return this.status === 'published' && this.dates.applicationDeadline > Date.now();
};

// Method to check if user has applied
OpportunitySchema.methods.hasUserApplied = function(userId) {
  return this.applicants.some(applicant => applicant.user.toString() === userId.toString());
};

module.exports = mongoose.model('Opportunity', OpportunitySchema);
