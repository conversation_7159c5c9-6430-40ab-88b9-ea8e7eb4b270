{"name": "telyz", "version": "1.0.0", "description": "Telyz - Professional Sports Network Platform", "main": "server/app.js", "scripts": {"start": "node server/app.js", "dev": "nodemon server/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "repository": {"type": "git", "url": "git+https://github.com/yourusername/telyz.git"}, "keywords": ["sports", "platform", "athletes", "ai", "analysis"], "author": "Your Name", "license": "MIT", "bugs": {"url": "https://github.com/yourusername/telyz/issues"}, "homepage": "https://github.com/yourusername/telyz#readme", "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-rate-limit": "^6.7.0", "express-validator": "^7.0.1", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.0", "mongoose": "^7.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.1", "uuid": "^9.0.0"}, "devDependencies": {"eslint": "^8.38.0", "jest": "^29.5.0", "nodemon": "^2.0.22", "supertest": "^6.3.3"}}