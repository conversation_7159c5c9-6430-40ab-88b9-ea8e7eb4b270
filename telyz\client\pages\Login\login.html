<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Telyz | Professional Sports Network</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="login_styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body class="login-page">
    <div class="login-container">
        <!-- Logo and branding section -->
        <div class="login-branding">
            <div class="login-logo">
                <div class="logo-container">T</div>
            </div>
            <h1 class="login-title">Telyz</h1>
            <p class="login-tagline">The Professional Sports Network</p>
        </div>

        <!-- Login form section -->
        <div class="login-form-container">
            <div class="login-tabs">
                <button class="login-tab-btn active" data-tab="login">Login</button>
                <button class="login-tab-btn" data-tab="register">Register</button>
            </div>

            <!-- Login Form -->
            <div class="login-form-section active" id="login-section">
                <h2>Welcome Back</h2>
                <p class="login-subtitle">Sign in to access your professional sports network</p>

                <form class="login-form" id="login-form">
                    <div class="form-group">
                        <label for="login-email">Email</label>
                        <div class="input-with-icon">
                            <i class="fas fa-envelope"></i>
                            <input type="email" id="login-email" name="email" placeholder="Enter your email" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="login-password">Password</label>
                        <div class="input-with-icon">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="login-password" name="password" placeholder="Enter your password" required>
                            <i class="fas fa-eye toggle-password"></i>
                        </div>
                    </div>

                    <div class="form-options">
                        <div class="remember-me">
                            <input type="checkbox" id="remember-me" name="remember">
                            <label for="remember-me">Remember me</label>
                        </div>
                        <a href="#" class="forgot-password">Forgot password?</a>
                    </div>

                    <button type="submit" class="login-button">Sign In</button>
                </form>

                <div class="social-login">
                    <p>Or sign in with</p>
                    <div class="social-buttons">
                        <button class="social-btn google">
                            <i class="fab fa-google"></i>
                            <span>Google</span>
                        </button>
                        <button class="social-btn facebook">
                            <i class="fab fa-facebook-f"></i>
                            <span>Facebook</span>
                        </button>
                        <button class="social-btn apple">
                            <i class="fab fa-apple"></i>
                            <span>Apple</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Registration Form -->
            <div class="login-form-section" id="register-section">
                <h2>Join Telyz</h2>
                <p class="login-subtitle">Create your professional sports profile</p>

                <div class="registration-steps">
                    <div class="step-indicator">
                        <div class="step active" data-step="1">
                            <div class="step-number">1</div>
                            <div class="step-label">Role</div>
                        </div>
                        <div class="step" data-step="2">
                            <div class="step-number">2</div>
                            <div class="step-label">Level</div>
                        </div>
                        <div class="step" data-step="3">
                            <div class="step-number">3</div>
                            <div class="step-label">Details</div>
                        </div>
                    </div>

                    <!-- Step 1: User Role Selection -->
                    <div class="registration-step active" id="step-1">
                        <h3 class="step-title">Select your role in sports</h3>
                        <div class="user-type-selection">
                            <div class="user-type-options">
                                <div class="user-type-option">
                                    <input type="radio" id="user-athlete" name="user-type" value="athlete" checked>
                                    <label for="user-athlete">
                                        <i class="fas fa-running"></i>
                                        <span>Athlete</span>
                                    </label>
                                </div>
                                <div class="user-type-option">
                                    <input type="radio" id="user-coach" name="user-type" value="coach">
                                    <label for="user-coach">
                                        <i class="fas fa-clipboard"></i>
                                        <span>Coach</span>
                                    </label>
                                </div>
                                <div class="user-type-option">
                                    <input type="radio" id="user-scout" name="user-type" value="scout">
                                    <label for="user-scout">
                                        <i class="fas fa-binoculars"></i>
                                        <span>Scout</span>
                                    </label>
                                </div>
                                <div class="user-type-option">
                                    <input type="radio" id="user-club" name="user-type" value="club">
                                    <label for="user-club">
                                        <i class="fas fa-shield-alt"></i>
                                        <span>Club</span>
                                    </label>
                                </div>
                                <div class="user-type-option">
                                    <input type="radio" id="user-other" name="user-type" value="other">
                                    <label for="user-other">
                                        <i class="fas fa-user"></i>
                                        <span>Other</span>
                                    </label>
                                </div>
                                <div class="other-role-input" style="display: none;">
                                    <input type="text" id="other-role" name="other-role" placeholder="Specify your sports role">
                                </div>
                            </div>
                        </div>
                        <div class="step-navigation">
                            <button type="button" class="next-step-btn" data-next="2">Continue</button>
                        </div>
                    </div>

                    <!-- Step 2: Professional Level Selection -->
                    <div class="registration-step" id="step-2">
                        <h3 class="step-title">Select your professional level</h3>
                        <div class="level-selection">
                            <div class="level-options">
                                <div class="level-option">
                                    <input type="radio" id="level-professional" name="level-type" value="professional" checked>
                                    <label for="level-professional">
                                        <i class="fas fa-award"></i>
                                        <span>Professional</span>
                                        <p class="level-description">Active professional athlete with official contracts</p>
                                    </label>
                                </div>
                                <div class="level-option">
                                    <input type="radio" id="level-semi-pro" name="level-type" value="semi-pro">
                                    <label for="level-semi-pro">
                                        <i class="fas fa-star-half-alt"></i>
                                        <span>Semi-Professional</span>
                                        <p class="level-description">Part-time professional or academy player</p>
                                    </label>
                                </div>
                                <div class="level-option">
                                    <input type="radio" id="level-youth" name="level-type" value="youth">
                                    <label for="level-youth">
                                        <i class="fas fa-child"></i>
                                        <span>Youth/Academy</span>
                                        <p class="level-description">Youth player in development program</p>
                                    </label>
                                </div>
                                <div class="level-option">
                                    <input type="radio" id="level-amateur" name="level-type" value="amateur">
                                    <label for="level-amateur">
                                        <i class="fas fa-heart"></i>
                                        <span>Amateur</span>
                                        <p class="level-description">Recreational or amateur league player</p>
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="step-navigation">
                            <button type="button" class="prev-step-btn" data-prev="1">Back</button>
                            <button type="button" class="next-step-btn" data-next="3">Continue</button>
                        </div>
                    </div>

                    <!-- Step 3: User Details -->
                    <div class="registration-step" id="step-3">
                        <h3 class="step-title">Complete your profile information</h3>


                        <form class="register-form" id="register-form">
                            <!-- Name Fields Container - Will be dynamically filled -->
                            <div id="name-fields-container">
                                <!-- Personal Name Fields (default) -->
                                <div class="form-row personal-name-fields" id="personal-name-fields">
                                    <div class="form-group">
                                        <label for="register-first-name">First Name</label>
                                        <input type="text" id="register-first-name" name="first-name" placeholder="First name" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="register-last-name">Last Name</label>
                                        <input type="text" id="register-last-name" name="last-name" placeholder="Last name" required>
                                    </div>
                                </div>

                                <!-- Nickname Field (optional, for non-club users) -->
                                <div class="form-group nickname-field" id="nickname-field">
                                    <label for="register-nickname">Nickname <span class="optional-label">(Optional)</span></label>
                                    <input type="text" id="register-nickname" name="nickname" placeholder="Enter your nickname or known name">
                                </div>

                                <!-- Club Name Field (hidden by default) -->
                                <div class="form-group club-name-field" id="club-name-field" style="display: none;">
                                    <label for="register-club-name">Club Name</label>
                                    <input type="text" id="register-club-name" name="club-name" placeholder="Enter club name" required disabled>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="register-email">Email</label>
                                <div class="input-with-icon">
                                    <i class="fas fa-envelope"></i>
                                    <input type="email" id="register-email" name="email" placeholder="Email address" required>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="register-password">Password</label>
                                <div class="input-with-icon">
                                    <i class="fas fa-lock"></i>
                                    <input type="password" id="register-password" name="password" placeholder="Create a password" required>
                                    <i class="fas fa-eye toggle-password"></i>
                                </div>
                                <div class="password-strength">
                                    <div class="strength-meter">
                                        <div class="strength-segment"></div>
                                        <div class="strength-segment"></div>
                                        <div class="strength-segment"></div>
                                        <div class="strength-segment"></div>
                                    </div>
                                    <span class="strength-text">Password strength</span>
                                </div>
                            </div>

                            <div class="form-row sports-fields">
                                <div class="form-group sport-field">
                                    <label for="sport-select">Sport Category</label>
                                    <select id="sport-select" name="sport" required>
                                        <option value="" disabled selected>Select sport category</option>
                                        <option value="football">Football</option>
                                        <option value="basketball">Basketball</option>
                                        <option value="volleyball">Volleyball</option>
                                        <option value="baseball">Baseball</option>
                                        <option value="cricket">Cricket</option>
                                        <option value="hockey">Field Hockey</option>
                                    </select>
                                </div>
                                <div class="form-group level-field">
                                    <label for="level-select">Professional Level</label>
                                    <select id="level-select" name="level" required>
                                        <option value="" disabled selected>Select your level</option>
                                        <option value="professional">Professional</option>
                                        <option value="semi-pro">Semi-Professional</option>
                                        <option value="youth">Youth/Academy</option>
                                        <option value="amateur">Amateur</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group terms-privacy">
                                <input type="checkbox" id="terms-agree" name="terms" required>
                                <label for="terms-agree">I agree to the <a href="#">Terms of Service</a> and <a href="#">Privacy Policy</a></label>
                            </div>

                            <div class="step-navigation">
                                <button type="button" class="prev-step-btn" data-prev="2">Back</button>
                                <button type="submit" class="register-button">Create Account</button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="social-register">
                    <p>Or register with</p>
                    <div class="social-buttons">
                        <button class="social-btn google">
                            <i class="fab fa-google"></i>
                            <span>Google</span>
                        </button>
                        <button class="social-btn facebook">
                            <i class="fab fa-facebook-f"></i>
                            <span>Facebook</span>
                        </button>
                        <button class="social-btn apple">
                            <i class="fab fa-apple"></i>
                            <span>Apple</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="login-footer">
        <div class="footer-links">
            <a href="#">About</a>
            <a href="#">Privacy</a>
            <a href="#">Terms</a>
            <a href="#">Help Center</a>
            <a href="#">Contact</a>
        </div>
        <div class="footer-copyright">
            &copy; 2023 Telyz. All rights reserved.
        </div>
    </footer>

    <script src="login_script.js"></script>
</body>
</html>
