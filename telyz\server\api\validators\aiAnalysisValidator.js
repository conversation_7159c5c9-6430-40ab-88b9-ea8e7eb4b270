/**
 * AI Analysis Validator
 * 
 * Validation middleware for AI analysis requests.
 */

const { body, param, validationResult } = require('express-validator');

/**
 * Validate analysis request
 */
exports.validateAnalysisRequest = [
  body('title')
    .optional()
    .isString()
    .withMessage('Title must be a string')
    .isLength({ min: 3, max: 100 })
    .withMessage('Title must be between 3 and 100 characters'),
  
  body('description')
    .optional()
    .isString()
    .withMessage('Description must be a string')
    .isLength({ max: 500 })
    .withMessage('Description must be at most 500 characters'),
  
  body('sport')
    .isString()
    .withMessage('Sport must be a string')
    .isIn(['football', 'basketball', 'volleyball', 'baseball', 'cricket', 'hockey'])
    .withMessage('Invalid sport type'),
  
  body('position')
    .isString()
    .withMessage('Position must be a string')
    .notEmpty()
    .withMessage('Position is required'),
  
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  }
];

/**
 * Validate chat request
 */
exports.validateChatRequest = [
  body('message')
    .isString()
    .withMessage('Message must be a string')
    .notEmpty()
    .withMessage('Message is required')
    .isLength({ max: 1000 })
    .withMessage('Message must be at most 1000 characters'),
  
  body('analysisId')
    .optional()
    .isString()
    .withMessage('Analysis ID must be a string'),
  
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  }
];