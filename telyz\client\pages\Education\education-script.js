/**
 * EDUCATION PLATFORM JAVASCRIPT - TELYZ
 * Advanced Male Sports Education Hub Functionality
 */

// Global Variables
let currentFilter = {
    sport: 'football',
    contentType: ['live', 'video', 'course'],
    topics: [],
    level: '',
    duration: [],
    search: ''
};

let currentView = 'grid';
let isLoading = false;
let currentPage = 1;
const itemsPerPage = 12;

// Sample Data for Male Sports Education
const sampleEducationData = {
    liveSession: [
        {
            id: 1,
            title: 'Advanced Tactical Formations',
            expert: {
                name: 'Coach <PERSON>',
                title: 'Former FC Barcelona Assistant Coach',
                avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=face&auto=format',
                rating: 4.9
            },
            sport: 'football',
            topic: 'tactics',
            level: 'advanced',
            viewers: 1247,
            duration: 45,
            isLive: true,
            thumbnail: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=250&fit=crop&auto=format',
            tags: ['Tactics', 'Formation', 'Advanced']
        },
        {
            id: 2,
            title: 'Basketball Shooting Fundamentals',
            expert: {
                name: 'Coach <PERSON>',
                title: 'Former NBA Player',
                avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=60&h=60&fit=crop&crop=face&auto=format',
                rating: 4.8
            },
            sport: 'basketball',
            topic: 'skills',
            level: 'beginner',
            viewers: 892,
            duration: 30,
            isLive: true,
            thumbnail: 'https://images.unsplash.com/photo-**********-68e109498ffc?w=400&h=250&fit=crop&auto=format',
            tags: ['Shooting', 'Fundamentals', 'Technique']
        },
        {
            id: 3,
            title: 'Tennis Serve Mastery',
            expert: {
                name: 'Coach Andre Williams',
                title: 'Former ATP Player',
                avatar: 'https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=60&h=60&fit=crop&crop=face&auto=format',
                rating: 4.7
            },
            sport: 'tennis',
            topic: 'skills',
            level: 'intermediate',
            viewers: 534,
            duration: 35,
            isLive: true,
            thumbnail: 'https://images.unsplash.com/photo-1622279457486-62dcc4a431d6?w=400&h=250&fit=crop&auto=format',
            tags: ['Serve', 'Technique', 'Power']
        }
    ],
    videos: [
        {
            id: 101,
            title: 'Perfect Free Kick Technique',
            expert: {
                name: 'Coach David Silva',
                title: 'Professional Football Coach',
                avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=60&h=60&fit=crop&crop=face&auto=format'
            },
            sport: 'football',
            topic: 'skills',
            level: 'intermediate',
            duration: 15,
            views: 15420,
            likes: 1240,
            thumbnail: 'https://images.unsplash.com/photo-1431324155629-1a6deb1dec8d?w=400&h=250&fit=crop&auto=format',
            tags: ['Free Kick', 'Technique', 'Skills'],
            isPremium: false,
            description: 'Master the art of free kick taking with professional techniques used by top male players.'
        },
        {
            id: 102,
            title: 'Mental Preparation for Competition',
            expert: {
                name: 'Dr. Mark Thompson',
                title: 'Sports Psychologist',
                avatar: 'https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=60&h=60&fit=crop&crop=face&auto=format'
            },
            sport: 'football',
            topic: 'psychology',
            level: 'intermediate',
            duration: 25,
            views: 8950,
            likes: 890,
            thumbnail: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=250&fit=crop&auto=format',
            tags: ['Mental', 'Psychology', 'Preparation'],
            isPremium: true,
            description: 'Learn how to prepare mentally for important matches and competitions.'
        },
        {
            id: 103,
            title: 'Basketball Dribbling Drills',
            expert: {
                name: 'Coach James Carter',
                title: 'NBA Skills Coach',
                avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=face&auto=format'
            },
            sport: 'basketball',
            topic: 'skills',
            level: 'beginner',
            duration: 20,
            views: 12300,
            likes: 980,
            thumbnail: 'https://images.unsplash.com/photo-**********-68e109498ffc?w=400&h=250&fit=crop&auto=format',
            tags: ['Dribbling', 'Ball Handling', 'Fundamentals'],
            isPremium: false,
            description: 'Essential dribbling drills to improve your ball handling skills.'
        }
    ],
    courses: [
        {
            id: 201,
            title: 'Complete Football Coaching Masterclass',
            expert: {
                name: 'Coach Antonio Hernandez',
                title: 'UEFA Pro License Coach',
                avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=face&auto=format'
            },
            sport: 'football',
            topic: 'tactics',
            level: 'advanced',
            duration: 480,
            students: 2340,
            rating: 4.9,
            price: 129,
            thumbnail: 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=400&h=250&fit=crop&auto=format',
            tags: ['Complete Course', 'Advanced', 'Mastery'],
            isPremium: true,
            description: 'Comprehensive coaching course covering all aspects of modern football tactics and strategy.'
        },
        {
            id: 202,
            title: 'Basketball Coaching Fundamentals',
            expert: {
                name: 'Coach Robert Davis',
                title: 'College Basketball Coach',
                avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=60&h=60&fit=crop&crop=face&auto=format'
            },
            sport: 'basketball',
            topic: 'tactics',
            level: 'intermediate',
            duration: 360,
            students: 1890,
            rating: 4.7,
            price: 99,
            thumbnail: 'https://images.unsplash.com/photo-**********-68e109498ffc?w=400&h=250&fit=crop&auto=format',
            tags: ['Coaching', 'Strategy', 'Team Management'],
            isPremium: true,
            description: 'Learn the fundamentals of basketball coaching and team management.'
        }
    ]
};

// Chat Messages for Live Sessions (Male Sports Focus)
const sampleChatMessages = [
    {
        id: 1,
        user: 'Ahmed_M',
        message: 'Amazing explanation of the 4-3-3 formation!',
        timestamp: new Date(Date.now() - 30000),
        isPremium: true
    },
    {
        id: 2,
        user: 'CoachSalim',
        message: 'How do you handle pressing in this formation?',
        timestamp: new Date(Date.now() - 45000),
        isPremium: true
    },
    {
        id: 3,
        user: 'FootballFan23',
        message: 'Can you show the attacking transitions?',
        timestamp: new Date(Date.now() - 60000),
        isPremium: false
    },
    {
        id: 4,
        user: 'MohamedK',
        message: 'This is exactly what our team needed!',
        timestamp: new Date(Date.now() - 90000),
        isPremium: true
    }
];

// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeEducationPlatform();
});

// Initialize Platform
function initializeEducationPlatform() {
    console.log('🎓 Initializing Telyz Education Platform...');
    
    // Hide loading screen
    setTimeout(() => {
        hideLoadingScreen();
    }, 2000);
    
    // Initialize components
    initializeEventListeners();
    initializeFilters();
    loadInitialContent();
    initializeSearch();
    initializeToasts();
    
    console.log('✅ Telyz Education Platform Initialized Successfully!');
}

// Hide Loading Screen
function hideLoadingScreen() {
    const loadingScreen = document.getElementById('loadingScreen');
    if (loadingScreen) {
        loadingScreen.classList.add('hidden');
        setTimeout(() => {
            loadingScreen.style.display = 'none';
        }, 500);
    }
}

// Initialize Event Listeners
function initializeEventListeners() {
    // Header scroll effect
    window.addEventListener('scroll', handleHeaderScroll);
    
    // Quick access cards
    document.querySelectorAll('.quick-access-card').forEach(card => {
        card.addEventListener('click', handleQuickAccessClick);
    });
    
    // Filter toggles
    document.addEventListener('change', handleFilterChange);
    
    // View toggle
    document.querySelectorAll('.view-btn').forEach(btn => {
        btn.addEventListener('click', handleViewToggle);
    });
    
    // Sort change
    const sortSelect = document.getElementById('sortSelect');
    if (sortSelect) {
        sortSelect.addEventListener('change', handleSortChange);
    }
    
    // Load more button
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', handleLoadMore);
    }
    
    // Modal close buttons
    document.querySelectorAll('.modal-close').forEach(btn => {
        btn.addEventListener('click', closeModal);
    });
    
    // Search functionality
    const searchInput = document.getElementById('mainSearchInput');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(handleSearchInput, 300));
        searchInput.addEventListener('focus', showSearchSuggestions);
    }
    
    // Premium button
    const premiumBtn = document.getElementById('premiumBtn');
    if (premiumBtn) {
        premiumBtn.addEventListener('click', showPremiumModal);
    }
    
    // Mobile filters toggle
    const filtersToggle = document.getElementById('filtersToggle');
    if (filtersToggle) {
        filtersToggle.addEventListener('click', toggleMobileFilters);
    }
    
    // Chat functionality
    const chatSendBtn = document.getElementById('chatSendBtn');
    const chatInput = document.getElementById('chatInput');
    if (chatSendBtn && chatInput) {
        chatSendBtn.addEventListener('click', sendChatMessage);
        chatInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendChatMessage();
            }
        });
    }
}

// Handle Header Scroll
function handleHeaderScroll() {
    const header = document.querySelector('.education-header');
    if (header) {
        if (window.scrollY > 50) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    }
}

// Handle Quick Access Click
function handleQuickAccessClick(event) {
    const card = event.currentTarget;
    const section = card.dataset.section;
    
    // Add click animation
    card.style.transform = 'translateY(-2px) scale(0.98)';
    setTimeout(() => {
        card.style.transform = '';
    }, 150);
    
    // Navigate to section
    switch(section) {
        case 'live':
            scrollToSection('liveSessionsSection');
            showToast('Navigating to Live Sessions', 'info');
            break;
        case 'library':
            scrollToSection('videoLibrarySection');
            showToast('Opening Video Library', 'info');
            break;
        case 'ai':
            showPremiumModal();
            break;
        case 'progress':
            showToast('Opening Progress Tracker', 'info');
            break;
    }
}

// Scroll to Section
function scrollToSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) {
        section.scrollIntoView({ 
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// Initialize Filters
function initializeFilters() {
    // Set default sport
    const defaultSport = document.querySelector('.sport-option[data-sport="football"]');
    if (defaultSport) {
        defaultSport.classList.add('active');
    }
    
    // Check default content types
    document.querySelectorAll('input[name="contentType"]').forEach(input => {
        input.checked = true;
    });
}

// Handle Filter Change
function handleFilterChange(event) {
    const target = event.target;
    
    if (target.name === 'contentType') {
        updateContentTypeFilter();
    } else if (target.name === 'topic') {
        updateTopicsFilter();
    } else if (target.name === 'level') {
        updateLevelFilter();
    } else if (target.name === 'duration') {
        updateDurationFilter();
    }
    
    // Debounce content reload
    debounce(loadFilteredContent, 300)();
}

// Update Filters
function updateContentTypeFilter() {
    currentFilter.contentType = Array.from(
        document.querySelectorAll('input[name="contentType"]:checked')
    ).map(input => input.value);
}

function updateTopicsFilter() {
    currentFilter.topics = Array.from(
        document.querySelectorAll('input[name="topic"]:checked')
    ).map(input => input.value);
}

function updateLevelFilter() {
    const checkedLevel = document.querySelector('input[name="level"]:checked');
    currentFilter.level = checkedLevel ? checkedLevel.value : '';
}

function updateDurationFilter() {
    currentFilter.duration = Array.from(
        document.querySelectorAll('input[name="duration"]:checked')
    ).map(input => input.value);
}

// Handle Sport Selection
document.addEventListener('click', function(event) {
    if (event.target.matches('.sport-option') || event.target.closest('.sport-option')) {
        const sportOption = event.target.closest('.sport-option') || event.target;
        const sport = sportOption.dataset.sport;
        
        // Update active state
        document.querySelectorAll('.sport-option').forEach(option => {
            option.classList.remove('active');
        });
        sportOption.classList.add('active');
        
        // Update filter
        currentFilter.sport = sport;
        
        // Update content
        loadFilteredContent();
        
        showToast(`Switched to ${sport} content`, 'success');
    }
});

// Load Initial Content
function loadInitialContent() {
    loadLiveSessions();
    loadFeaturedContent();
    loadVideoLibrary();
}

// Load Live Sessions
function loadLiveSessions() {
    const grid = document.getElementById('liveSessionsGrid');
    if (!grid) return;
    
    grid.innerHTML = '';
    
    sampleEducationData.liveSession.forEach(session => {
        const sessionCard = createLiveSessionCard(session);
        grid.appendChild(sessionCard);
    });
}

// Create Live Session Card
function createLiveSessionCard(session) {
    const card = document.createElement('div');
    card.className = 'live-session-card';
    card.innerHTML = `
        <div class="session-thumbnail">
            <img src="${session.thumbnail}" alt="${session.title}" loading="lazy">
            <div class="live-badge">
                <i class="fas fa-circle"></i>
                <span>LIVE</span>
            </div>
            <div class="session-viewers">
                <i class="fas fa-users"></i>
                <span>${formatNumber(session.viewers)}</span>
            </div>
            <div class="session-duration">
                <i class="fas fa-clock"></i>
                <span>${session.duration}m</span>
            </div>
        </div>
        <div class="session-content">
            <div class="session-header">
                <h4>${session.title}</h4>
                <div class="session-tags">
                    ${session.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                </div>
            </div>
            <div class="expert-info">
                <img src="${session.expert.avatar}" alt="${session.expert.name}" class="expert-avatar">
                <div class="expert-details">
                    <span class="expert-name">${session.expert.name}</span>
                    <span class="expert-title">${session.expert.title}</span>
                    <div class="expert-rating">
                        ${createStarRating(session.expert.rating)}
                        <span>${session.expert.rating}</span>
                    </div>
                </div>
            </div>
            <div class="session-actions">
                <button class="btn btn-primary join-session-btn" onclick="joinLiveSession(${session.id})">
                    <i class="fas fa-video"></i>
                    Join Session
                </button>
                <button class="btn btn-ghost bookmark-btn" title="Bookmark">
                    <i class="far fa-bookmark"></i>
                </button>
                <button class="btn btn-ghost share-btn" title="Share">
                    <i class="fas fa-share"></i>
                </button>
            </div>
        </div>
    `;
    
    return card;
}

// Load Featured Content
function loadFeaturedContent() {
    const grid = document.getElementById('featuredGrid');
    if (!grid) return;
    
    grid.innerHTML = '';
    
    // Combine videos and courses for featured content
    const featuredContent = [...sampleEducationData.videos, ...sampleEducationData.courses];
    
    featuredContent.forEach(content => {
        const contentCard = createContentCard(content, 'featured');
        grid.appendChild(contentCard);
    });
}

// Load Video Library
function loadVideoLibrary() {
    const grid = document.getElementById('videoLibraryGrid');
    if (!grid) return;
    
    grid.innerHTML = '';
    
    sampleEducationData.videos.forEach(video => {
        const videoCard = createContentCard(video, 'video');
        grid.appendChild(videoCard);
    });
}

// Create Content Card
function createContentCard(content, type = 'video') {
    const card = document.createElement('div');
    card.className = `content-card ${type}-card`;
    
    const isPremium = content.isPremium || content.price;
    const duration = formatDuration(content.duration);
    
    card.innerHTML = `
        <div class="content-thumbnail">
            <img src="${content.thumbnail}" alt="${content.title}" loading="lazy">
            ${isPremium ? '<div class="premium-badge"><i class="fas fa-crown"></i></div>' : ''}
            <div class="content-duration">
                <i class="fas fa-clock"></i>
                <span>${duration}</span>
            </div>
            ${content.price ? `<div class="content-price">$${content.price}</div>` : ''}
            <div class="content-overlay">
                <button class="play-btn" onclick="${type === 'video' ? `playVideo(${content.id})` : `viewCourse(${content.id})`}">
                    <i class="fas fa-play"></i>
                </button>
            </div>
        </div>
        <div class="content-info">
            <div class="content-header">
                <h4>${content.title}</h4>
                <div class="content-stats">
                    ${content.views ? `<span><i class="fas fa-eye"></i> ${formatNumber(content.views)}</span>` : ''}
                    ${content.students ? `<span><i class="fas fa-users"></i> ${formatNumber(content.students)}</span>` : ''}
                    ${content.likes ? `<span><i class="fas fa-heart"></i> ${formatNumber(content.likes)}</span>` : ''}
                </div>
            </div>
            <div class="expert-info">
                <img src="${content.expert.avatar}" alt="${content.expert.name}" class="expert-avatar">
                <div class="expert-details">
                    <span class="expert-name">${content.expert.name}</span>
                    <span class="expert-title">${content.expert.title}</span>
                </div>
            </div>
            <div class="content-tags">
                ${content.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
            </div>
            <div class="content-actions">
                <button class="btn btn-primary content-action-btn" onclick="${type === 'video' ? `playVideo(${content.id})` : `viewCourse(${content.id})`}">
                    <i class="fas fa-${type === 'video' ? 'play' : 'book'}"></i>
                    ${type === 'video' ? 'Watch Now' : 'Enroll Now'}
                </button>
                <button class="btn btn-ghost bookmark-btn" title="Bookmark">
                    <i class="far fa-bookmark"></i>
                </button>
                <button class="btn btn-ghost share-btn" title="Share">
                    <i class="fas fa-share"></i>
                </button>
            </div>
        </div>
    `;
    
    return card;
}

// Utility Functions
function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

function formatDuration(minutes) {
    if (minutes >= 60) {
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        return `${hours}h ${mins}m`;
    }
    return `${minutes}m`;
}

function createStarRating(rating) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    let stars = '';
    
    for (let i = 0; i < 5; i++) {
        if (i < fullStars) {
            stars += '<i class="fas fa-star"></i>';
        } else if (i === fullStars && hasHalfStar) {
            stars += '<i class="fas fa-star-half-alt"></i>';
        } else {
            stars += '<i class="far fa-star"></i>';
        }
    }
    
    return stars;
}

// Search Functionality
function initializeSearch() {
    const searchInput = document.getElementById('mainSearchInput');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(handleSearchInput, 300));
    }
}

function handleSearchInput(event) {
    const query = event.target.value.trim();
    currentFilter.search = query;
    
    if (query.length > 0) {
        showSearchSuggestions();
        loadFilteredContent();
    } else {
        hideSearchSuggestions();
        loadInitialContent();
    }
}

function showSearchSuggestions() {
    const suggestions = document.getElementById('searchSuggestions');
    if (suggestions) {
        suggestions.classList.add('active');
    }
}

function hideSearchSuggestions() {
    const suggestions = document.getElementById('searchSuggestions');
    if (suggestions) {
        suggestions.classList.remove('active');
    }
}

// Filter Content
function loadFilteredContent() {
    if (isLoading) return;
    
    isLoading = true;
    showLoadingIndicator();
    
    setTimeout(() => {
        loadInitialContent();
        hideLoadingIndicator();
        isLoading = false;
    }, 500);
}

// Loading Indicators
function showLoadingIndicator() {
    document.querySelectorAll('.live-sessions-grid, .featured-content-grid, .video-library-grid').forEach(grid => {
        grid.classList.add('loading');
    });
}

function hideLoadingIndicator() {
    document.querySelectorAll('.live-sessions-grid, .featured-content-grid, .video-library-grid').forEach(grid => {
        grid.classList.remove('loading');
    });
}

// Modal Functions
function joinLiveSession(sessionId) {
    const modal = document.getElementById('liveSessionModal');
    if (modal) {
        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
        
        loadChatMessages();
        showToast('Joining live session...', 'success');
    }
}

function playVideo(videoId) {
    const modal = document.getElementById('videoPlayerModal');
    if (modal) {
        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
        
        const video = sampleEducationData.videos.find(v => v.id === videoId);
        if (video) {
            document.getElementById('videoModalTitle').textContent = video.title;
            document.getElementById('videoModalDescription').textContent = video.description;
            document.getElementById('expertName').textContent = video.expert.name;
            document.getElementById('expertTitle').textContent = video.expert.title;
        }
        
        showToast('Loading video...', 'info');
    }
}

function viewCourse(courseId) {
    showToast('Opening course details...', 'info');
}

function showPremiumModal() {
    const modal = document.getElementById('premiumModal');
    if (modal) {
        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
    }
}

function closeModal(event) {
    const modal = event.target.closest('.modal-overlay');
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = '';
    }
}

// Chat Functions
function loadChatMessages() {
    const chatContainer = document.getElementById('chatMessages');
    if (!chatContainer) return;
    
    chatContainer.innerHTML = '';
    
    sampleChatMessages.forEach(message => {
        const messageElement = createChatMessage(message);
        chatContainer.appendChild(messageElement);
    });
    
    chatContainer.scrollTop = chatContainer.scrollHeight;
}

function createChatMessage(message) {
    const div = document.createElement('div');
    div.className = `chat-message ${message.isPremium ? 'premium-user' : ''}`;
    
    const timeStr = message.timestamp.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    
    div.innerHTML = `
        <div class="message-info">
            <span class="username">${message.user}</span>
            ${message.isPremium ? '<i class="fas fa-crown premium-icon"></i>' : ''}
            <span class="timestamp">${timeStr}</span>
        </div>
        <div class="message-content">${message.message}</div>
    `;
    
    return div;
}

function sendChatMessage() {
    const input = document.getElementById('chatInput');
    if (!input || !input.value.trim()) return;
    
    const message = {
        id: Date.now(),
        user: 'You',
        message: input.value.trim(),
        timestamp: new Date(),
        isPremium: true
    };
    
    const chatContainer = document.getElementById('chatMessages');
    if (chatContainer) {
        const messageElement = createChatMessage(message);
        chatContainer.appendChild(messageElement);
        chatContainer.scrollTop = chatContainer.scrollHeight;
    }
    
    input.value = '';
    showToast('Message sent!', 'success');
}

// Toast Notifications
function initializeToasts() {
    if (!document.getElementById('toastContainer')) {
        const container = document.createElement('div');
        container.id = 'toastContainer';
        container.className = 'toast-container';
        document.body.appendChild(container);
    }
}

function showToast(message, type = 'info', duration = 3000) {
    const container = document.getElementById('toastContainer');
    if (!container) return;
    
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    
    const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
    };
    
    toast.innerHTML = `
        <i class="${icons[type]}"></i>
        <span>${message}</span>
        <button class="toast-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    container.appendChild(toast);
    
    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, duration);
    
    setTimeout(() => {
        toast.classList.add('show');
    }, 100);
}

// Mobile Functions
function toggleMobileFilters() {
    const sidebar = document.getElementById('contentSidebar');
    const overlay = document.getElementById('mobileOverlay');
    
    if (sidebar && overlay) {
        sidebar.classList.toggle('active');
        overlay.classList.toggle('active');
        document.body.style.overflow = sidebar.classList.contains('active') ? 'hidden' : '';
    }
}

// Handle View Toggle
function handleViewToggle(event) {
    const btn = event.currentTarget;
    const view = btn.dataset.view;
    
    document.querySelectorAll('.view-btn').forEach(button => {
        button.classList.remove('active');
    });
    btn.classList.add('active');
    
    currentView = view;
    updateContentView();
    
    showToast(`Switched to ${view} view`, 'info');
}

function updateContentView() {
    const grids = document.querySelectorAll('.live-sessions-grid, .featured-content-grid, .video-library-grid');
    grids.forEach(grid => {
        if (currentView === 'list') {
            grid.classList.add('list-view');
        } else {
            grid.classList.remove('list-view');
        }
    });
}

function handleSortChange(event) {
    const sortBy = event.target.value;
    showToast(`Sorting by ${sortBy}`, 'info');
    loadFilteredContent();
}

function handleLoadMore() {
    if (isLoading) return;
    
    isLoading = true;
    const btn = document.getElementById('loadMoreBtn');
    const originalText = btn.innerHTML;
    
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
    btn.disabled = true;
    
    setTimeout(() => {
        currentPage++;
        btn.innerHTML = originalText;
        btn.disabled = false;
        isLoading = false;
        showToast('More content loaded!', 'success');
    }, 1500);
}

// Utility: Debounce Function
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Clear Filters
document.addEventListener('click', function(event) {
    if (event.target.matches('#clearFilters')) {
        document.querySelectorAll('input[type="checkbox"]').forEach(input => {
            input.checked = input.name === 'contentType';
        });
        
        document.querySelectorAll('input[type="radio"]').forEach(input => {
            input.checked = false;
        });
        
        currentFilter = {
            sport: 'football',
            contentType: ['live', 'video', 'course'],
            topics: [],
            level: '',
            duration: [],
            search: ''
        };
        
        const searchInput = document.getElementById('mainSearchInput');
        if (searchInput) {
            searchInput.value = '';
        }
        
        loadInitialContent();
        showToast('All filters cleared!', 'info');
    }
});

// Close modal when clicking outside
document.addEventListener('click', function(event) {
    if (event.target.classList.contains('modal-overlay')) {
        closeModal(event);
    }
});

// Keyboard Shortcuts
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        const openModal = document.querySelector('.modal-overlay[style*="flex"]');
        if (openModal) {
            closeModal({ target: openModal });
        }
    }
    
    if (event.ctrlKey && event.key === 'k') {
        event.preventDefault();
        const searchInput = document.getElementById('mainSearchInput');
        if (searchInput) {
            searchInput.focus();
        }
    }
});

console.log('🎓 Telyz Male Sports Education Platform JavaScript Loaded Successfully!');