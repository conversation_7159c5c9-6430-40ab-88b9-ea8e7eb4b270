/**
 * Opportunities Page JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize sidebar submenus
    initSidebarSubmenus();

    // Initialize filter tabs
    initFilterTabs();

    // Initialize filter actions
    initFilterActions();

    // Initialize search functionality
    initSearch();

    // Initialize view controls
    initViewControls();

    // Initialize card actions
    initCardActions();

    // Initialize profile dropdown
    initProfileDropdown();
});

/**
 * Initialize profile dropdown functionality
 */
function initProfileDropdown() {
    const profileDropdownToggle = document.getElementById('profileDropdownToggle');
    const profileDropdownMenu = document.getElementById('profileDropdownMenu');

    if (profileDropdownToggle && profileDropdownMenu) {
        profileDropdownToggle.addEventListener('click', function() {
            const isHidden = profileDropdownMenu.style.display === 'none' || !profileDropdownMenu.style.display;
            profileDropdownMenu.style.display = isHidden ? 'block' : 'none';
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            if (!profileDropdownToggle.contains(event.target) && !profileDropdownMenu.contains(event.target)) {
                profileDropdownMenu.style.display = 'none';
            }
        });
    }
}

/**
 * Initialize sidebar submenus functionality
 */
function initSidebarSubmenus() {
    // Configure AI Analysis dropdown menu
    const aiMenuItem = document.getElementById('aiMenuItem');
    const aiDropdownMenu = document.getElementById('aiDropdownMenu');

    if (aiMenuItem && aiDropdownMenu) {
        aiMenuItem.addEventListener('mouseenter', function() {
            aiDropdownMenu.style.display = 'block';
            aiDropdownMenu.style.zIndex = '100';
        });

        aiMenuItem.addEventListener('mouseleave', function() {
            aiDropdownMenu.style.display = 'none';
        });
    }

    // Configure Sports dropdown menu
    const sportsMenuItem = document.getElementById('sportsMenuItem');
    const sportsSubmenu = document.getElementById('sportsSubmenu');

    if (sportsMenuItem && sportsSubmenu) {
        sportsMenuItem.addEventListener('mouseenter', function() {
            sportsSubmenu.style.display = 'block';
            sportsSubmenu.style.zIndex = '100';
        });

        sportsMenuItem.addEventListener('mouseleave', function() {
            sportsSubmenu.style.display = 'none';
        });
    }
}

/**
 * Initialize filter tabs functionality
 */
function initFilterTabs() {
    const filterTabs = document.querySelectorAll('.filter-tab');

    filterTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Remove active class from all tabs
            filterTabs.forEach(t => t.classList.remove('active'));

            // Add active class to clicked tab
            this.classList.add('active');

            // Get filter value
            const filter = this.getAttribute('data-filter');

            // Here you would typically filter the opportunities based on the selected filter
            console.log('Filter selected:', filter);

            // For demo purposes, show a notification
            showNotification(`Filtering by: ${filter}`);
        });
    });
}

/**
 * Initialize filter actions functionality
 */
function initFilterActions() {
    const resetButton = document.querySelector('.reset-button');
    const applyButton = document.querySelector('.apply-button');
    const filterSelects = document.querySelectorAll('.filter-group select');

    if (resetButton) {
        resetButton.addEventListener('click', function() {
            // Reset all select elements to their first option
            filterSelects.forEach(select => {
                select.selectedIndex = 0;
            });

            // Show notification
            showNotification('Filters have been reset');
        });
    }

    if (applyButton) {
        applyButton.addEventListener('click', function() {
            // Collect filter values
            const filterValues = {};
            filterSelects.forEach(select => {
                const label = select.parentElement.querySelector('label').textContent;
                filterValues[label] = select.options[select.selectedIndex].text;
            });

            // Here you would typically apply the filters to the opportunities list
            console.log('Applied filters:', filterValues);

            // Show notification
            showNotification('Filters applied successfully');
        });
    }
}

/**
 * Initialize search functionality
 */
function initSearch() {
    const searchButton = document.querySelector('.search-button');
    const searchInput = document.querySelector('.search-input-container input');

    if (searchButton && searchInput) {
        searchButton.addEventListener('click', function() {
            const searchTerm = searchInput.value.trim();

            if (searchTerm) {
                // Here you would typically perform the search
                console.log('Searching for:', searchTerm);

                // Show notification
                showNotification(`Searching for: ${searchTerm}`);
            } else {
                // Show error notification
                showNotification('Please enter a search term', 'error');
            }
        });

        // Add event listener for Enter key
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchButton.click();
            }
        });
    }
}

/**
 * Show a notification message
 * @param {string} message - The message to display
 * @param {string} type - The type of notification (success, error, info)
 */
function showNotification(message, type = 'success') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;

    // Set icon based on type
    let icon = 'check-circle';
    if (type === 'error') icon = 'exclamation-circle';
    if (type === 'info') icon = 'info-circle';

    // Set content
    notification.innerHTML = `
        <i class="fas fa-${icon}"></i>
        <span>${message}</span>
        <button class="close-notification"><i class="fas fa-times"></i></button>
    `;

    // Add to document
    document.body.appendChild(notification);

    // Add styles if they don't exist
    if (!document.getElementById('notification-styles')) {
        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            .notification {
                position: fixed;
                top: 20px;
                right: 20px;
                background-color: white;
                border-radius: 8px;
                padding: 15px 20px;
                display: flex;
                align-items: center;
                box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
                z-index: 1000;
                animation: slideIn 0.3s forwards;
            }

            .notification.success i:first-child {
                color: #4CAF50;
            }

            .notification.error i:first-child {
                color: #F44336;
            }

            .notification.info i:first-child {
                color: #2196F3;
            }

            .notification i:first-child {
                font-size: 18px;
                margin-right: 10px;
            }

            .notification span {
                flex: 1;
                font-size: 14px;
                color: #333;
            }

            .close-notification {
                background: none;
                border: none;
                color: #999;
                cursor: pointer;
                font-size: 14px;
                padding: 0;
                margin-left: 10px;
            }

            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }

            @keyframes slideOut {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }

    // Add close button functionality
    const closeButton = notification.querySelector('.close-notification');
    closeButton.addEventListener('click', function() {
        notification.style.animation = 'slideOut 0.3s forwards';
        setTimeout(() => {
            notification.remove();
        }, 300);
    });

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (document.body.contains(notification)) {
            notification.style.animation = 'slideOut 0.3s forwards';
            setTimeout(() => {
                notification.remove();
            }, 300);
        }
    }, 5000);
}

/**
 * Initialize view controls functionality
 */
function initViewControls() {
    const viewControls = document.querySelectorAll('.view-control');
    const opportunitiesGrid = document.querySelector('.opportunities-grid');

    if (viewControls.length && opportunitiesGrid) {
        viewControls.forEach(control => {
            control.addEventListener('click', function() {
                // Remove active class from all controls
                viewControls.forEach(c => c.classList.remove('active'));

                // Add active class to clicked control
                this.classList.add('active');

                // Get view type
                const viewType = this.getAttribute('data-view');

                // Apply view type
                if (viewType === 'grid') {
                    opportunitiesGrid.style.display = 'grid';
                    opportunitiesGrid.classList.remove('list-view');
                } else if (viewType === 'list') {
                    opportunitiesGrid.style.display = 'flex';
                    opportunitiesGrid.style.flexDirection = 'column';
                    opportunitiesGrid.classList.add('list-view');
                }

                // Show notification
                showNotification(`View changed to ${viewType} view`);
            });
        });
    }
}

/**
 * Initialize card actions functionality
 */
function initCardActions() {
    // View details buttons
    const viewDetailsButtons = document.querySelectorAll('.view-details-btn');
    if (viewDetailsButtons.length) {
        viewDetailsButtons.forEach(button => {
            button.addEventListener('click', function() {
                const card = this.closest('.featured-card');
                const title = card.querySelector('h3').textContent;

                // Show notification
                showNotification(`Viewing details for: ${title}`);
            });
        });
    }

    // Save buttons
    const saveButtons = document.querySelectorAll('.save-btn');
    if (saveButtons.length) {
        saveButtons.forEach(button => {
            button.addEventListener('click', function() {
                const card = this.closest('.featured-card');
                const title = card.querySelector('h3').textContent;

                // Toggle saved state
                if (this.classList.contains('saved')) {
                    this.classList.remove('saved');
                    this.innerHTML = '<i class="far fa-bookmark"></i>';
                    showNotification(`Removed from saved: ${title}`, 'info');
                } else {
                    this.classList.add('saved');
                    this.innerHTML = '<i class="fas fa-bookmark"></i>';
                    showNotification(`Saved to your list: ${title}`, 'success');
                }
            });
        });
    }

    // Apply now buttons
    const applyButtons = document.querySelectorAll('.apply-now-btn');
    if (applyButtons.length) {
        applyButtons.forEach(button => {
            button.addEventListener('click', function() {
                const card = this.closest('.opportunity-card');
                const title = card.querySelector('h3').textContent;

                // Show notification
                showNotification(`Application started for: ${title}`, 'success');
            });
        });
    }

    // Load more button
    const loadMoreButton = document.querySelector('.load-more-btn');
    if (loadMoreButton) {
        loadMoreButton.addEventListener('click', function() {
            // Show loading state
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
            this.disabled = true;

            // Simulate loading delay
            setTimeout(() => {
                // Reset button
                this.innerHTML = 'Load More <i class="fas fa-sync"></i>';
                this.disabled = false;

                // Show notification
                showNotification('No more opportunities to load', 'info');
            }, 1500);
        });
    }
}
