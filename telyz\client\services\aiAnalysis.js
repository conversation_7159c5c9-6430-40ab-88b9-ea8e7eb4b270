/**
 * AI Analysis Service
 * 
 * Service for handling AI analysis operations.
 */

const api = require('./api');

class AIAnalysisService {
  /**
   * Create an AI analysis service
   */
  constructor() {
    // Bind methods
    this.uploadVideo = this.uploadVideo.bind(this);
    this.getAnalyses = this.getAnalyses.bind(this);
    this.getAnalysisById = this.getAnalysisById.bind(this);
    this.getAnalysisStatus = this.getAnalysisStatus.bind(this);
    this.getAnalysisReport = this.getAnalysisReport.bind(this);
    this.deleteAnalysis = this.deleteAnalysis.bind(this);
    this.shareAnalysis = this.shareAnalysis.bind(this);
    this.updateAnalysisVisibility = this.updateAnalysisVisibility.bind(this);
  }
  
  /**
   * Upload a video for analysis
   * 
   * @param {FormData} formData - Form data with video and metadata
   * @param {Function} onProgress - Progress callback
   * @returns {Promise} Promise with analysis data
   */
  async uploadVideo(formData, onProgress = null) {
    try {
      return await api.upload('/ai-analysis/upload', formData, onProgress);
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * Get all analyses for the current user
   * 
   * @param {Object} params - Query parameters
   * @returns {Promise} Promise with analyses data
   */
  async getAnalyses(params = {}) {
    try {
      return await api.get('/ai-analysis', params);
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * Get analysis by ID
   * 
   * @param {string} id - Analysis ID
   * @returns {Promise} Promise with analysis data
   */
  async getAnalysisById(id) {
    try {
      return await api.get(`/ai-analysis/${id}`);
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * Get analysis status
   * 
   * @param {string} id - Analysis ID
   * @returns {Promise} Promise with status data
   */
  async getAnalysisStatus(id) {
    try {
      return await api.get(`/ai-analysis/${id}/status`);
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * Get analysis report
   * 
   * @param {string} id - Analysis ID
   * @returns {Promise} Promise with report data
   */
  async getAnalysisReport(id) {
    try {
      return await api.get(`/ai-analysis/${id}/report`);
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * Delete analysis
   * 
   * @param {string} id - Analysis ID
   * @returns {Promise} Promise that resolves when analysis is deleted
   */
  async deleteAnalysis(id) {
    try {
      return await api.delete(`/ai-analysis/${id}`);
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * Share analysis with other users
   * 
   * @param {string} id - Analysis ID
   * @param {string[]} userIds - User IDs to share with
   * @returns {Promise} Promise that resolves when analysis is shared
   */
  async shareAnalysis(id, userIds) {
    try {
      return await api.post(`/ai-analysis/${id}/share`, { userIds });
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * Update analysis visibility
   * 
   * @param {string} id - Analysis ID
   * @param {boolean} isPublic - Whether analysis should be public
   * @returns {Promise} Promise that resolves when visibility is updated
   */
  async updateAnalysisVisibility(id, isPublic) {
    try {
      return await api.put(`/ai-analysis/${id}/visibility`, { isPublic });
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * Get public analyses
   * 
   * @param {Object} params - Query parameters
   * @returns {Promise} Promise with public analyses data
   */
  async getPublicAnalyses(params = {}) {
    try {
      return await api.get('/ai-analysis/public', params);
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * Get analyses shared with current user
   * 
   * @param {Object} params - Query parameters
   * @returns {Promise} Promise with shared analyses data
   */
  async getSharedAnalyses(params = {}) {
    try {
      return await api.get('/ai-analysis/shared', params);
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * Poll analysis status until complete
   * 
   * @param {string} id - Analysis ID
   * @param {Function} onStatusUpdate - Status update callback
   * @param {number} interval - Polling interval in milliseconds
   * @returns {Promise} Promise that resolves when analysis is complete
   */
  async pollAnalysisStatus(id, onStatusUpdate = null, interval = 5000) {
    return new Promise((resolve, reject) => {
      const checkStatus = async () => {
        try {
          const status = await this.getAnalysisStatus(id);
          
          if (onStatusUpdate) {
            onStatusUpdate(status);
          }
          
          if (status.status === 'completed') {
            resolve(status);
            return;
          }
          
          if (status.status === 'failed') {
            reject(new Error(status.error?.message || 'Analysis failed'));
            return;
          }
          
          // Continue polling
          setTimeout(checkStatus, interval);
        } catch (error) {
          reject(error);
        }
      };
      
      // Start polling
      checkStatus();
    });
  }
}

// Create AI analysis service instance
const aiAnalysis = new AIAnalysisService();

// Export AI analysis service
if (typeof module !== 'undefined' && module.exports) {
  module.exports = aiAnalysis;
}
