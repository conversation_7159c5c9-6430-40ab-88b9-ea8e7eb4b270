// JavaScript for the Sports Experts Recommendations page

document.addEventListener('DOMContentLoaded', function() {
    // Menú desplegable del perfil
    const toggleButton = document.getElementById('profileDropdownToggle');
    const dropdownMenu = document.getElementById('profileDropdownMenu');
    const userProfile = document.querySelector('.user-profile-sidebar');

    let isMenuOpen = false;

    // Función para mostrar/ocultar el menú
    toggleButton.addEventListener('click', function(e) {
        e.stopPropagation();
        isMenuOpen = !isMenuOpen;
        dropdownMenu.classList.toggle('show', isMenuOpen);

        // Asegurarse de que los iconos sean visibles
        if (isMenuOpen) {
            setTimeout(() => {
                const icons = dropdownMenu.querySelectorAll('i');
                icons.forEach(icon => {
                    icon.style.display = 'inline-block';
                });
            }, 50);
        }
    });

    // Cerrar el menú al hacer clic fuera de él
    document.addEventListener('click', function(e) {
        if (!dropdownMenu.contains(e.target) && !toggleButton.contains(e.target) && !userProfile.contains(e.target)) {
            isMenuOpen = false;
            dropdownMenu.classList.remove('show');
        }
    });

    // Evitar que el menú se cierre al hacer clic dentro de él
    dropdownMenu.addEventListener('click', function(e) {
        e.stopPropagation();
    });

    // Evitar que el menú se cierre al pasar el ratón sobre él
    dropdownMenu.addEventListener('mouseenter', function() {
        if (isMenuOpen) {
            dropdownMenu.classList.add('show');
        }
    });

    // Botón de solicitar nueva recomendación
    const requestRecommendationBtn = document.querySelector('.request-recommendation-btn');
    if (requestRecommendationBtn) {
        requestRecommendationBtn.addEventListener('click', function() {
            showRequestRecommendationModal();
        });
    }

    // Botón de ver recomendaciones dadas
    const recommendationsGivenBtn = document.querySelector('.recommendations-given-btn');
    if (recommendationsGivenBtn) {
        recommendationsGivenBtn.addEventListener('click', function() {
            showRecommendationsGiven();
        });
    }

    // Botones de ver recomendación completa
    const viewFullBtns = document.querySelectorAll('.view-full-btn');
    viewFullBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const card = this.closest('.recommendation-card');
            showFullRecommendation(card);
        });
    });

    // Botón de cargar más recomendaciones
    const loadMoreBtn = document.querySelector('.load-more-btn');
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', function() {
            loadMoreRecommendations();
        });
    }

    // Function to show the recommendation request modal
    function showRequestRecommendationModal() {
        // Create the modal
        const modal = document.createElement('div');
        modal.className = 'recommendation-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Request Recommendation from Sports Expert</h2>
                    <button class="close-modal-btn"><i class="fas fa-times"></i></button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label>Select Sports Expert:</label>
                        <input type="text" placeholder="Search your connections..." class="search-input">
                        <div class="experts-list">
                            <div class="expert-item">
                                <img src="https://i.pravatar.cc/40?img=33" alt="Expert">
                                <div class="expert-info">
                                    <h4>Carlos Rodriguez</h4>
                                    <p>UEFA A Licensed Coach • Barcelona Academy</p>
                                </div>
                                <button class="select-expert-btn">Select</button>
                            </div>
                            <div class="expert-item">
                                <img src="https://i.pravatar.cc/40?img=68" alt="Expert">
                                <div class="expert-info">
                                    <h4>Juan Martinez</h4>
                                    <p>Talent Scout • Real Madrid</p>
                                </div>
                                <button class="select-expert-btn">Select</button>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Professional Relationship Type:</label>
                        <div class="radio-group">
                            <label class="radio-label">
                                <input type="radio" name="relationship" value="coach" checked>
                                <span>Current/Former Coach</span>
                            </label>
                            <label class="radio-label">
                                <input type="radio" name="relationship" value="scout">
                                <span>Talent Scout</span>
                            </label>
                            <label class="radio-label">
                                <input type="radio" name="relationship" value="director">
                                <span>Academy Director</span>
                            </label>
                            <label class="radio-label">
                                <input type="radio" name="relationship" value="other">
                                <span>Other</span>
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Personal Message (optional):</label>
                        <textarea placeholder="Write a personal message to the expert..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="cancel-btn">Cancel</button>
                    <button class="send-request-btn">Send Request</button>
                </div>
            </div>
        `;

        // Añadir estilos inline para el modal
        modal.style.position = 'fixed';
        modal.style.top = '0';
        modal.style.left = '0';
        modal.style.width = '100%';
        modal.style.height = '100%';
        modal.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
        modal.style.display = 'flex';
        modal.style.justifyContent = 'center';
        modal.style.alignItems = 'center';
        modal.style.zIndex = '9999';

        const modalContent = modal.querySelector('.modal-content');
        modalContent.style.backgroundColor = 'white';
        modalContent.style.borderRadius = '10px';
        modalContent.style.width = '90%';
        modalContent.style.maxWidth = '500px';
        modalContent.style.maxHeight = '80vh';
        modalContent.style.overflow = 'auto';
        modalContent.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.3)';
        modalContent.style.direction = 'ltr';
        modalContent.style.textAlign = 'left';

        // Añadir el modal al DOM
        document.body.appendChild(modal);

        // Cerrar el modal
        const closeBtn = modal.querySelector('.close-modal-btn');
        const cancelBtn = modal.querySelector('.cancel-btn');

        closeBtn.addEventListener('click', function() {
            document.body.removeChild(modal);
        });

        cancelBtn.addEventListener('click', function() {
            document.body.removeChild(modal);
        });

        // Evitar que el clic en el contenido del modal cierre el modal
        modalContent.addEventListener('click', function(e) {
            e.stopPropagation();
        });

        // Cerrar el modal al hacer clic fuera del contenido
        modal.addEventListener('click', function() {
            document.body.removeChild(modal);
        });

        // Enviar solicitud
        const sendRequestBtn = modal.querySelector('.send-request-btn');
        sendRequestBtn.addEventListener('click', function() {
            // Aquí iría la lógica para enviar la solicitud
            document.body.removeChild(modal);
            showSuccessMessage('Recommendation request sent successfully');
        });
    }

    // Función para mostrar un mensaje de éxito
    function showSuccessMessage(message) {
        const successMsg = document.createElement('div');
        successMsg.className = 'success-message';
        successMsg.innerHTML = `
            <div class="success-content">
                <i class="fas fa-check-circle"></i>
                <p>${message}</p>
            </div>
        `;

        // Estilos para el mensaje
        successMsg.style.position = 'fixed';
        successMsg.style.bottom = '20px';
        successMsg.style.right = '20px';
        successMsg.style.backgroundColor = '#4caf50';
        successMsg.style.color = 'white';
        successMsg.style.padding = '15px 20px';
        successMsg.style.borderRadius = '8px';
        successMsg.style.boxShadow = '0 3px 10px rgba(0, 0, 0, 0.2)';
        successMsg.style.zIndex = '9999';
        successMsg.style.display = 'flex';
        successMsg.style.alignItems = 'center';
        successMsg.style.animation = 'fadeIn 0.3s ease';

        const successContent = successMsg.querySelector('.success-content');
        successContent.style.display = 'flex';
        successContent.style.alignItems = 'center';

        const icon = successMsg.querySelector('i');
        icon.style.marginLeft = '10px';
        icon.style.fontSize = '20px';

        // Añadir al DOM
        document.body.appendChild(successMsg);

        // Eliminar después de 3 segundos
        setTimeout(function() {
            if (document.body.contains(successMsg)) {
                successMsg.style.animation = 'fadeOut 0.3s ease';
                setTimeout(function() {
                    document.body.removeChild(successMsg);
                }, 300);
            }
        }, 3000);
    }

    // Función para mostrar la recomendación completa
    function showFullRecommendation(card) {
        // Aquí iría la lógica para mostrar la recomendación completa
        console.log('Mostrar recomendación completa', card);
        // Por ahora, simplemente expandimos el texto
        const textElement = card.querySelector('.recommendation-text');
        textElement.style.maxHeight = textElement.style.maxHeight ? null : '1000px';
    }

    // Función para mostrar las recomendaciones dadas
    function showRecommendationsGiven() {
        // Aquí iría la lógica para mostrar las recomendaciones dadas
        console.log('Mostrar recomendaciones dadas');
        showInfoMessage('This feature will be implemented soon');
    }

    // Función para cargar más recomendaciones
    function loadMoreRecommendations() {
        // Aquí iría la lógica para cargar más recomendaciones
        console.log('Cargar más recomendaciones');
        showInfoMessage('Loading more recommendations...');
    }

    // Función para mostrar un mensaje informativo
    function showInfoMessage(message) {
        const infoMsg = document.createElement('div');
        infoMsg.className = 'info-message';
        infoMsg.innerHTML = `
            <div class="info-content">
                <i class="fas fa-info-circle"></i>
                <p>${message}</p>
            </div>
        `;

        // Estilos para el mensaje
        infoMsg.style.position = 'fixed';
        infoMsg.style.bottom = '20px';
        infoMsg.style.right = '20px';
        infoMsg.style.backgroundColor = '#2196F3';
        infoMsg.style.color = 'white';
        infoMsg.style.padding = '15px 20px';
        infoMsg.style.borderRadius = '8px';
        infoMsg.style.boxShadow = '0 3px 10px rgba(0, 0, 0, 0.2)';
        infoMsg.style.zIndex = '9999';
        infoMsg.style.display = 'flex';
        infoMsg.style.alignItems = 'center';
        infoMsg.style.animation = 'fadeIn 0.3s ease';

        const infoContent = infoMsg.querySelector('.info-content');
        infoContent.style.display = 'flex';
        infoContent.style.alignItems = 'center';

        const icon = infoMsg.querySelector('i');
        icon.style.marginLeft = '10px';
        icon.style.fontSize = '20px';

        // Añadir al DOM
        document.body.appendChild(infoMsg);

        // Eliminar después de 3 segundos
        setTimeout(function() {
            if (document.body.contains(infoMsg)) {
                infoMsg.style.animation = 'fadeOut 0.3s ease';
                setTimeout(function() {
                    document.body.removeChild(infoMsg);
                }, 300);
            }
        }, 3000);
    }

    // Añadir animaciones CSS
    const style = document.createElement('style');
    style.textContent = `
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        @keyframes fadeOut {
            from { opacity: 1; transform: translateY(0); }
            to { opacity: 0; transform: translateY(20px); }
        }
    `;
    document.head.appendChild(style);
});
