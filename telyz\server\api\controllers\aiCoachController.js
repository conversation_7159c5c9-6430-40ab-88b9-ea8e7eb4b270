/**
 * AI Coach Controller
 * 
 * Controller for AI coach functionality.
 */

const aiAnalysisService = require('../../../ai-analysis/services');
const llmService = require('../../../ai-analysis/llm');

/**
 * Chat with AI coach
 * 
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
exports.chat = async (req, res) => {
  try {
    const { message, analysisId } = req.body;
    const userId = req.user.id;
    
    // Get chat history from database or session
    const chatHistory = await getChatHistoryFromDb(userId);
    
    // Get analysis context if analysisId is provided
    let context = {};
    if (analysisId) {
      try {
        const analysis = await aiAnalysisService.getAnalysis(analysisId);
        context = { videoAnalysis: analysis };
      } catch (error) {
        console.error(`Error getting analysis ${analysisId}:`, error);
        // Continue without analysis context
      }
    }
    
    // Chat with AI coach
    const response = await aiAnalysisService.chatWithCoach(message, chatHistory, context);
    
    // Save chat history
    await saveChatToHistory(userId, message, response.text);
    
    res.json({
      message: response.text,
      timestamp: response.timestamp,
      analysisContext: analysisId ? { analysisId } : null,
    });
  } catch (error) {
    console.error('Error in AI coach chat:', error);
    res.status(500).json({ message: 'Error processing chat request', error: error.message });
  }
};

/**
 * Get chat history
 * 
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
exports.getChatHistory = async (req, res) => {
  try {
    const userId = req.user.id;
    const limit = parseInt(req.query.limit) || 50;
    
    // Get chat history from database
    const chatHistory = await getChatHistoryFromDb(userId, limit);
    
    res.json({
      history: chatHistory,
      count: chatHistory.length,
    });
  } catch (error) {
    console.error('Error getting chat history:', error);
    res.status(500).json({ message: 'Error retrieving chat history', error: error.message });
  }
};

/**
 * Clear chat history
 * 
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
exports.clearChatHistory = async (req, res) => {
  try {
    const userId = req.user.id;
    
    // Clear chat history in database
    await clearChatHistoryInDb(userId);
    
    res.json({ message: 'Chat history cleared successfully' });
  } catch (error) {
    console.error('Error clearing chat history:', error);
    res.status(500).json({ message: 'Error clearing chat history', error: error.message });
  }
};

/**
 * Get AI coach status
 * 
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 */
exports.getStatus = async (req, res) => {
  try {
    // Check if LLM service is initialized
    let status = 'unavailable';
    let modelInfo = null;
    
    try {
      // Initialize LLM if not already initialized
      const llm = await llmService.initialize();
      status = llm.loaded ? 'ready' : 'initializing';
      modelInfo = llm.modelInfo;
    } catch (error) {
      console.error('Error checking LLM status:', error);
      status = 'error';
    }
    
    res.json({
      status,
      modelInfo,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error getting AI coach status:', error);
    res.status(500).json({ message: 'Error getting AI coach status', error: error.message });
  }
};

// Helper functions

/**
 * Get chat history from database
 * 
 * @param {string} userId - User ID
 * @param {number} limit - Maximum number of messages to retrieve
 * @returns {Promise<Array>} Chat history
 */
const getChatHistoryFromDb = async (userId, limit = 50) => {
  // In a real implementation, this would retrieve chat history from the database
  // For now, we'll return mock data
  return [
    {
      user: 'كيف يمكنني تحسين مهارات التمرير؟',
      assistant: 'التمرير هو أساس لعبة كرة القدم. للتحسين، أنصحك بالتركيز على الدقة قبل القوة. تدرب على التمرير بكلتا القدمين وفي مختلف المسافات. تمارين الروندو ممتازة لتحسين سرعة التمرير والدقة تحت الضغط. أيضاً، عليك تحسين رؤيتك الميدانية من خلال رفع رأسك قبل استلام الكرة لمعرفة خياراتك مسبقاً.',
      timestamp: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
    },
    {
      user: 'ما هي أفضل التمارين للاعب وسط؟',
      assistant: 'لاعب الوسط يحتاج لمجموعة متنوعة من المهارات. أنصح بتمارين تحسين الاستلام والتمرير تحت الضغط، مثل الروندو والتمارين المصغرة 4 ضد 4. أيضاً، تمارين التحول السريع من الدفاع للهجوم مهمة جداً. لا تنسى تمارين اللياقة البدنية والتحمل، فلاعب الوسط يغطي مساحات كبيرة في الملعب. أخيراً، تدرب على التسديد من خارج منطقة الجزاء، فهذه مهارة قيمة للاعب الوسط.',
      timestamp: new Date(Date.now() - 7200000).toISOString(), // 2 hours ago
    },
  ];
};

/**
 * Save chat to history
 * 
 * @param {string} userId - User ID
 * @param {string} userMessage - User message
 * @param {string} assistantMessage - Assistant message
 * @returns {Promise<void>}
 */
const saveChatToHistory = async (userId, userMessage, assistantMessage) => {
  // In a real implementation, this would save the chat to the database
  console.log(`Saving chat for user ${userId}:`);
  console.log(`User: ${userMessage}`);
  console.log(`Assistant: ${assistantMessage}`);
};

/**
 * Clear chat history in database
 * 
 * @param {string} userId - User ID
 * @returns {Promise<void>}
 */
const clearChatHistoryInDb = async (userId) => {
  // In a real implementation, this would clear the chat history in the database
  console.log(`Clearing chat history for user ${userId}`);
};