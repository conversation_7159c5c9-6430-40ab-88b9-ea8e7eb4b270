# Server Configuration
NODE_ENV=development
PORT=3000
HOST=http://localhost:3000
CORS_ORIGIN=*

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/telyz

# JWT Configuration
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRES_IN=1h
JWT_REFRESH_SECRET=your-jwt-refresh-secret-key
JWT_REFRESH_EXPIRES_IN=7d

# Upload Configuration
UPLOAD_DIR=uploads
UPLOAD_MAX_SIZE=104857600

# Email Configuration
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-email-password
EMAIL_FROM=<EMAIL>

# AI Analysis Configuration
AI_ANALYSIS_TEMP_DIR=temp
AI_ANALYSIS_MAX_VIDEO_LENGTH=600
AI_MODELS_DIR=ai-analysis/models/weights
AI_PROCESSING_MAX_CONCURRENT=2
AI_PROCESSING_TIMEOUT=3600

# Storage Configuration
STORAGE_TYPE=local
STORAGE_LOCAL_DIR=storage

# S3 Configuration (if using S3 storage)
S3_BUCKET=your-s3-bucket
S3_REGION=your-s3-region
S3_ACCESS_KEY=your-s3-access-key
S3_SECRET_KEY=your-s3-secret-key

# Google Cloud Storage Configuration (if using GCS storage)
GCS_BUCKET=your-gcs-bucket
GCS_KEY_FILENAME=your-gcs-key-filename

# Social Authentication Configuration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_CALLBACK_URL=http://localhost:3000/api/auth/google/callback

FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret
FACEBOOK_CALLBACK_URL=http://localhost:3000/api/auth/facebook/callback

APPLE_CLIENT_ID=your-apple-client-id
APPLE_TEAM_ID=your-apple-team-id
APPLE_KEY_ID=your-apple-key-id
APPLE_PRIVATE_KEY=your-apple-private-key
APPLE_CALLBACK_URL=http://localhost:3000/api/auth/apple/callback

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=combined
LOG_DIR=logs
