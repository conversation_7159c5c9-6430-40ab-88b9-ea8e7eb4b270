/* Estilos generales */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: '<PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: #f0f2f5;
    color: #333;
}

.container {
    display: flex;
    min-height: 100vh;
    max-width: 1177px; /* تم إضافة السايدبار الأيمن (907px + 270px) */
    margin: 0 auto;
    background-color: #f0f2f5;
}

/* Barra lateral izquierda */
.sidebar-left {
    background-color: #f0f2f5;
    padding: 15px 0;
    position: sticky;
    top: 0;
    height: 100vh;
    width: 270px; /* Ajustado a 270px para mantener una buena proporción */
    overflow-y: auto;
}

.logo {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

.logo-link {
    text-decoration: none;
    cursor: pointer;
    transition: transform 0.2s ease;
    display: block;
}

.logo-link:hover {
    transform: scale(1.05);
}

.logo-container {
    width: 40px;
    height: 40px;
    background-color: #6a0dad; /* Cambiado a color púrpura */
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(106, 13, 173, 0.3); /* Sombra púrpura */
}

.sidebar-menu {
    padding: 0 10px; /* Reducido el padding horizontal para acercar más los elementos al borde */
    max-height: calc(100vh - 100px);
    width: 100%;
}

.menu-title {
    font-size: 12px;
    color: #666;
    margin: 20px 0 8px;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.sidebar-menu ul {
    list-style: none;
}

.sidebar-menu > ul > li {
    margin: 4px 0;
    padding: 8px 5px; /* Reducido significativamente el padding horizontal para acercar más al borde */
    border-radius: 10px;
    transition: all 0.3s;
}

.sidebar-menu li:hover {
    background-color: rgba(106, 13, 173, 0.05); /* Fondo púrpura muy suave al pasar el ratón */
}

/* Submenu de deportes */
.has-submenu {
    position: relative;
}

.submenu {
    display: none;
    list-style: none;
    padding-left: 0; /* Eliminado el padding izquierdo */
    margin-top: 5px;
    margin-bottom: 5px;
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 8px;
    overflow: visible; /* Cambiado a visible para evitar recortes */
    max-height: 0;
    transition: max-height 0.3s ease-in-out;
    width: 100%; /* Ancho completo */
    min-width: 260px; /* Ancho mínimo para asegurar que el texto completo sea visible */
    padding: 8px 0; /* Añadir padding vertical */
}

.has-submenu:hover .submenu {
    display: block;
    max-height: 400px; /* Aumentado para mostrar más contenido */
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.submenu li {
    margin: 0;
    padding: 8px 12px 8px 5px; /* Reducido significativamente el padding izquierdo para acercar más al borde */
    min-width: 240px; /* Ancho mínimo para asegurar que el texto completo sea visible */
    position: relative;
}

.submenu li:hover {
    background-color: rgba(106, 13, 173, 0.1);
    transform: translateX(5px);
}

.sidebar-menu a {
    text-decoration: none;
    color: #4a0080; /* Cambiado a un tono de púrpura más oscuro para mejor legibilidad */
    display: flex;
    align-items: center;
    font-size: 16px; /* Reducido de 17px a 16px */
    padding: 6px 0;
    white-space: nowrap; /* Evita que el texto se divida en varias líneas */
    overflow: hidden; /* Oculta el texto que excede el ancho */
    text-overflow: ellipsis; /* Muestra puntos suspensivos si el texto es demasiado largo */
    font-weight: 500; /* Ligeramente más grueso para mejor legibilidad con el nuevo color */
    transition: all 0.3s;
    position: relative;
}

.sidebar-menu a:hover {
    background-color: rgba(106, 13, 173, 0.1); /* Fondo púrpura muy suave */
    color: #6a0dad; /* Color púrpura para el texto */
    transform: translateX(2px);
}

.sidebar-menu i {
    margin-right: 10px; /* Reducido para mejor alineación */
    font-size: 19px; /* Reducido de 21px a 19px */
    width: 22px; /* Reducido para mejor alineación */
    text-align: center;
    color: #6a0dad; /* Cambiado a color púrpura moderno */
}

/* Ajustes específicos para el menú AI Analysis */
.ai-submenu {
    overflow: visible !important;
    width: 260px; /* Reducido de 300px a 260px para adaptarse al nuevo ancho del sidebar */
    padding: 8px 0; /* Añadir padding vertical */
}

.ai-submenu li {
    overflow: visible;
    padding: 12px 15px 12px 10px; /* Aumentar el padding vertical y horizontal */
    border-bottom: 1px solid rgba(0, 0, 0, 0.03); /* Añadir separador sutil */
}

.ai-submenu li:last-child {
    border-bottom: none; /* Eliminar borde del último elemento */
}

.ai-submenu a {
    font-size: 14px;
    padding: 4px 0;
    white-space: normal; /* Permitir que el texto se divida en varias líneas si es necesario */
    overflow: visible; /* Cambiado a visible para mostrar todo el texto */
    text-overflow: clip; /* Eliminado ellipsis */
    display: flex; /* Añadido display flex para mejor alineación */
    align-items: flex-start; /* Alinear al inicio para mejor visualización con múltiples líneas */
    width: 100%; /* Ancho completo */
    line-height: 1.4; /* Mejorar la legibilidad */
}

.ai-submenu a span {
    display: inline-block;
    white-space: normal; /* Permitir que el texto se divida en varias líneas si es necesario */
    overflow: visible;
    width: auto;
    padding-top: 2px; /* Pequeño ajuste para alinear mejor con el icono */
    font-weight: 500; /* Hacer el texto un poco más visible */
}

.ai-submenu i {
    font-size: 18px; /* Iconos ligeramente más grandes */
    margin-right: 12px; /* Más espacio entre el icono y el texto */
    margin-left: 0; /* Eliminar cualquier margen izquierdo */
    width: 20px; /* Ancho ajustado */
    text-align: center;
    display: inline-flex; /* Cambiado a inline-flex para mejor alineación */
    justify-content: center; /* Centrado horizontal */
    align-items: center; /* Centrado vertical */
}

/* Colores específicos para cada opción en el menú de AI Analysis */
.ai-submenu li:nth-child(1) i { color: #FF5722; } /* Video Analysis */
.ai-submenu li:nth-child(2) i { color: #4CAF50; } /* Performance Stats */
.ai-submenu li:nth-child(3) i { color: #2196F3; } /* Player Comparison */
.ai-submenu li:nth-child(4) i { color: #FFC107; } /* Training Recommendations */
.ai-submenu li:nth-child(5) i { color: #9C27B0; } /* Talent Benchmarks */

/* Colores específicos para cada deporte en el menú - Variaciones de púrpura */
.sports-submenu li:nth-child(1) i { color: #8e44ad; } /* Football - Púrpura medio */
.sports-submenu li:nth-child(2) i { color: #9b59b6; } /* Basketball - Púrpura claro */
.sports-submenu li:nth-child(3) i { color: #6a0dad; } /* Volleyball - Púrpura principal */
.sports-submenu li:nth-child(4) i { color: #5d3fd3; } /* Baseball - Púrpura azulado */
.sports-submenu li:nth-child(5) i { color: #7d3c98; } /* Cricket - Púrpura oscuro */
.sports-submenu li:nth-child(6) i { color: #a569bd; } /* Field Hockey - Púrpura rosado */

/* Insignia NEW */
.badge {
    display: inline-block;
    padding: 2px 6px;
    font-size: 10px;
    font-weight: bold;
    border-radius: 10px;
    margin-left: 5px;
}

.new-badge {
    background-color: #ff3366;
    color: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    animation: pulse 2s infinite;
    transition: opacity 0.5s ease;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Perfil de usuario en sidebar */
.user-profile-sidebar {
    display: flex;
    align-items: center;
    margin-top: 23px;
    padding: 12px 15px;
    border-radius: 10px;
    background-color: rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
}

.user-profile-sidebar:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.profile-pic-small {
    width: 46px;
    height: 46px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 12px;
    border: 2px solid #6a0dad; /* Cambiado a color púrpura */
}

.profile-pic-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-info-sidebar {
    flex: 1;
}

.profile-info-sidebar span:first-child {
    display: block;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.teams-clubs {
    font-size: 12px;
    color: #666;
    margin-top: 2px;
}

.profile-dropdown-toggle {
    margin-left: auto;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    border-radius: 50%;
    transition: all 0.2s;
    background-color: rgba(0, 0, 0, 0.05);
    cursor: pointer;
}

.profile-dropdown-toggle:hover {
    background-color: rgba(0, 0, 0, 0.1);
    color: #0066cc;
    transform: scale(1.1);
}

.profile-dropdown-toggle i {
    font-size: 20px;
    color: #444;
}

/* Menú desplegable del perfil */
.profile-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.2);
    width: 220px; /* Reducido de 230px a 220px para adaptarse al nuevo ancho del sidebar */
    z-index: 1000;
    overflow: hidden;
    display: none;
    border: 1px solid #eee;
    margin-top: 6px;
    max-height: 0;
    opacity: 0;
    transition: max-height 0.3s ease, opacity 0.3s ease;
    visibility: hidden;
    text-align: left;
}

.profile-dropdown-menu.show {
    display: block;
    max-height: 400px;
    opacity: 1;
    overflow-y: auto;
    z-index: 1001;
    visibility: visible;
}

.profile-dropdown-item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 14px 25px;
    text-decoration: none;
    color: #333;
    transition: all 0.2s ease;
    font-size: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.03);
    position: relative;
}

.profile-dropdown-item:last-child {
    border-bottom: none;
}

.profile-dropdown-item:hover {
    background-color: #f5f5f5;
    color: #6a0dad;
}

.profile-dropdown-item i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
    color: #6a0dad;
    font-size: 17px;
    display: inline-block;
}

.dropdown-divider {
    height: 1px;
    background-color: #e0e0e0;
    margin: 8px 0;
}

.logout-item {
    color: #e74c3c;
}

.logout-item:hover {
    background-color: rgba(231, 76, 60, 0.05);
    color: #e74c3c;
}

.logout-item i {
    color: #e74c3c;
}

/* Contenido central */
.main-content {
    flex: 1;
    padding: 20px;
    background-color: #f0f2f5;
    max-width: 637px; /* العرض الجديد بعد التقليل بنسبة 30% إجمالي (885px - 248px) */
    margin: 0 auto; /* توسيط المحتوى */
}

/* Área de creación de publicación */
.post-creation {
    background-color: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.user-info-layout {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
}

.profile-pic-container {
    margin-right: 15px;
}

.profile-pic-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid #6a0dad;
}

.profile-pic-circle .profile-pic-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-info-details {
    flex: 1;
}

.user-name-container {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 5px;
}

.user-name-container h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.eye-icon-container {
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 20px;
    background-color: rgba(46, 204, 113, 0.1);
    transition: all 0.3s ease;
    position: relative;
}

.eye-icon-container:hover {
    background-color: rgba(46, 204, 113, 0.2);
    transform: scale(1.05);
}

.eye-icon-container i {
    color: #2ecc71;
    font-size: 14px;
    animation: blink 3s infinite;
}

@keyframes blink {
    0%, 45%, 55%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
}

.watchers-count {
    font-size: 12px;
    font-weight: 600;
    color: #2ecc71;
}

.eye-tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    margin-bottom: 5px;
    z-index: 1000;
}

.eye-icon-container:hover .eye-tooltip {
    opacity: 1;
    visibility: visible;
}

.team-info {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 5px;
}

.team-logo {
    width: 20px;
    height: 20px;
    object-fit: contain;
}

.team-info span {
    font-size: 14px;
    font-weight: 500;
    color: #666;
}

.position {
    font-size: 13px;
    color: #999;
}

.simple-textarea {
    width: 100%;
    border: none;
    resize: none;
    font-size: 16px;
    padding: 15px 0;
    background: transparent;
    font-family: inherit;
    color: #333;
    outline: none;
    min-height: 60px;
}

.simple-textarea::placeholder {
    color: #999;
    font-weight: 400;
}

.post-actions-new {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

.post-icons {
    display: flex;
    gap: 10px;
}

.post-icon-btn {
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
    font-size: 18px;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.post-icon-btn:hover {
    background-color: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
    transform: scale(1.1);
}

.post-button {
    background-color: #6a0dad;
    color: white;
    border: none;
    padding: 10px 25px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.post-button:hover {
    background-color: #5a0b9a;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(106, 13, 173, 0.3);
}

/* Área de contenido */
.content-area {
    background-color: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.content-placeholder {
    text-align: center;
    color: #666;
}

.content-placeholder h2 {
    color: #333;
    margin-bottom: 15px;
    font-size: 24px;
    font-weight: 600;
}

.content-placeholder p {
    font-size: 16px;
    line-height: 1.6;
}

/* Botones flotantes */
.floating-buttons {
    position: fixed;
    bottom: 30px;
    right: 30px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    z-index: 1000;
}

.chat-button, .top-button {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.chat-button {
    background-color: #6a0dad;
}

.chat-button:hover {
    background-color: #5a0b9a;
    transform: scale(1.1);
}

.top-button {
    background-color: #2ecc71;
}

.top-button:hover {
    background-color: #27ae60;
    transform: scale(1.1);
}

/* Tooltips */
.js-tooltip {
    position: fixed;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px 15px;
    border-radius: 8px;
    font-size: 14px;
    max-width: 300px;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    pointer-events: none;
    line-height: 1.4;
}

.js-tooltip.visible {
    opacity: 1;
    visibility: visible;
}

.js-tooltip::before {
    content: '';
    position: absolute;
    top: 50%;
    left: -8px;
    transform: translateY(-50%);
    border: 8px solid transparent;
    border-right-color: var(--tooltip-color, rgba(0, 0, 0, 0.8));
}

/* Animaciones de notificación */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        flex-direction: column;
        max-width: 100%;
    }

    .sidebar-left {
        width: 100%;
        height: auto;
        position: relative;
        order: 2;
    }

    .main-content {
        width: 100%;
        max-width: 100%;
        order: 1;
        padding: 15px;
    }

    .floating-buttons {
        bottom: 20px;
        right: 20px;
    }

    .user-info-layout {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .profile-pic-container {
        margin-right: 0;
        margin-bottom: 15px;
    }
}

@media (max-width: 480px) {
    .post-actions-new {
        flex-direction: column;
        gap: 15px;
    }

    .post-icons {
        justify-content: center;
    }

    .floating-buttons {
        bottom: 15px;
        right: 15px;
    }

    .chat-button, .top-button {
        width: 45px;
        height: 45px;
        font-size: 16px;
    }
}

/* السايدبار الأيمن (من الواجهة الأصلية) */
.sidebar-right {
    width: 270px;
    padding: 20px 15px;
    background-color: #f0f2f5;
    position: sticky;
    top: 0;
    height: 100vh;
    overflow-y: auto;
}

/* شريط البحث */
.search-bar {
    position: relative;
    margin-bottom: 20px;
}

.search-bar i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    font-size: 16px;
}

.search-bar input {
    width: 100%;
    padding: 12px 15px 12px 40px;
    border: none;
    border-radius: 25px;
    background-color: white;
    font-size: 14px;
    outline: none;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.search-bar input:focus {
    box-shadow: 0 4px 12px rgba(106, 13, 173, 0.15);
    border: 2px solid rgba(106, 13, 173, 0.2);
}

.search-bar input::placeholder {
    color: #999;
}

/* قسم الفرص */
.opportunities-section {
    background-color: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.opportunities-section h3 {
    color: #333;
    font-size: 18px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.opportunities-section h3 i {
    color: #6a0dad;
    font-size: 16px;
}

.opportunities-subtitle {
    color: #666;
    font-size: 13px;
    margin-bottom: 15px;
}

.opportunity {
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.opportunity:last-child {
    border-bottom: none;
}

.opportunity:hover {
    background-color: rgba(106, 13, 173, 0.02);
    border-radius: 8px;
    padding: 15px 10px;
    margin: 0 -10px;
}

.opportunity-header {
    margin-bottom: 8px;
}

.sport-tag {
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.tag-football {
    background-color: rgba(76, 175, 80, 0.1);
    color: #4CAF50;
}

.tag-basketball {
    background-color: rgba(255, 152, 0, 0.1);
    color: #FF9800;
}

.tag-volleyball {
    background-color: rgba(33, 150, 243, 0.1);
    color: #2196F3;
}

.tag-baseball {
    background-color: rgba(156, 39, 176, 0.1);
    color: #9C27B0;
}

.tag-cricket {
    background-color: rgba(255, 193, 7, 0.1);
    color: #FFC107;
}

.opportunity h4 {
    color: #333;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
    line-height: 1.4;
}

.opportunity-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.opportunity-date,
.opportunity-time {
    color: #666;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.opportunity-meta {
    margin-bottom: 8px;
}

.spots-available {
    color: #4CAF50;
    font-size: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
}

.view-more-button {
    width: 100%;
    padding: 12px;
    background-color: #6a0dad;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.view-more-button:hover {
    background-color: #5a0b9a;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(106, 13, 173, 0.3);
}

/* زر Premium */
.premium-trial-button {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    border-radius: 15px;
    padding: 15px;
    margin-bottom: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 12px;
    box-shadow: 0 4px 15px rgba(255, 165, 0, 0.3);
}

.premium-trial-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 165, 0, 0.4);
}

.premium-badge {
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.premium-badge i {
    color: white;
    font-size: 18px;
}

.premium-text {
    flex: 1;
}

.premium-title {
    display: block;
    color: white;
    font-weight: 700;
    font-size: 16px;
}

.premium-subtitle {
    display: block;
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
}

.premium-arrow i {
    color: white;
    font-size: 14px;
}

/* قسم طلبات المتابعة */
.follow-requests-section {
    background-color: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.follow-requests-section h3 {
    color: #333;
    font-size: 18px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.follow-requests-section h3 i {
    color: #6a0dad;
    font-size: 16px;
}

.follow-requests-subtitle {
    color: #666;
    font-size: 13px;
    margin-bottom: 15px;
}

.follow-request {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.follow-request:last-of-type {
    border-bottom: none;
}

.request-user {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
}

.request-user-pic {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #6a0dad;
}

.request-user-info h4 {
    color: #333;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 2px;
}

.request-user-info p {
    color: #666;
    font-size: 12px;
}

.request-actions {
    display: flex;
    gap: 8px;
}

.accept-button,
.decline-button {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    transition: all 0.3s ease;
}

.accept-button {
    background-color: rgba(76, 175, 80, 0.1);
    color: #4CAF50;
}

.accept-button:hover {
    background-color: #4CAF50;
    color: white;
    transform: scale(1.1);
}

.decline-button {
    background-color: rgba(244, 67, 54, 0.1);
    color: #f44336;
}

.decline-button:hover {
    background-color: #f44336;
    color: white;
    transform: scale(1.1);
}

.view-all-button {
    width: 100%;
    padding: 12px;
    background-color: transparent;
    color: #6a0dad;
    border: 2px solid #6a0dad;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.view-all-button:hover {
    background-color: #6a0dad;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(106, 13, 173, 0.3);
}

.request-count {
    background-color: #6a0dad;
    color: white;
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 700;
}

.view-all-button:hover .request-count {
    background-color: white;
    color: #6a0dad;
}
    margin-bottom: 20px;
}

.search-bar i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    font-size: 16px;
}

.search-bar input {
    width: 100%;
    padding: 12px 15px 12px 40px;
    border: none;
    border-radius: 25px;
    background-color: white;
    font-size: 14px;
    outline: none;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.search-bar input:focus {
    box-shadow: 0 4px 12px rgba(106, 13, 173, 0.15);
    border: 2px solid rgba(106, 13, 173, 0.2);
}

.search-bar input::placeholder {
    color: #999;
}

/* قسم الفرص */
.opportunities-section {
    background-color: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.opportunities-section h3 {
    color: #333;
    font-size: 18px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.opportunities-section h3 i {
    color: #6a0dad;
    font-size: 16px;
}

.opportunities-subtitle {
    color: #666;
    font-size: 13px;
    margin-bottom: 15px;
}

.opportunity {
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.opportunity:last-child {
    border-bottom: none;
}

.opportunity:hover {
    background-color: rgba(106, 13, 173, 0.02);
    border-radius: 8px;
    padding: 15px 10px;
    margin: 0 -10px;
}

.opportunity-header {
    margin-bottom: 8px;
}

.sport-tag {
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.tag-football {
    background-color: rgba(76, 175, 80, 0.1);
    color: #4CAF50;
}

.tag-basketball {
    background-color: rgba(255, 152, 0, 0.1);
    color: #FF9800;
}

.tag-volleyball {
    background-color: rgba(33, 150, 243, 0.1);
    color: #2196F3;
}

.tag-baseball {
    background-color: rgba(156, 39, 176, 0.1);
    color: #9C27B0;
}

.tag-cricket {
    background-color: rgba(255, 193, 7, 0.1);
    color: #FFC107;
}

.opportunity h4 {
    color: #333;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
    line-height: 1.4;
}

.opportunity-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.opportunity-date,
.opportunity-time {
    color: #666;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.opportunity-meta {
    margin-bottom: 8px;
}

.spots-available {
    color: #4CAF50;
    font-size: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
}

.view-more-button {
    width: 100%;
    padding: 12px;
    background-color: #6a0dad;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.view-more-button:hover {
    background-color: #5a0b9a;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(106, 13, 173, 0.3);
}

/* زر Premium */
.premium-trial-button {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    border-radius: 15px;
    padding: 15px;
    margin-bottom: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 12px;
    box-shadow: 0 4px 15px rgba(255, 165, 0, 0.3);
}

.premium-trial-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 165, 0, 0.4);
}

.premium-badge {
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.premium-badge i {
    color: white;
    font-size: 18px;
}

.premium-text {
    flex: 1;
}

.premium-title {
    display: block;
    color: white;
    font-weight: 700;
    font-size: 16px;
}

.premium-subtitle {
    display: block;
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
}

.premium-arrow i {
    color: white;
    font-size: 14px;
}

/* قسم طلبات المتابعة */
.follow-requests-section {
    background-color: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.follow-requests-section h3 {
    color: #333;
    font-size: 18px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.follow-requests-section h3 i {
    color: #6a0dad;
    font-size: 16px;
}

.follow-requests-subtitle {
    color: #666;
    font-size: 13px;
    margin-bottom: 15px;
}

.follow-request {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.follow-request:last-of-type {
    border-bottom: none;
}

.request-user {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
}

.request-user-pic {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #6a0dad;
}

.request-user-info h4 {
    color: #333;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 2px;
}

.request-user-info p {
    color: #666;
    font-size: 12px;
}

.request-actions {
    display: flex;
    gap: 8px;
}

.accept-button,
.decline-button {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    transition: all 0.3s ease;
}

.accept-button {
    background-color: rgba(76, 175, 80, 0.1);
    color: #4CAF50;
}

.accept-button:hover {
    background-color: #4CAF50;
    color: white;
    transform: scale(1.1);
}

.decline-button {
    background-color: rgba(244, 67, 54, 0.1);
    color: #f44336;
}

.decline-button:hover {
    background-color: #f44336;
    color: white;
    transform: scale(1.1);
}

.view-all-button {
    width: 100%;
    padding: 12px;
    background-color: transparent;
    color: #6a0dad;
    border: 2px solid #6a0dad;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.view-all-button:hover {
    background-color: #6a0dad;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(106, 13, 173, 0.3);
}

.request-count {
    background-color: #6a0dad;
    color: white;
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 700;
}

.view-all-button:hover .request-count {
    background-color: white;
    color: #6a0dad;
}