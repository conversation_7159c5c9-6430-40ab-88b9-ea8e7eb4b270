// Este archivo contiene solo el código mínimo para mostrar el diseño
// Las funcionalidades se implementarán más adelante

// Cargar imágenes de ejemplo
document.addEventListener('DOMContentLoaded', function() {
    // Actualizar la imagen de perfil del usuario
    const profilePic = document.querySelector('.profile-pic');
    profilePic.src = 'https://via.placeholder.com/60/0066cc/ffffff?text=JD';

    // Actualizar el logo del equipo
    const teamLogo = document.querySelector('.team-logo');
    teamLogo.src = 'https://via.placeholder.com/20/ff6b00/ffffff?text=FCB';

    // Actualizar la imagen de la publicación
    const postImage = document.querySelector('.post-image');
    postImage.src = 'https://via.placeholder.com/600x300/ff6b00/ffffff?text=Sports+Post';
});
