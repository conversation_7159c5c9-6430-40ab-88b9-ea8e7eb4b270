<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Search - Telyz</title>
    <link rel="stylesheet" href="search.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Agregamos fuentes de Google -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Barra lateral izquierda -->
        <div class="sidebar-left">
            <div class="logo">
                <a href="../Home/index.html" class="logo-link">
                    <div class="logo-container">T</div>
                </a>
            </div>
            <div class="sidebar-menu">
                <ul>
                    <li class="has-submenu" id="aiMenuItem">
    <a href="#"><i class="fas fa-robot"></i> AI Analysis <span class="badge new-badge">NEW</span></a>
    <ul class="submenu ai-submenu" id="aiDropdownMenu">
        <li>
            <a href="../../ai-analysis/interface/index.html" data-description="Upload game footage for AI breakdown of techniques and plays" data-color="#FF5722">
                <i class="fas fa-video"></i> <span>Video Analysis</span>
            </a>
        </li>
        <li>
            <a href="#" data-description="View weekly stats of star players and compare with your performance" data-color="#4CAF50">
                <i class="fas fa-chart-line"></i> <span>Performance Stats</span>
            </a>
        </li>
        <li>
            <a href="#" data-description="Compare your metrics with other players in your position" data-color="#2196F3">
                <i class="fas fa-users"></i> <span>Player Comparison</span>
            </a>
        </li>
        <li>
            <a href="#" data-description="Get personalized training plans based on your performance data" data-color="#FFC107">
                <i class="fas fa-dumbbell"></i> <span>Training Recommendations</span>
            </a>
        </li>
        <li>
            <a href="#" data-description="See how you measure against professional standards in your sport" data-color="#9C27B0">
                <i class="fas fa-trophy"></i> <span>Talent Benchmarks</span>
            </a>
        </li>
    </ul>
</li>
                    <li><a href="search.html" class="active"><i class="fas fa-search"></i> Search</a></li>
                    <li><a href="#"><i class="fas fa-hashtag"></i> Explore</a></li>
                    <li><a href="#"><i class="fas fa-bullhorn"></i> Announcements</a></li>
                    <li><a href="../Messages/index.html"><i class="fas fa-envelope"></i> Messages</a></li>
                    <li class="has-submenu" id="sportsMenuItem">
                        <a href="#"><i class="fas fa-running"></i> Sports</a>
                        <ul class="submenu sports-submenu" id="sportsSubmenu">
                            <li><a href="#"><i class="fas fa-futbol"></i> Football</a></li>
                            <li><a href="#"><i class="fas fa-basketball-ball"></i> Basketball</a></li>
                            <li><a href="#"><i class="fas fa-volleyball-ball"></i> Volleyball</a></li>
                            <li><a href="#"><i class="fas fa-baseball-ball"></i> Baseball</a></li>
                            <li><a href="#"><i class="fas fa-table-tennis"></i> Cricket</a></li>
                            <li><a href="#"><i class="fas fa-hockey-puck"></i> Field Hockey</a></li>
                        </ul>
                    </li>
                    <li><a href="../Opportunities/opportunities.html"><i class="fas fa-briefcase"></i> Opportunities</a></li>
                    <li><a href="#"><i class="fas fa-trophy"></i> Achievements</a></li>
                    <li><a href="#"><i class="fas fa-dumbbell"></i> Training</a></li>
                </ul>
                <div class="user-profile-sidebar">
                    <div class="profile-pic-small">
                        <img src="https://via.placeholder.com/40" alt="Profile" class="profile-pic-img">
                    </div>
                    <div class="profile-info-sidebar">
                        <span>John Doe</span>
                        <span class="teams-clubs">Teams & Clubs</span>
                    </div>
                    <div class="profile-dropdown-toggle" id="profileDropdownToggle">
                        <i class="fas fa-ellipsis-v"></i>
                    </div>
                    <div class="profile-dropdown-menu" id="profileDropdownMenu">
                        <a href="../Profile/athletic_profile.html" class="profile-dropdown-item" data-tooltip="View your complete athletic profile and career history"><i class="fas fa-user"></i> View Athletic Profile</a>
                        <a href="#" class="profile-dropdown-item" data-tooltip="Edit your athletic profile information, skills and achievements"><i class="fas fa-cog"></i> Edit Athletic Profile</a>
                        <a href="#" class="profile-dropdown-item" data-tooltip="View and manage your sports achievements, trophies and awards"><i class="fas fa-medal"></i> Achievements</a>
                        <a href="../SportsRecommendations/sports_recommendations.html" class="profile-dropdown-item" data-tooltip="View and manage recommendations from coaches, scouts and sports experts"><i class="fas fa-star"></i> Expert Recommendations</a>
                        <a href="#" class="profile-dropdown-item" data-tooltip="Control your athletic profile privacy and who can view it"><i class="fas fa-shield-alt"></i> Privacy Settings</a>
                        <div class="dropdown-divider"></div>
                        <a href="#" class="profile-dropdown-item logout-item" data-tooltip="Sign out from your account"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </div>
                </div>

                <script>
                    // JavaScript para controlar el menú desplegable
                    document.addEventListener('DOMContentLoaded', function() {
                        const toggleButton = document.getElementById('profileDropdownToggle');
                        const dropdownMenu = document.getElementById('profileDropdownMenu');
                        const userProfile = document.querySelector('.user-profile-sidebar');

                        let isMenuOpen = false;

                        // Función para mostrar/ocultar el menú
                        toggleButton.addEventListener('click', function(e) {
                            e.stopPropagation();
                            isMenuOpen = !isMenuOpen;
                            dropdownMenu.classList.toggle('show', isMenuOpen);

                            // Asegurarse de que los iconos sean visibles
                            if (isMenuOpen) {
                                setTimeout(() => {
                                    const icons = dropdownMenu.querySelectorAll('i');
                                    icons.forEach(icon => {
                                        icon.style.display = 'inline-block';
                                    });
                                }, 50);
                            }
                        });

                        // Cerrar el menú al hacer clic fuera de él
                        document.addEventListener('click', function(e) {
                            if (!dropdownMenu.contains(e.target) && !toggleButton.contains(e.target) && !userProfile.contains(e.target)) {
                                isMenuOpen = false;
                                dropdownMenu.classList.remove('show');
                            }
                        });

                        // Evitar que el menú se cierre al hacer clic dentro de él
                        dropdownMenu.addEventListener('click', function(e) {
                            e.stopPropagation();
                        });

                        // Evitar que el menú se cierre al pasar el ratón sobre él
                        dropdownMenu.addEventListener('mouseenter', function() {
                            if (isMenuOpen) {
                                dropdownMenu.classList.add('show');
                            }
                        });

                        // Configurar tooltips para el menú del perfil
                        const profileTooltip = document.getElementById('profile-tooltip');
                        const profileMenuItems = dropdownMenu.querySelectorAll('.profile-dropdown-item');

                        profileMenuItems.forEach((item) => {
                            // Mostrar tooltip al pasar el ratón
                            item.addEventListener('mouseenter', function(event) {
                                const description = this.getAttribute('data-tooltip');
                                const rect = this.getBoundingClientRect();

                                // Configurar el tooltip
                                profileTooltip.textContent = description;
                                profileTooltip.style.backgroundColor = 'rgba(106, 13, 173, 0.9)';
                                profileTooltip.style.left = (rect.right + 15) + 'px';
                                profileTooltip.style.top = (rect.top + rect.height/2 - 20) + 'px';

                                // Configurar el color de la flecha
                                profileTooltip.style.setProperty('--tooltip-color', 'rgba(106, 13, 173, 0.9)');

                                // Mostrar el tooltip
                                profileTooltip.classList.add('visible');
                            });

                            // Ocultar tooltip al quitar el ratón
                            item.addEventListener('mouseleave', function() {
                                profileTooltip.classList.remove('visible');
                            });
                        });
                    });
                </script>
            </div>
        </div>

        <!-- Contenido central -->
        <div class="main-content">
            <!-- Search Header -->
            <div class="search-header">
                <div class="search-title">
                    <h1><i class="fas fa-search"></i> Advanced Search</h1>
                    <p>Discover talent, opportunities, and connections across the sports world</p>
                </div>
            </div>

            <!-- Search Bar Section -->
            <div class="search-bar-section">
                <div class="main-search-bar">
                    <div class="search-input-container">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" id="mainSearchInput" placeholder="Search for players, teams, coaches, opportunities..." class="search-input">
                        <button class="voice-search-btn" title="Voice Search">
                            <i class="fas fa-microphone"></i>
                        </button>
                    </div>
                    <button class="advanced-search-btn" id="advancedSearchBtn">
                        <i class="fas fa-sliders-h"></i> Advanced Filters
                    </button>
                </div>
            </div>

            <!-- Search Categories -->
            <div class="search-categories">
                <div class="category-tabs">
                    <button class="category-tab active" data-category="all">
                        <i class="fas fa-globe"></i> All
                    </button>
                    <button class="category-tab" data-category="players">
                        <i class="fas fa-user-friends"></i> Players
                    </button>
                    <button class="category-tab" data-category="teams">
                        <i class="fas fa-users"></i> Teams & Clubs
                    </button>
                    <button class="category-tab" data-category="coaches">
                        <i class="fas fa-chalkboard-teacher"></i> Coaches
                    </button>
                    <button class="category-tab" data-category="opportunities">
                        <i class="fas fa-briefcase"></i> Opportunities
                    </button>
                    <button class="category-tab" data-category="events">
                        <i class="fas fa-calendar-alt"></i> Events
                    </button>
                </div>
            </div>

            <!-- Advanced Filters Panel -->
            <div class="advanced-filters-panel" id="advancedFiltersPanel">
                <div class="filters-header">
                    <h3><i class="fas fa-filter"></i> Advanced Filters</h3>
                    <button class="close-filters-btn" id="closeFiltersBtn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="filters-content">
                    <div class="filter-group">
                        <label class="filter-label">Sport</label>
                        <div class="filter-options">
                            <select class="filter-select" id="sportFilter">
                                <option value="">All Sports</option>
                                <option value="football">Football</option>
                                <option value="basketball">Basketball</option>
                                <option value="volleyball">Volleyball</option>
                                <option value="baseball">Baseball</option>
                                <option value="cricket">Cricket</option>
                                <option value="hockey">Field Hockey</option>
                            </select>
                        </div>
                    </div>

                    <div class="filter-group">
                        <label class="filter-label">Location</label>
                        <div class="filter-options">
                            <input type="text" class="filter-input" id="locationFilter" placeholder="City, Country, or Region">
                            <div class="location-suggestions" id="locationSuggestions"></div>
                        </div>
                    </div>

                    <div class="filter-group">
                        <label class="filter-label">Age Range</label>
                        <div class="filter-options">
                            <div class="range-inputs">
                                <input type="number" class="age-input" id="minAge" placeholder="Min" min="10" max="50">
                                <span class="range-separator">to</span>
                                <input type="number" class="age-input" id="maxAge" placeholder="Max" min="10" max="50">
                            </div>
                        </div>
                    </div>

                    <div class="filter-group">
                        <label class="filter-label">Skill Level</label>
                        <div class="filter-options">
                            <div class="skill-levels">
                                <label class="skill-option">
                                    <input type="checkbox" value="amateur"> Amateur
                                </label>
                                <label class="skill-option">
                                    <input type="checkbox" value="semi-professional"> Semi-Professional
                                </label>
                                <label class="skill-option">
                                    <input type="checkbox" value="professional"> Professional
                                </label>
                                <label class="skill-option">
                                    <input type="checkbox" value="elite"> Elite
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="filter-group">
                        <label class="filter-label">Position</label>
                        <div class="filter-options">
                            <select class="filter-select" id="positionFilter">
                                <option value="">All Positions</option>
                                <option value="goalkeeper">Goalkeeper</option>
                                <option value="defender">Defender</option>
                                <option value="midfielder">Midfielder</option>
                                <option value="forward">Forward</option>
                                <option value="center">Center</option>
                                <option value="guard">Guard</option>
                                <option value="striker">Striker</option>
                            </select>
                        </div>
                    </div>

                    <div class="filter-actions">
                        <button class="clear-filters-btn" id="clearFiltersBtn">
                            <i class="fas fa-eraser"></i> Clear All
                        </button>
                        <button class="apply-filters-btn" id="applyFiltersBtn">
                            <i class="fas fa-check"></i> Apply Filters
                        </button>
                    </div>
                </div>
            </div>

            <!-- Search Results -->
            <div class="search-results">
                <div class="results-header">
                    <div class="results-info">
                        <span class="results-count">Showing <strong>1,247</strong> results for "<span id="searchTerm">all</span>"</span>
                        <div class="results-time">Search completed in 0.32 seconds</div>
                    </div>
                    <div class="results-controls">
                        <div class="sort-controls">
                            <label>Sort by:</label>
                            <select class="sort-select">
                                <option value="relevance">Relevance</option>
                                <option value="newest">Newest First</option>
                                <option value="rating">Highest Rated</option>
                                <option value="location">Location</option>
                            </select>
                        </div>
                        <div class="view-controls">
                            <button class="view-btn active" data-view="grid" title="Grid View">
                                <i class="fas fa-th"></i>
                            </button>
                            <button class="view-btn" data-view="list" title="List View">
                                <i class="fas fa-list"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Results Grid -->
                <div class="results-grid" id="resultsGrid">
                    <!-- Player Result Card -->
                    <div class="result-card player-card">
                        <div class="card-header">
                            <div class="result-type">
                                <i class="fas fa-user"></i> Player
                            </div>
                            <div class="card-actions">
                                <button class="action-btn save-btn" title="Save">
                                    <i class="far fa-bookmark"></i>
                                </button>
                                <button class="action-btn more-btn" title="More Options">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="profile-section">
                                <img src="https://i.pravatar.cc/80?img=1" alt="Player" class="profile-image">
                                <div class="profile-info">
                                    <h3 class="profile-name">David Rodriguez</h3>
                                    <div class="profile-position">Midfielder • Professional</div>
                                    <div class="profile-location">
                                        <i class="fas fa-map-marker-alt"></i> Barcelona, Spain
                                    </div>
                                </div>
                            </div>
                            <div class="stats-section">
                                <div class="stat-item">
                                    <span class="stat-label">Age</span>
                                    <span class="stat-value">24</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Rating</span>
                                    <span class="stat-value">8.5</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Matches</span>
                                    <span class="stat-value">127</span>
                                </div>
                            </div>
                            <div class="tags-section">
                                <span class="tag">Football</span>
                                <span class="tag">La Liga</span>
                                <span class="tag">Available</span>
                            </div>
                        </div>
                        <div class="card-footer">
                            <button class="primary-btn contact-btn">
                                <i class="fas fa-comment"></i> Contact
                            </button>
                            <button class="secondary-btn profile-btn">
                                <i class="fas fa-user"></i> View Profile
                            </button>
                        </div>
                    </div>

                    <!-- Team Result Card -->
                    <div class="result-card team-card">
                        <div class="card-header">
                            <div class="result-type">
                                <i class="fas fa-users"></i> Team
                            </div>
                            <div class="card-actions">
                                <button class="action-btn save-btn" title="Save">
                                    <i class="far fa-bookmark"></i>
                                </button>
                                <button class="action-btn more-btn" title="More Options">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="profile-section">
                                <img src="https://upload.wikimedia.org/wikipedia/en/4/47/FC_Barcelona_%28crest%29.svg" alt="Team" class="profile-image">
                                <div class="profile-info">
                                    <h3 class="profile-name">FC Barcelona Youth</h3>
                                    <div class="profile-position">Youth Academy • Semi-Professional</div>
                                    <div class="profile-location">
                                        <i class="fas fa-map-marker-alt"></i> Barcelona, Spain
                                    </div>
                                </div>
                            </div>
                            <div class="stats-section">
                                <div class="stat-item">
                                    <span class="stat-label">Founded</span>
                                    <span class="stat-value">1899</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Players</span>
                                    <span class="stat-value">32</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Wins</span>
                                    <span class="stat-value">78%</span>
                                </div>
                            </div>
                            <div class="tags-section">
                                <span class="tag">Football</span>
                                <span class="tag">Youth</span>
                                <span class="tag">Recruiting</span>
                            </div>
                        </div>
                        <div class="card-footer">
                            <button class="primary-btn contact-btn">
                                <i class="fas fa-handshake"></i> Apply
                            </button>
                            <button class="secondary-btn profile-btn">
                                <i class="fas fa-info-circle"></i> View Info
                            </button>
                        </div>
                    </div>

                    <!-- Coach Result Card -->
                    <div class="result-card coach-card">
                        <div class="card-header">
                            <div class="result-type">
                                <i class="fas fa-chalkboard-teacher"></i> Coach
                            </div>
                            <div class="card-actions">
                                <button class="action-btn save-btn" title="Save">
                                    <i class="far fa-bookmark"></i>
                                </button>
                                <button class="action-btn more-btn" title="More Options">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="profile-section">
                                <img src="https://i.pravatar.cc/80?img=5" alt="Coach" class="profile-image">
                                <div class="profile-info">
                                    <h3 class="profile-name">Maria Gonzalez</h3>
                                    <div class="profile-position">Youth Coach • UEFA Pro License</div>
                                    <div class="profile-location">
                                        <i class="fas fa-map-marker-alt"></i> Madrid, Spain
                                    </div>
                                </div>
                            </div>
                            <div class="stats-section">
                                <div class="stat-item">
                                    <span class="stat-label">Experience</span>
                                    <span class="stat-value">12 yrs</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Rating</span>
                                    <span class="stat-value">9.2</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Players</span>
                                    <span class="stat-value">156</span>
                                </div>
                            </div>
                            <div class="tags-section">
                                <span class="tag">Football</span>
                                <span class="tag">Youth Development</span>
                                <span class="tag">Available</span>
                            </div>
                        </div>
                        <div class="card-footer">
                            <button class="primary-btn contact-btn">
                                <i class="fas fa-envelope"></i> Contact
                            </button>
                            <button class="secondary-btn profile-btn">
                                <i class="fas fa-user"></i> View Profile
                            </button>
                        </div>
                    </div>

                    <!-- Opportunity Result Card -->
                    <div class="result-card opportunity-card">
                        <div class="card-header">
                            <div class="result-type">
                                <i class="fas fa-briefcase"></i> Opportunity
                            </div>
                            <div class="card-actions">
                                <button class="action-btn save-btn" title="Save">
                                    <i class="far fa-bookmark"></i>
                                </button>
                                <button class="action-btn more-btn" title="More Options">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="profile-section">
                                <img src="https://upload.wikimedia.org/wikipedia/en/7/7a/Manchester_United_FC_crest.svg" alt="Team" class="profile-image">
                                <div class="profile-info">
                                    <h3 class="profile-name">Youth Academy Trials</h3>
                                    <div class="profile-position">Manchester United FC</div>
                                    <div class="profile-location">
                                        <i class="fas fa-map-marker-alt"></i> Manchester, UK
                                    </div>
                                </div>
                            </div>
                            <div class="stats-section">
                                <div class="stat-item">
                                    <span class="stat-label">Age Range</span>
                                    <span class="stat-value">16-18</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Deadline</span>
                                    <span class="stat-value">30 days</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Positions</span>
                                    <span class="stat-value">All</span>
                                </div>
                            </div>
                            <div class="tags-section">
                                <span class="tag">Football</span>
                                <span class="tag">Youth</span>
                                <span class="tag">Premier League</span>
                            </div>
                        </div>
                        <div class="card-footer">
                            <button class="primary-btn contact-btn">
                                <i class="fas fa-paper-plane"></i> Apply Now
                            </button>
                            <button class="secondary-btn profile-btn">
                                <i class="fas fa-info-circle"></i> View Details
                            </button>
                        </div>
                    </div>

                    <!-- Event Result Card -->
                    <div class="result-card event-card">
                        <div class="card-header">
                            <div class="result-type">
                                <i class="fas fa-calendar-alt"></i> Event
                            </div>
                            <div class="card-actions">
                                <button class="action-btn save-btn" title="Save">
                                    <i class="far fa-bookmark"></i>
                                </button>
                                <button class="action-btn more-btn" title="More Options">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="profile-section">
                                <img src="https://images.unsplash.com/photo-1431324155629-1a6deb1dec8d?w=80&h=80&fit=crop&crop=center" alt="Event" class="profile-image">
                                <div class="profile-info">
                                    <h3 class="profile-name">International Youth Tournament</h3>
                                    <div class="profile-position">Annual Championship</div>
                                    <div class="profile-location">
                                        <i class="fas fa-map-marker-alt"></i> Valencia, Spain
                                    </div>
                                </div>
                            </div>
                            <div class="stats-section">
                                <div class="stat-item">
                                    <span class="stat-label">Date</span>
                                    <span class="stat-value">Aug 15</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Teams</span>
                                    <span class="stat-value">32</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Prize</span>
                                    <span class="stat-value">€50K</span>
                                </div>
                            </div>
                            <div class="tags-section">
                                <span class="tag">Football</span>
                                <span class="tag">Tournament</span>
                                <span class="tag">Youth</span>
                            </div>
                        </div>
                        <div class="card-footer">
                            <button class="primary-btn contact-btn">
                                <i class="fas fa-ticket-alt"></i> Register
                            </button>
                            <button class="secondary-btn profile-btn">
                                <i class="fas fa-info-circle"></i> View Details
                            </button>
                        </div>
                    </div>

                    <!-- Scout Result Card -->
                    <div class="result-card scout-card">
                        <div class="card-header">
                            <div class="result-type">
                                <i class="fas fa-binoculars"></i> Scout
                            </div>
                            <div class="card-actions">
                                <button class="action-btn save-btn" title="Save">
                                    <i class="far fa-bookmark"></i>
                                </button>
                                <button class="action-btn more-btn" title="More Options">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="profile-section">
                                <img src="https://i.pravatar.cc/80?img=8" alt="Scout" class="profile-image">
                                <div class="profile-info">
                                    <h3 class="profile-name">Roberto Silva</h3>
                                    <div class="profile-position">Talent Scout • Real Madrid</div>
                                    <div class="profile-location">
                                        <i class="fas fa-map-marker-alt"></i> Madrid, Spain
                                    </div>
                                </div>
                            </div>
                            <div class="stats-section">
                                <div class="stat-item">
                                    <span class="stat-label">Experience</span>
                                    <span class="stat-value">8 yrs</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Discoveries</span>
                                    <span class="stat-value">43</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Rating</span>
                                    <span class="stat-value">9.7</span>
                                </div>
                            </div>
                            <div class="tags-section">
                                <span class="tag">Football</span>
                                <span class="tag">La Liga</span>
                                <span class="tag">Youth Specialist</span>
                            </div>
                        </div>
                        <div class="card-footer">
                            <button class="primary-btn contact-btn">
                                <i class="fas fa-handshake"></i> Connect
                            </button>
                            <button class="secondary-btn profile-btn">
                                <i class="fas fa-user"></i> View Profile
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                <div class="pagination-section">
                    <div class="pagination">
                        <button class="page-btn disabled">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button class="page-btn active">1</button>
                        <button class="page-btn">2</button>
                        <button class="page-btn">3</button>
                        <button class="page-btn">4</button>
                        <button class="page-btn">5</button>
                        <span class="page-dots">...</span>
                        <button class="page-btn">127</button>
                        <button class="page-btn">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                    <div class="pagination-info">
                        Showing 1-12 of 1,247 results
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Botones flotantes -->
    <div class="floating-buttons">
        <button class="chat-button" title="Chat Support">
            <i class="fas fa-comment"></i>
        </button>
        <button class="top-button" title="Back to Top">
            <i class="fas fa-arrow-up"></i>
        </button>
    </div>

    <!-- Tooltips para los menús -->
    <div id="feature-tooltip" class="js-tooltip"></div>
    <div id="profile-tooltip" class="js-tooltip"></div>

    <!-- Script para la funcionalidad de búsqueda -->
    <script src="search.js"></script>
</body>
</html>