/* Card Component Styles */

.card {
  background-color: var(--card-color);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  transition: all var(--transition-normal);
  width: 100%;
}

/* Card Variants */

.card-default {
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
}

.card-outlined {
  border: 1px solid var(--border-color);
}

.card-elevated {
  box-shadow: var(--shadow-md);
}

/* Card Image */

.card-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-normal);
}

/* Card Content */

.card-content {
  padding: 20px;
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
  margin: 0 0 8px 0;
}

.card-subtitle {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-light);
  margin: 0 0 16px 0;
}

.card-text {
  font-size: var(--font-size-sm);
  color: var(--text-color);
  line-height: 1.5;
}

/* Card Footer */

.card-footer {
  padding: 16px 20px;
  border-top: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Clickable Card */

.card-clickable {
  cursor: pointer;
}

.card-clickable:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.card-clickable:hover .card-image img {
  transform: scale(1.05);
}

.card-clickable:active {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* Card Sizes */

.card-small {
  max-width: 300px;
}

.card-small .card-image {
  height: 150px;
}

.card-small .card-content {
  padding: 16px;
}

.card-small .card-title {
  font-size: var(--font-size-md);
}

.card-medium {
  max-width: 400px;
}

.card-large {
  max-width: 500px;
}

.card-large .card-image {
  height: 250px;
}

.card-large .card-content {
  padding: 24px;
}

.card-large .card-title {
  font-size: var(--font-size-xl);
}

/* Card with horizontal layout */

.card-horizontal {
  display: flex;
  flex-direction: row;
}

.card-horizontal .card-image {
  width: 150px;
  height: 100%;
  min-height: 150px;
}

.card-horizontal .card-content {
  flex: 1;
}

/* Card with badge */

.card-badge {
  position: relative;
}

.card-badge::after {
  content: attr(data-badge);
  position: absolute;
  top: 16px;
  right: 16px;
  background-color: var(--primary-color);
  color: white;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  padding: 4px 8px;
  border-radius: var(--border-radius-sm);
  z-index: 1;
}

/* Card with overlay */

.card-overlay {
  position: relative;
}

.card-overlay .card-image {
  height: 100%;
  min-height: 200px;
}

.card-overlay .card-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  color: white;
}

.card-overlay .card-title,
.card-overlay .card-subtitle,
.card-overlay .card-text {
  color: white;
}

/* Card Grid */

.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

/* Responsive */

@media (max-width: 768px) {
  .card-horizontal {
    flex-direction: column;
  }
  
  .card-horizontal .card-image {
    width: 100%;
    height: 200px;
  }
}
