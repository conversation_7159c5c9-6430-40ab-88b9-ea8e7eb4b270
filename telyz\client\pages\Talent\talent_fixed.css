/* General Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: '<PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: #f0f2f5;
    color: #333;
}

/* Container */
.container {
    display: flex;
    min-height: 100vh;
    max-width: 1200px;
    margin: 0 auto;
    background-color: #f0f2f5;
}


/* Main Content */
.main-content {
    flex: 1;
    padding: 20px;
    background: white;
    margin: 10px;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
}

/* Talents Header */
.talents-header {
    text-align: center;
    margin-bottom: 40px;
    padding: 20px 0;
}

.talents-header h1 {
    font-size: 36px;
    color: #333;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.talents-header h1 i {
    color: #6a0dad;
    font-size: 32px;
}

.talents-header .subtitle {
    font-size: 18px;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Tab Buttons */
.tab-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 30px;
}

.tab-btn {
    background: #f8f9fa;
    border: 2px solid #e0e0e0;
    border-radius: 15px;
    padding: 25px 30px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 200px;
    text-align: center;
}

.tab-btn:hover {
    border-color: #6a0dad;
    background: rgba(106, 13, 173, 0.05);
    transform: translateY(-3px);
}

.tab-btn.active {
    background: linear-gradient(135deg, #6a0dad, #8e44ad);
    color: white;
    border-color: #6a0dad;
    box-shadow: 0 6px 20px rgba(106, 13, 173, 0.3);
}

.tab-btn i {
    display: block;
    font-size: 28px;
    margin-bottom: 10px;
    color: #6a0dad;
}

.tab-btn.active i {
    color: white;
}

.tab-btn span {
    display: block;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 5px;
}

.tab-btn small {
    display: block;
    font-size: 14px;
    opacity: 0.8;
}

/* Search and Filters */
.search-filters-section {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 15px;
    margin-bottom: 40px;
}

.search-bar {
    display: flex;
    align-items: center;
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: 30px;
    padding: 15px 25px;
    margin-bottom: 25px;
    transition: border-color 0.3s;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.search-bar:focus-within {
    border-color: #6a0dad;
    box-shadow: 0 0 0 3px rgba(106, 13, 173, 0.1);
}

.search-bar i {
    color: #666;
    margin-right: 15px;
    font-size: 18px;
}

.search-bar input {
    flex: 1;
    border: none;
    outline: none;
    font-size: 16px;
    font-family: inherit;
}

.search-bar input::placeholder {
    color: #999;
}

.filter-controls {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.filter-select {
    padding: 12px 20px;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    background: white;
    font-size: 14px;
    color: #333;
    cursor: pointer;
    transition: border-color 0.3s;
    min-width: 160px;
}

.filter-select:focus {
    outline: none;
    border-color: #6a0dad;
}

/* Apply Filters Button */
.apply-filters-btn {
    padding: 12px 25px;
    background: linear-gradient(135deg, #6a0dad, #8e44ad);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 120px;
    justify-content: center;
}

.apply-filters-btn:hover {
    background: linear-gradient(135deg, #5a0a8a, #7a3d98);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(106, 13, 173, 0.3);
}

.apply-filters-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(106, 13, 173, 0.2);
}

/* Tab Content */
.talent-content-area {
    min-height: 500px;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.4s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.category-title {
    font-size: 28px;
    color: #333;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.category-title i {
    color: #6a0dad;
    font-size: 24px;
}

.category-description {
    color: #666;
    margin-bottom: 35px;
    font-size: 16px;
    line-height: 1.6;
}

/* Talents Grid */
.talents-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

/* Loading Placeholder */
.loading-placeholder {
    text-align: center;
    padding: 80px 20px;
    color: #666;
    grid-column: 1 / -1;
}

.loading-placeholder i {
    font-size: 40px;
    color: #6a0dad;
    margin-bottom: 20px;
}

.loading-placeholder p {
    font-size: 18px;
}

/* Talent Cards */
.talent-card {
    background: white;
    border-radius: 18px;
    overflow: hidden;
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
}

.talent-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

.talent-image {
    position: relative;
    height: 220px;
    overflow: hidden;
}

.talent-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s;
}

.talent-card:hover .talent-image img {
    transform: scale(1.05);
}

.talent-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: linear-gradient(135deg, #6a0dad, #8e44ad);
    color: white;
    padding: 8px 15px;
    border-radius: 18px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.talent-info {
    padding: 25px;
}

.talent-info h3 {
    font-size: 22px;
    font-weight: 700;
    color: #333;
    margin-bottom: 15px;
}

.talent-info p {
    color: #666;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 15px;
}

.talent-info p i {
    color: #6a0dad;
    width: 18px;
    font-size: 14px;
}

.talent-rating {
    display: flex;
    align-items: center;
    gap: 18px;
    margin: 25px 0;
    padding: 18px;
    background: #f8f9fa;
    border-radius: 12px;
}

.rating-circle {
    width: 55px;
    height: 55px;
    border-radius: 50%;
    background: linear-gradient(135deg, #6a0dad, #8e44ad);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 18px;
}

.rating-label {
    font-size: 15px;
    color: #666;
    font-weight: 500;
}

.talent-actions {
    display: flex;
    gap: 12px;
    margin-top: 25px;
}

.btn-primary,
.btn-secondary {
    flex: 1;
    padding: 14px 18px;
    border: none;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(135deg, #6a0dad, #8e44ad);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a0a8a, #7a3d98);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(106, 13, 173, 0.3);
}

.btn-secondary {
    background: #f8f9fa;
    color: #666;
    border: 2px solid #e0e0e0;
}

.btn-secondary:hover {
    background: #e9ecef;
    color: #333;
    border-color: #bbb;
}

/* Sidebar Right */
.sidebar-right {
    width: 270px; /* تقليل من 300px إلى 270px (تقليل 10%) */
    padding: 20px 15px;
    background-color: #f0f2f5;
    position: sticky;
    top: 0;
    height: 100vh;
    overflow-y: auto;
}

/* Widgets */
.widget {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid #f0f0f0;
}

.widget h3 {
    font-size: 20px;
    color: #333;
    margin-bottom: 20px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 12px;
}

.widget h3 i {
    color: #6a0dad;
    font-size: 18px;
}

/* Top Performers */
.top-performers-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.performer-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border-radius: 10px;
    transition: background-color 0.3s;
}

.performer-item:hover {
    background-color: #f8f9fa;
}

.performer-rank {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background: linear-gradient(135deg, #6a0dad, #8e44ad);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 13px;
    font-weight: 700;
}

.performer-item img {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #f0f0f0;
}

.performer-info {
    flex: 1;
}

.performer-info strong {
    display: block;
    font-size: 15px;
    color: #333;
    font-weight: 600;
}

.performer-info span {
    font-size: 13px;
    color: #666;
}

.performer-score {
    background: #e8f5e8;
    color: #2e7d32;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 13px;
    font-weight: 600;
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 18px;
}

.stat-item {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
    border: 1px solid #f0f0f0;
}

.stat-number {
    font-size: 26px;
    font-weight: 700;
    color: #6a0dad;
    margin-bottom: 8px;
}

.stat-label {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Activity List */
.activity-list {
    display: flex;
    flex-direction: column;
    gap: 18px;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 15px;
    border-radius: 10px;
    background: #f8f9fa;
}

.activity-item i {
    color: #6a0dad;
    font-size: 16px;
    margin-top: 3px;
    min-width: 18px;
}

.activity-content {
    flex: 1;
}

.activity-content strong {
    color: #333;
    font-weight: 600;
}

.activity-time {
    display: block;
    font-size: 12px;
    color: #999;
    margin-top: 5px;
}

/* Trending Sports */
.trending-sports {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.sport-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    border-radius: 10px;
    background: #f8f9fa;
    transition: background-color 0.3s;
}

.sport-item:hover {
    background: #e9ecef;
}

.sport-item i {
    color: #6a0dad;
    font-size: 18px;
    margin-right: 12px;
}

.sport-item span {
    flex: 1;
    font-weight: 500;
    color: #333;
    font-size: 15px;
}

.sport-trend {
    font-size: 13px;
    font-weight: 600;
    color: #2e7d32;
    background: #e8f5e8;
    padding: 6px 12px;
    border-radius: 15px;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .sidebar-right {
        width: 225px; /* تقليل من 250px إلى 225px للشاشات المتوسطة */
    }
    
    .sidebar-left {
        width: 220px;
    }
    
    .tab-buttons {
        gap: 15px;
    }
    
    .tab-btn {
        min-width: 160px;
        padding: 20px 25px;
    }
}

@media (max-width: 768px) {
    .container {
        flex-direction: column;
        max-width: 100%;
        margin: 0;
    }
    
    .sidebar-left,
    .sidebar-right {
        width: 100%;
        height: auto;
        position: static;
    }
    
    .main-content {
        margin: 5px;
        padding: 15px;
    }
    
    .tab-buttons {
        flex-direction: column;
        gap: 12px;
    }
    
    .tab-btn {
        min-width: 100%;
        padding: 18px;
    }
    
    .filter-controls {
        flex-direction: column;
        gap: 12px;
    }
    
    .filter-select {
        width: 100%;
    }
    
    .talents-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }
}

@media (max-width: 480px) {
    .talents-header h1 {
        font-size: 28px;
        flex-direction: column;
        gap: 10px;
    }
    
    .category-title {
        font-size: 24px;
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .talent-actions {
        flex-direction: column;
        gap: 10px;
    }
    
    .widget {
        padding: 20px;
        margin-bottom: 20px;
    }
}