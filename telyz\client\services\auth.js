/**
 * Authentication Service
 *
 * Service for handling user authentication.
 */

// Use the global api object in browser environment
const api = typeof window !== 'undefined' ? window.api : require('./api');

class AuthService {
  /**
   * Create an authentication service
   */
  constructor() {
    this.token = localStorage.getItem('token');
    this.refreshToken = localStorage.getItem('refreshToken');
    this.user = JSON.parse(localStorage.getItem('user') || 'null');

    // Bind methods
    this.login = this.login.bind(this);
    this.logout = this.logout.bind(this);
    this.register = this.register.bind(this);
    this.refreshAccessToken = this.refreshAccessToken.bind(this);
    this.getCurrentUser = this.getCurrentUser.bind(this);
    this.isAuthenticated = this.isAuthenticated.bind(this);
    this.hasRole = this.hasRole.bind(this);

    // Set up token refresh interval
    this.setupTokenRefresh();
  }

  /**
   * Set up token refresh interval
   */
  setupTokenRefresh() {
    // Clear any existing interval
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
    }

    // If we have a refresh token, set up interval to refresh access token
    if (this.refreshToken) {
      // Refresh token every 55 minutes (assuming 1 hour expiry)
      this.refreshInterval = setInterval(() => {
        this.refreshAccessToken();
      }, 55 * 60 * 1000);
    }
  }

  /**
   * Log in a user
   *
   * @param {string} email - User email
   * @param {string} password - User password
   * @returns {Promise} Promise with user data
   */
  async login(email, password) {
    try {
      const response = await api.post('/auth/login', { email, password });

      // Save tokens and user data
      this.token = response.token;
      this.refreshToken = response.refreshToken;
      this.user = response.user;

      localStorage.setItem('token', this.token);
      localStorage.setItem('refreshToken', this.refreshToken);
      localStorage.setItem('user', JSON.stringify(this.user));

      // Set up token refresh
      this.setupTokenRefresh();

      return this.user;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Log out the current user
   *
   * @returns {Promise} Promise that resolves when logout is complete
   */
  async logout() {
    try {
      // Call logout endpoint if authenticated
      if (this.token) {
        await api.post('/auth/logout');
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear tokens and user data
      this.token = null;
      this.refreshToken = null;
      this.user = null;

      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('user');

      // Clear token refresh interval
      if (this.refreshInterval) {
        clearInterval(this.refreshInterval);
        this.refreshInterval = null;
      }
    }
  }

  /**
   * Register a new user
   *
   * @param {Object} userData - User registration data
   * @returns {Promise} Promise with user data
   */
  async register(userData) {
    try {
      const response = await api.post('/users', userData);

      // Save token and user data
      this.token = response.token;
      this.user = response.user;

      localStorage.setItem('token', this.token);
      localStorage.setItem('user', JSON.stringify(this.user));

      return this.user;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Refresh the access token
   *
   * @returns {Promise} Promise that resolves when token is refreshed
   */
  async refreshAccessToken() {
    if (!this.refreshToken) {
      return;
    }

    try {
      const response = await api.post('/auth/refresh', { refreshToken: this.refreshToken });

      // Save new access token
      this.token = response.token;
      localStorage.setItem('token', this.token);

      return this.token;
    } catch (error) {
      console.error('Token refresh error:', error);

      // If refresh token is invalid, log out
      if (error.status === 401) {
        await this.logout();
      }
    }
  }

  /**
   * Get the current user
   *
   * @param {boolean} forceRefresh - Whether to force a refresh from the server
   * @returns {Promise} Promise with user data
   */
  async getCurrentUser(forceRefresh = false) {
    if (!this.token) {
      return null;
    }

    if (this.user && !forceRefresh) {
      return this.user;
    }

    try {
      const user = await api.get('/auth/me');

      // Update user data
      this.user = user;
      localStorage.setItem('user', JSON.stringify(this.user));

      return this.user;
    } catch (error) {
      console.error('Get current user error:', error);

      // If unauthorized, log out
      if (error.status === 401) {
        await this.logout();
      }

      return null;
    }
  }

  /**
   * Check if user is authenticated
   *
   * @returns {boolean} Whether user is authenticated
   */
  isAuthenticated() {
    return !!this.token;
  }

  /**
   * Check if user has a specific role
   *
   * @param {string|string[]} roles - Role or roles to check
   * @returns {boolean} Whether user has the role
   */
  hasRole(roles) {
    if (!this.user) {
      return false;
    }

    if (Array.isArray(roles)) {
      return roles.includes(this.user.role);
    }

    return this.user.role === roles;
  }
}

// Create authentication service instance
const auth = new AuthService();

// Export authentication service
if (typeof module !== 'undefined' && module.exports) {
  module.exports = auth;
} else {
  window.authService = auth;
}
