:root {
    --primary-color: #7b68ee;
    --primary-light: #9370db;
    --primary-dark: #5d43a6;
    --secondary-color: #4ecdc4;
    --accent-color: #ff7f50;
    --text-color: #2d3748;
    --text-light: #718096;
    --background-color: #f8f9fc;
    --white: #ffffff;
    --border-color: #e2e8f0;
    --success-color: #50c878;
    --warning-color: #ffaa33;
    --error-color: #ff6b6b;
    --shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: var(--background-color);
    color: var(--text-color);
    height: 100vh;
    overflow: hidden;
}

.messages-container {
    display: flex;
    height: 100vh;
    background-color: var(--white);
    border-radius: 20px;
    box-shadow: var(--shadow);
    overflow: hidden;
    margin: 20px;
    direction: rtl;
}

/* شريط المحادثات */
.conversations-sidebar {
    width: 320px;
    border-left: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    background-color: var(--white);
}

.search-filter {
    display: flex;
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--background-color);
}

.search-box {
    flex: 1;
    position: relative;
    margin-right: 10px;
}

.search-box i {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 15px;
    color: var(--text-light);
}

.search-box input {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 12px;
    background-color: var(--white);
    transition: var(--transition);
}

.search-box input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(123, 104, 238, 0.1);
    outline: none;
}

.filter-btn {
    width: 45px;
    height: 45px;
    border-radius: 12px;
    border: 1px solid var(--border-color);
    background-color: var(--white);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.filter-btn:hover {
    background-color: var(--background-color);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.conversations-list {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
}

.conversation {
    display: flex;
    padding: 15px;
    border-radius: 12px;
    cursor: pointer;
    transition: var(--transition);
    margin-bottom: 8px;
}

.conversation:hover {
    background-color: var(--background-color);
}

.conversation.active {
    background-color: rgba(123, 104, 238, 0.1);
}

.avatar {
    position: relative;
    margin-left: 15px;
}

.avatar img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
}

.online-status {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: var(--success-color);
    border: 2px solid var(--white);
}

.conversation-info {
    flex: 1;
}

.conversation-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
}

.conversation-header h3 {
    font-size: 16px;
    font-weight: 600;
}

.conversation-header .time {
    font-size: 12px;
    color: var(--text-light);
}

.last-message {
    font-size: 14px;
    color: var(--text-light);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.new-conversation-btn {
    padding: 15px;
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    margin: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.new-conversation-btn i {
    margin-left: 8px;
}

.new-conversation-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(123, 104, 238, 0.3);
}

/* منطقة المحادثة الرئيسية */
.chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    border-right: 1px solid var(--border-color);
    border-left: 1px solid var(--border-color);
}

.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
}

.contact-info {
    display: flex;
    align-items: center;
}

.contact-info > div {
    margin-right: 15px;
}

.contact-info h2 {
    font-size: 18px;
    margin-bottom: 3px;
}

.contact-info p {
    font-size: 14px;
    color: var(--success-color);
}

.chat-actions {
    display: flex;
}

.action-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 1px solid var(--border-color);
    background-color: transparent;
    margin-right: 10px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn:hover {
    background-color: var(--background-color);
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.messages-display {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background-color: rgba(248, 249, 252, 0.5);
}

.message {
    max-width: 70%;
    margin-bottom: 20px;
    display: flex;
}

.message.received {
    margin-right: auto;
}

.message.sent {
    margin-left: auto;
    justify-content: flex-end;
}

.message-content {
    padding: 12px 16px;
    border-radius: 18px;
    position: relative;
    max-width: 100%;
}

.message.received .message-content {
    background-color: var(--white);
    border: 1px solid var(--border-color);
    border-bottom-right-radius: 0;
}

.message.sent .message-content {
    background-color: var(--primary-color);
    color: var(--white);
    border-bottom-left-radius: 0;
}

.message-content .time {
    font-size: 12px;
    opacity: 0.7;
    margin-top: 5px;
    display: block;
    text-align: right;
}

.message-input-area {
    display: flex;
    padding: 15px;
    border-top: 1px solid var(--border-color);
    align-items: center;
}

.input-actions {
    display: flex;
    margin-left: 15px;
}

.message-input {
    flex: 1;
    margin: 0 15px;
}

.message-input textarea {
    width: 100%;
    padding: 12px 20px;
    border-radius: 25px;
    border: 1px solid var(--border-color);
    resize: none;
    height: 50px;
    font-size: 16px;
    transition: var(--transition);
}

.message-input textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(123, 104, 238, 0.1);
    outline: none;
}

.send-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.send-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(123, 104, 238, 0.3);
}

/* الشريط الجانبي للميزات */
.features-sidebar {
    width: 280px;
    background-color: var(--white);
    overflow-y: auto;
}

.groups-section, .notifications-section {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
}

.groups-section h3, .notifications-section h3 {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    font-size: 18px;
}

.groups-section h3 i, .notifications-section h3 i {
    margin-left: 10px;
    color: var(--primary-color);
}

.create-group-btn {
    width: 100%;
    padding: 12px;
    background-color: var(--background-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.create-group-btn i {
    margin-left: 8px;
}

.create-group-btn:hover {
    background-color: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.groups-list, .notifications-list {
    max-height: 300px;
    overflow-y: auto;
}

.notification {
    padding: 12px;
    border-radius: 12px;
    background-color: var(--background-color);
    margin-bottom: 10px;
    transition: var(--transition);
    cursor: pointer;
}

.notification:hover {
    background-color: rgba(123, 104, 238, 0.1);
}

.notification p {
    font-size: 14px;
    margin-bottom: 5px;
}

.notification .time {
    font-size: 12px;
    color: var(--text-light);
}

/* التصميم المتجاوب */
@media (max-width: 1200px) {
    .features-sidebar {
        width: 240px;
    }
}

@media (max-width: 992px) {
    .features-sidebar {
        display: none;
    }
    
    .conversations-sidebar {
        width: 280px;
    }
}

@media (max-width: 768px) {
    .messages-container {
        flex-direction: column;
        margin: 10px;
    }
    
    .conversations-sidebar, .chat-area {
        width: 100%;
    }
    
    .conversations-sidebar {
        border-left: none;
        border-bottom: 1px solid var(--border-color);
        height: 40vh;
    }
    
    .chat-area {
        height: 60vh;
    }
}