/**
 * AI Analysis Routes
 * 
 * API routes for AI analysis management and LLM-powered coaching.
 */

const express = require('express');
const router = express.Router();
const aiAnalysisController = require('../controllers/aiAnalysisController');
const aiCoachController = require('../controllers/aiCoachController');
const authMiddleware = require('../middlewares/authMiddleware');
const { validateAnalysisRequest, validateChatRequest } = require('../validators/aiAnalysisValidator');
const multer = require('multer');
const config = require('../../config');

// Configure multer for video uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, config.upload.directory);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
    cb(null, file.fieldname + '-' + uniqueSuffix + '.' + file.originalname.split('.').pop());
  },
});

const fileFilter = (req, file, cb) => {
  if (config.upload.allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only video files are allowed.'), false);
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: config.upload.maxSize,
  },
});

/**
 * @route   POST /api/ai-analysis/upload
 * @desc    Upload video for analysis
 * @access  Private
 */
router.post(
  '/upload',
  authMiddleware.authenticate,
  upload.single('video'),
  validateAnalysisRequest,
  aiAnalysisController.uploadVideo
);

/**
 * @route   GET /api/ai-analysis
 * @desc    Get all analyses for current user
 * @access  Private
 */
router.get(
  '/',
  authMiddleware.authenticate,
  aiAnalysisController.getUserAnalyses
);

/**
 * @route   GET /api/ai-analysis/:id
 * @desc    Get analysis by ID
 * @access  Private
 */
router.get(
  '/:id',
  authMiddleware.authenticate,
  aiAnalysisController.getAnalysisById
);

/**
 * @route   GET /api/ai-analysis/:id/status
 * @desc    Get analysis status
 * @access  Private
 */
router.get(
  '/:id/status',
  authMiddleware.authenticate,
  aiAnalysisController.getAnalysisStatus
);

/**
 * @route   GET /api/ai-analysis/:id/report
 * @desc    Get analysis report
 * @access  Private
 */
router.get(
  '/:id/report',
  authMiddleware.authenticate,
  aiAnalysisController.getAnalysisReport
);

/**
 * @route   GET /api/ai-analysis/:id/video
 * @desc    Get analyzed video
 * @access  Private
 */
router.get(
  '/:id/video',
  authMiddleware.authenticate,
  aiAnalysisController.getAnalyzedVideo
);

/**
 * @route   DELETE /api/ai-analysis/:id
 * @desc    Delete analysis
 * @access  Private
 */
router.delete(
  '/:id',
  authMiddleware.authenticate,
  aiAnalysisController.deleteAnalysis
);

/**
 * @route   POST /api/ai-analysis/:id/share
 * @desc    Share analysis with other users
 * @access  Private
 */
router.post(
  '/:id/share',
  authMiddleware.authenticate,
  aiAnalysisController.shareAnalysis
);

/**
 * @route   PUT /api/ai-analysis/:id/visibility
 * @desc    Update analysis visibility
 * @access  Private
 */
router.put(
  '/:id/visibility',
  authMiddleware.authenticate,
  aiAnalysisController.updateAnalysisVisibility
);

/**
 * @route   GET /api/ai-analysis/public
 * @desc    Get public analyses
 * @access  Public
 */
router.get(
  '/public',
  aiAnalysisController.getPublicAnalyses
);

/**
 * @route   GET /api/ai-analysis/shared
 * @desc    Get analyses shared with current user
 * @access  Private
 */
router.get(
  '/shared',
  authMiddleware.authenticate,
  aiAnalysisController.getSharedAnalyses
);

/**
 * @route   POST /api/ai-analysis/chat
 * @desc    Chat with AI coach
 * @access  Private
 */
router.post(
  '/chat',
  authMiddleware.authenticate,
  validateChatRequest,
  aiCoachController.chat
);

/**
 * @route   GET /api/ai-analysis/chat/history
 * @desc    Get chat history
 * @access  Private
 */
router.get(
  '/chat/history',
  authMiddleware.authenticate,
  aiCoachController.getChatHistory
);

/**
 * @route   DELETE /api/ai-analysis/chat/history
 * @desc    Clear chat history
 * @access  Private
 */
router.delete(
  '/chat/history',
  authMiddleware.authenticate,
  aiCoachController.clearChatHistory
);

/**
 * @route   GET /api/ai-analysis/coach/status
 * @desc    Get AI coach status
 * @access  Public
 */
router.get(
  '/coach/status',
  aiCoachController.getStatus
);

module.exports = router;
