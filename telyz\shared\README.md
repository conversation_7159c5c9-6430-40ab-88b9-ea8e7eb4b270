# Shared Module

This module contains shared code, types, and utilities that are used across different parts of the Telyz platform. It helps maintain consistency and avoid code duplication between the client, server, and AI analysis modules.

## Directory Structure

```
shared/
├── types/       # TypeScript type definitions
├── constants/   # Shared constants and enumerations
└── utils/       # Utility functions used across modules
```

## Types

The `types` directory contains TypeScript type definitions for:

- **User**: User profile and authentication types
- **Athlete**: Athlete-specific data structures
- **Sports**: Sport-specific data structures
- **Opportunities**: Job and opportunity types
- **AIAnalysis**: Analysis result types
- **API**: Request and response types for API endpoints

## Constants

The `constants` directory contains shared constants such as:

- **SportTypes**: Enumeration of supported sports
- **PositionTypes**: Player positions for each sport
- **OpportunityTypes**: Types of opportunities
- **UserRoles**: User role definitions
- **ErrorCodes**: Standardized error codes
- **Config**: Shared configuration values

## Utilities

The `utils` directory contains utility functions for:

- **Validation**: Input validation functions
- **Formatting**: Data formatting utilities
- **Conversion**: Type conversion utilities
- **Date**: Date manipulation functions
- **String**: String manipulation functions
- **Object**: Object manipulation functions
- **Array**: Array manipulation functions

## Usage

### In Client Code

```javascript
import { SportTypes } from 'shared/constants';
import { formatDate } from 'shared/utils';
import type { UserProfile } from 'shared/types';

const sport = SportTypes.FOOTBALL;
const formattedDate = formatDate(new Date(), 'YYYY-MM-DD');
```

### In Server Code

```javascript
const { SportTypes } = require('../shared/constants');
const { validateEmail } = require('../shared/utils');
```

### In AI Analysis Code

```javascript
const { PositionTypes } = require('../shared/constants');
const { calculateDistance } = require('../shared/utils');
```

## Development

When adding new shared code, consider:

1. **Placement**: Is this truly shared across modules?
2. **Dependencies**: Minimize dependencies in shared code
3. **Testing**: Ensure shared code is well-tested
4. **Documentation**: Document all shared code thoroughly

## Contribution Guidelines

1. Keep shared code minimal and focused
2. Avoid module-specific logic in shared code
3. Use clear, descriptive names
4. Add appropriate JSDoc comments
5. Write unit tests for all shared code
