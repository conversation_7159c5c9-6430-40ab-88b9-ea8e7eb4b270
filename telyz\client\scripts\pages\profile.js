// JavaScript for the Athletic Profile page

document.addEventListener('DOMContentLoaded', function() {
    initializeProfilePage();
});

function initializeProfilePage() {
    initializeProfileNavigation();
    initializeProfileActions();
    initializeWatchingEye();
    initializeProfileDropdown();
    initializeTooltips();
}

// Initialize profile navigation tabs
function initializeProfileNavigation() {
    const navTabs = document.querySelectorAll('.profile-nav-tabs li a');
    const profileSections = document.querySelectorAll('.profile-section');

    navTabs.forEach(tab => {
        tab.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetSection = this.getAttribute('href').substring(1);
            
            // Remove active class from all tabs and sections
            navTabs.forEach(t => t.parentElement.classList.remove('active'));
            profileSections.forEach(s => s.classList.remove('active'));
            
            // Add active class to clicked tab
            this.parentElement.classList.add('active');
            
            // Show target section
            const targetElement = document.getElementById(targetSection + '-section');
            if (targetElement) {
                targetElement.classList.add('active');
            }
        });
    });
}

// Initialize profile action buttons
function initializeProfileActions() {
    // Share Profile button
    const shareBtn = document.querySelector('.profile-action-btn:has(i.fa-share-alt)');
    if (shareBtn) {
        shareBtn.addEventListener('click', function() {
            handleShareProfile();
        });
    }

    // Export Resume button
    const exportBtn = document.querySelector('.profile-action-btn:has(i.fa-download)');
    if (exportBtn) {
        exportBtn.addEventListener('click', function() {
            handleExportResume();
        });
    }

    // Edit Profile button
    const editBtn = document.querySelector('.profile-action-btn.primary');
    if (editBtn) {
        editBtn.addEventListener('click', function() {
            handleEditProfile();
        });
    }
}

// Initialize watching eye functionality
function initializeWatchingEye() {
    const eyeIcon = document.getElementById('watchingEyeIcon');
    if (eyeIcon) {
        eyeIcon.addEventListener('click', function() {
            showWatchersModal();
        });
    }
}

// Initialize profile dropdown
function initializeProfileDropdown() {
    const toggleButton = document.getElementById('profileDropdownToggle');
    const dropdownMenu = document.getElementById('profileDropdownMenu');
    if (!toggleButton || !dropdownMenu) return;
    
    let isMenuOpen = false;
    
    toggleButton.addEventListener('click', function(e) {
        e.stopPropagation();
        isMenuOpen = !isMenuOpen;
        dropdownMenu.classList.toggle('show', isMenuOpen);
        
        // Ensure icons are visible
        if (isMenuOpen) {
            setTimeout(() => {
                const icons = dropdownMenu.querySelectorAll('i');
                icons.forEach(icon => {
                    icon.style.display = 'inline-block';
                });
            }, 50);
        }
    });
    
    // Close dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!toggleButton.contains(e.target) && !dropdownMenu.contains(e.target)) {
            isMenuOpen = false;
            dropdownMenu.classList.remove('show');
        }
    });
}

// Initialize tooltips
function initializeTooltips() {
    const tooltipElements = document.querySelectorAll('[data-tooltip]');
    
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', function(e) {
            showTooltip(e, this.getAttribute('data-tooltip'));
        });
        
        element.addEventListener('mouseleave', function() {
            hideTooltip();
        });
    });
}

// Handle share profile
function handleShareProfile() {
    if (navigator.share) {
        navigator.share({
            title: 'John Doe - Athletic Profile',
            text: 'Check out my athletic profile on Telyz',
            url: window.location.href
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(window.location.href).then(() => {
            showSuccessMessage('Profile link copied to clipboard!');
        });
    }
}

// Handle export resume
function handleExportResume() {
    // Create a modal for export options
    const modal = document.createElement('div');
    modal.className = 'export-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h2>Export Athletic Resume</h2>
                <button class="close-modal-btn"><i class="fas fa-times"></i></button>
            </div>
            <div class="modal-body">
                <div class="export-options">
                    <button class="export-option-btn" data-format="pdf">
                        <i class="fas fa-file-pdf"></i>
                        <span>Export as PDF</span>
                    </button>
                    <button class="export-option-btn" data-format="word">
                        <i class="fas fa-file-word"></i>
                        <span>Export as Word Document</span>
                    </button>
                    <button class="export-option-btn" data-format="json">
                        <i class="fas fa-file-code"></i>
                        <span>Export as JSON</span>
                    </button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Close modal functionality
    const closeBtn = modal.querySelector('.close-modal-btn');
    closeBtn.addEventListener('click', () => {
        document.body.removeChild(modal);
    });
    
    // Export options
    const exportOptions = modal.querySelectorAll('.export-option-btn');
    exportOptions.forEach(btn => {
        btn.addEventListener('click', () => {
            const format = btn.getAttribute('data-format');
            exportProfile(format);
            document.body.removeChild(modal);
        });
    });
}

// Handle edit profile
function handleEditProfile() {
    showInfoMessage('Edit profile feature will be implemented soon');
}

// Show watchers modal
function showWatchersModal() {
    const modal = document.createElement('div');
    modal.className = 'watchers-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h2>Who's Watching Your Profile</h2>
                <button class="close-modal-btn"><i class="fas fa-times"></i></button>
            </div>
            <div class="modal-body">
                <div class="watchers-list">
                    <div class="watcher-item">
                        <img src="https://i.pravatar.cc/40?img=20" alt="Scout">
                        <div class="watcher-info">
                            <h4>Carlos Rodriguez</h4>
                            <span>Scout - Real Madrid CF</span>
                        </div>
                        <span class="watching-since">Watching since 2 weeks ago</span>
                    </div>
                    <div class="watcher-item">
                        <img src="https://i.pravatar.cc/40?img=21" alt="Scout">
                        <div class="watcher-info">
                            <h4>Marco Silva</h4>
                            <span>Talent Scout - AC Milan</span>
                        </div>
                        <span class="watching-since">Watching since 1 month ago</span>
                    </div>
                    <div class="watcher-item">
                        <img src="https://i.pravatar.cc/40?img=22" alt="Coach">
                        <div class="watcher-info">
                            <h4>Thomas Mueller</h4>
                            <span>Youth Coach - Bayern Munich</span>
                        </div>
                        <span class="watching-since">Watching since 3 days ago</span>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Close modal functionality
    const closeBtn = modal.querySelector('.close-modal-btn');
    closeBtn.addEventListener('click', () => {
        document.body.removeChild(modal);
    });
}

// Export profile function
function exportProfile(format) {
    showInfoMessage(`Exporting profile as ${format.toUpperCase()}...`);
    
    // Simulate export process
    setTimeout(() => {
        showSuccessMessage(`Profile exported successfully as ${format.toUpperCase()}!`);
    }, 2000);
}

// Show tooltip
function showTooltip(event, text) {
    const tooltip = document.createElement('div');
    tooltip.className = 'js-tooltip visible';
    tooltip.textContent = text;
    
    document.body.appendChild(tooltip);
    
    // Position tooltip
    const rect = event.target.getBoundingClientRect();
    tooltip.style.left = (rect.right + 10) + 'px';
    tooltip.style.top = (rect.top + rect.height / 2 - tooltip.offsetHeight / 2) + 'px';
}

// Hide tooltip
function hideTooltip() {
    const tooltip = document.querySelector('.js-tooltip');
    if (tooltip) {
        document.body.removeChild(tooltip);
    }
}

// Utility functions for messages
function showSuccessMessage(message) {
    showMessage(message, 'success');
}

function showInfoMessage(message) {
    showMessage(message, 'info');
}

function showMessage(message, type) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.textContent = message;
    
    Object.assign(messageDiv.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        padding: '12px 20px',
        borderRadius: '6px',
        color: 'white',
        backgroundColor: type === 'success' ? '#4caf50' : '#2196f3',
        zIndex: '10000',
        animation: 'slideIn 0.3s ease-out'
    });
    
    document.body.appendChild(messageDiv);
    
    setTimeout(() => {
        messageDiv.style.animation = 'slideOut 0.3s ease-in';
        setTimeout(() => {
            if (document.body.contains(messageDiv)) {
                document.body.removeChild(messageDiv);
            }
        }, 300);
    }, 3000);
}
