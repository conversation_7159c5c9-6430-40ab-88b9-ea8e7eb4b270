<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Help & Support - Telez</title>
    <link rel="stylesheet" href="support.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Barra lateral izquierda -->
        <div class="sidebar-left">
            <div class="logo">
                <a href="../Home/index.html" class="logo-link">
                    <div class="logo-container">T</div>
                </a>
            </div>
            <div class="sidebar-menu">
                <ul>
                    <li class="has-submenu" id="aiMenuItem">
    <a href="#"><i class="fas fa-robot"></i> AI Analysis <span class="badge new-badge">NEW</span></a>
    <ul class="submenu ai-submenu" id="aiDropdownMenu">
        <li>
            <a href="../AiAnalysisSystem/ai_analysis_system.html" data-description="Upload game footage for AI breakdown of techniques and plays" data-color="#FF5722">
                <i class="fas fa-video"></i> <span>Video Analysis</span>
            </a>
        </li>
        <li>
            <a href="#" data-description="View weekly stats of star players and compare with your performance" data-color="#4CAF50">
                <i class="fas fa-chart-line"></i> <span>Performance Stats</span>
            </a>
        </li>
        <li>
            <a href="#" data-description="Compare your metrics with other players in your position" data-color="#2196F3">
                <i class="fas fa-users"></i> <span>Player Comparison</span>
            </a>
        </li>
        <li>
            <a href="#" data-description="Get personalized training plans based on your performance data" data-color="#FFC107">
                <i class="fas fa-dumbbell"></i> <span>Training Recommendations</span>
            </a>
        </li>
        <li>
            <a href="#" data-description="See how you measure against professional standards in your sport" data-color="#9C27B0">
                <i class="fas fa-trophy"></i> <span>Talent Benchmarks</span>
            </a>
        </li>
    </ul>
</li>
                    <li><a href="#"><i class="fas fa-hashtag"></i> Explore</a></li>
                    <li><a href="#"><i class="fas fa-bullhorn"></i> Announcements</a></li>
                    <li><a href="../Messages/index.html"><i class="fas fa-envelope"></i> Messages</a></li>
                    <li class="has-submenu" id="sportsMenuItem">
                        <a href="#"><i class="fas fa-running"></i> Sports</a>
                        <ul class="submenu sports-submenu" id="sportsSubmenu">
                            <li><a href="#"><i class="fas fa-futbol"></i> Football</a></li>
                            <li><a href="#"><i class="fas fa-basketball-ball"></i> Basketball</a></li>
                            <li><a href="#"><i class="fas fa-volleyball-ball"></i> Volleyball</a></li>
                            <li><a href="#"><i class="fas fa-baseball-ball"></i> Baseball</a></li>
                            <li><a href="#"><i class="fas fa-table-tennis"></i> Cricket</a></li>
                            <li><a href="#"><i class="fas fa-hockey-puck"></i> Field Hockey</a></li>
                        </ul>
                    </li>
                    <li><a href="../Opportunities/opportunities.html"><i class="fas fa-briefcase"></i> Opportunities</a></li>
                    <li><a href="#"><i class="fas fa-trophy"></i> Achievements</a></li>
                    <li><a href="#"><i class="fas fa-dumbbell"></i> Training</a></li>
                </ul>
                <div class="user-profile-sidebar">
                    <div class="profile-pic-small">
                        <img src="https://via.placeholder.com/40" alt="Profile" class="profile-pic-img">
                    </div>
                    <div class="profile-info-sidebar">
                        <span>John Doe</span>
                        <span class="teams-clubs">Teams & Clubs</span>
                    </div>
                    <div class="profile-dropdown-toggle" id="profileDropdownToggle">
                        <i class="fas fa-ellipsis-v"></i>
                    </div>
                    <div class="profile-dropdown-menu" id="profileDropdownMenu">
                        <a href="../Profile/athletic_profile.html" class="profile-dropdown-item" data-tooltip="View your complete athletic profile and career history"><i class="fas fa-user"></i> View Athletic Profile</a>
                        <a href="#" class="profile-dropdown-item" data-tooltip="Edit your athletic profile information, skills and achievements"><i class="fas fa-cog"></i> Edit Athletic Profile</a>
                        <a href="#" class="profile-dropdown-item" data-tooltip="View and manage your sports achievements, trophies and awards"><i class="fas fa-medal"></i> Achievements</a>
                        <a href="../SportsRecommendations/sports_recommendations.html" class="profile-dropdown-item" data-tooltip="View and manage recommendations from coaches, scouts and sports experts"><i class="fas fa-star"></i> Expert Recommendations</a>
                        <a href="#" class="profile-dropdown-item" data-tooltip="Control your athletic profile privacy and who can view it"><i class="fas fa-shield-alt"></i> Privacy Settings</a>
                        <div class="dropdown-divider"></div>
                        <a href="#" class="profile-dropdown-item logout-item" data-tooltip="Sign out from your account"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </div>
                </div>

                <script>
                    // JavaScript para controlar el menú desplegable
                    document.addEventListener('DOMContentLoaded', function() {
                        const toggleButton = document.getElementById('profileDropdownToggle');
                        const dropdownMenu = document.getElementById('profileDropdownMenu');
                        const userProfile = document.querySelector('.user-profile-sidebar');

                        let isMenuOpen = false;

                        // Función para mostrar/ocultar el menú
                        toggleButton.addEventListener('click', function(e) {
                            e.stopPropagation();
                            isMenuOpen = !isMenuOpen;
                            dropdownMenu.classList.toggle('show', isMenuOpen);

                            // Asegurarse de que los iconos sean visibles
                            if (isMenuOpen) {
                                setTimeout(() => {
                                    const icons = dropdownMenu.querySelectorAll('i');
                                    icons.forEach(icon => {
                                        icon.style.display = 'inline-block';
                                    });
                                }, 50);
                            }
                        });

                        // Cerrar el menú al hacer clic fuera de él
                        document.addEventListener('click', function(event) {
                            if (!userProfile.contains(event.target)) {
                                isMenuOpen = false;
                                dropdownMenu.classList.remove('show');
                            }
                        });

                        // Configurar tooltips para las opciones del menú AI Analysis
                        const featureTooltip = document.getElementById('feature-tooltip');
                        const featureMenuItems = document.querySelectorAll('#aiDropdownMenu a');

                        featureMenuItems.forEach((item, index) => {
                            const colors = ['#FF5722', '#4CAF50', '#2196F3', '#FFC107', '#9C27B0'];
                            const color = colors[index] || '#6a0dad';

                            // Mostrar tooltip al pasar el ratón
                            item.addEventListener('mouseenter', function(event) {
                                const description = this.getAttribute('data-description');
                                const rect = this.getBoundingClientRect();

                                // Configurar el tooltip
                                featureTooltip.textContent = description;
                                featureTooltip.style.backgroundColor = color;
                                featureTooltip.style.left = (rect.right + 15) + 'px';
                                featureTooltip.style.top = (rect.top + rect.height/2 - 20) + 'px';

                                // Configurar el color de la flecha
                                featureTooltip.style.setProperty('--tooltip-color', color);

                                // Mostrar el tooltip
                                featureTooltip.classList.add('visible');
                            });

                            // Ocultar tooltip al quitar el ratón
                            item.addEventListener('mouseleave', function() {
                                featureTooltip.classList.remove('visible');
                            });
                        });

                        // Configurar tooltips para el menú del perfil
                        const profileTooltip = document.getElementById('profile-tooltip');
                        const profileMenuItems = dropdownMenu.querySelectorAll('.profile-dropdown-item');

                        profileMenuItems.forEach((item) => {
                            // Mostrar tooltip al pasar el ratón
                            item.addEventListener('mouseenter', function(event) {
                                const description = this.getAttribute('data-tooltip');
                                const rect = this.getBoundingClientRect();

                                // Configurar el tooltip
                                profileTooltip.textContent = description;
                                profileTooltip.style.backgroundColor = 'rgba(106, 13, 173, 0.9)';
                                profileTooltip.style.left = (rect.right + 15) + 'px';
                                profileTooltip.style.top = (rect.top + rect.height/2 - 20) + 'px';

                                // Configurar el color de la flecha
                                profileTooltip.style.setProperty('--tooltip-color', 'rgba(106, 13, 173, 0.9)');

                                // Mostrar el tooltip
                                profileTooltip.classList.add('visible');
                            });

                            // Ocultar tooltip al quitar el ratón
                            item.addEventListener('mouseleave', function() {
                                profileTooltip.classList.remove('visible');
                            });
                        });
                    });
                </script>
            </div>
        </div>

        <!-- Contenido central -->
        <div class="main-content">
            <!-- Support Header -->
            <div class="support-header">
                <div class="header-content">
                    <div class="header-title">
                        <h1><i class="fas fa-life-ring"></i> Help & Support</h1>
                        <p>Get help, find answers, and connect with our support team</p>
                    </div>
                    <div class="header-actions">
                        <button class="contact-support-btn" id="contactSupportBtn">
                            <i class="fas fa-comment-dots"></i> Contact Support
                        </button>
                        <div class="support-status">
                            <i class="fas fa-circle status-online"></i> Online Support Available
                        </div>
                    </div>
                </div>
            </div>

            <!-- Support Categories -->
            <div class="support-filters">
                <div class="filters-header">
                    <h3><i class="fas fa-th-large"></i> Support Categories</h3>
                </div>
                <div class="filter-tabs">
                    <button class="filter-tab active" data-filter="all">
                        <i class="fas fa-list"></i> All Topics
                    </button>
                    <button class="filter-tab" data-filter="account">
                        <i class="fas fa-user-circle"></i> Account
                    </button>
                    <button class="filter-tab" data-filter="technical">
                        <i class="fas fa-cogs"></i> Technical Issues
                    </button>
                    <button class="filter-tab" data-filter="billing">
                        <i class="fas fa-credit-card"></i> Billing
                    </button>
                    <button class="filter-tab" data-filter="features">
                        <i class="fas fa-magic"></i> Features
                    </button>
                    <button class="filter-tab" data-filter="privacy">
                        <i class="fas fa-shield-alt"></i> Privacy & Security
                    </button>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions-section">
                <h3><i class="fas fa-bolt"></i> Quick Actions</h3>
                <div class="quick-actions-grid">
                    <div class="quick-action-card">
                        <div class="action-icon">
                            <i class="fas fa-comments"></i>
                        </div>
                        <h4>Live Chat</h4>
                        <p>Chat with our support team in real-time</p>
                        <button class="action-btn" onclick="startLiveChat()">Start Chat</button>
                    </div>
                    
                    <div class="quick-action-card">
                        <div class="action-icon">
                            <i class="fas fa-video"></i>
                        </div>
                        <h4>Video Tutorial</h4>
                        <p>Watch step-by-step video guides</p>
                        <button class="action-btn" onclick="openTutorials()">Watch Now</button>
                    </div>
                    
                    <div class="quick-action-card">
                        <div class="action-icon">
                            <i class="fas fa-book"></i>
                        </div>
                        <h4>User Guide</h4>
                        <p>Complete documentation and guides</p>
                        <button class="action-btn" onclick="openUserGuide()">Read Guide</button>
                    </div>
                    
                    <div class="quick-action-card">
                        <div class="action-icon">
                            <i class="fas fa-bug"></i>
                        </div>
                        <h4>Report Issue</h4>
                        <p>Report bugs or technical problems</p>
                        <button class="action-btn" onclick="reportIssue()">Report Now</button>
                    </div>
                </div>
            </div>

            <!-- FAQ Section -->
            <div class="faq-section">
                <h3><i class="fas fa-question-circle"></i> Frequently Asked Questions</h3>
                <div class="faq-container">
                    
                    <!-- FAQ Item 1 -->
                    <div class="faq-item" data-category="account">
                        <div class="faq-question" onclick="toggleFAQ(this)">
                            <h4><i class="fas fa-user"></i> How do I create my athletic profile?</h4>
                            <i class="fas fa-chevron-down toggle-icon"></i>
                        </div>
                        <div class="faq-answer">
                            <p>To create your athletic profile:</p>
                            <ol>
                                <li>Go to your Profile section from the sidebar</li>
                                <li>Click on "Edit Athletic Profile"</li>
                                <li>Fill in your sports information, achievements, and statistics</li>
                                <li>Upload photos and videos of your performance</li>
                                <li>Save your profile to make it visible to scouts and coaches</li>
                            </ol>
                            <div class="faq-actions">
                                <button class="btn-secondary" onclick="window.location.href='../Profile/athletic_profile.html'">Go to Profile</button>
                            </div>
                        </div>
                    </div>

                    <!-- FAQ Item 2 -->
                    <div class="faq-item" data-category="features">
                        <div class="faq-question" onclick="toggleFAQ(this)">
                            <h4><i class="fas fa-robot"></i> How does AI Analysis work?</h4>
                            <i class="fas fa-chevron-down toggle-icon"></i>
                        </div>
                        <div class="faq-answer">
                            <p>Our AI Analysis feature provides:</p>
                            <ul>
                                <li><strong>Video Analysis:</strong> Upload game footage for detailed breakdown</li>
                                <li><strong>Performance Stats:</strong> Compare your stats with professional players</li>
                                <li><strong>Player Comparison:</strong> See how you match against others in your position</li>
                                <li><strong>Training Recommendations:</strong> Get personalized training plans</li>
                                <li><strong>Talent Benchmarks:</strong> Measure against professional standards</li>
                            </ul>
                            <div class="faq-actions">
                                <button class="btn-primary" onclick="window.location.href='../AIAnalysis/ai_analysis_system.html'">Try AI Analysis</button>
                            </div>
                        </div>
                    </div>

                    <!-- FAQ Item 3 -->
                    <div class="faq-item" data-category="technical">
                        <div class="faq-question" onclick="toggleFAQ(this)">
                            <h4><i class="fas fa-upload"></i> Why can't I upload my videos?</h4>
                            <i class="fas fa-chevron-down toggle-icon"></i>
                        </div>
                        <div class="faq-answer">
                            <p>Common video upload issues and solutions:</p>
                            <ul>
                                <li><strong>File size:</strong> Maximum file size is 500MB</li>
                                <li><strong>Supported formats:</strong> MP4, AVI, MOV, WMV</li>
                                <li><strong>Internet connection:</strong> Ensure stable internet connection</li>
                                <li><strong>Browser compatibility:</strong> Use Chrome, Firefox, or Safari</li>
                            </ul>
                            <p>If issues persist, try clearing your browser cache or contact support.</p>
                            <div class="faq-actions">
                                <button class="btn-secondary" onclick="clearBrowserCache()">Clear Cache</button>
                                <button class="btn-primary" onclick="contactSupport()">Contact Support</button>
                            </div>
                        </div>
                    </div>

                    <!-- FAQ Item 4 -->
                    <div class="faq-item" data-category="privacy">
                        <div class="faq-question" onclick="toggleFAQ(this)">
                            <h4><i class="fas fa-eye"></i> Who can see my profile?</h4>
                            <i class="fas fa-chevron-down toggle-icon"></i>
                        </div>
                        <div class="faq-answer">
                            <p>Your profile visibility settings:</p>
                            <ul>
                                <li><strong>Public:</strong> Visible to all scouts, coaches, and users</li>
                                <li><strong>Verified Only:</strong> Only verified coaches and scouts can see</li>
                                <li><strong>Private:</strong> Only visible to you and invited contacts</li>
                            </ul>
                            <p>You can change these settings anytime in Privacy Settings.</p>
                            <div class="faq-actions">
                                <button class="btn-primary" onclick="openPrivacySettings()">Privacy Settings</button>
                            </div>
                        </div>
                    </div>

                    <!-- FAQ Item 5 -->
                    <div class="faq-item" data-category="billing">
                        <div class="faq-question" onclick="toggleFAQ(this)">
                            <h4><i class="fas fa-credit-card"></i> How do I upgrade my account?</h4>
                            <i class="fas fa-chevron-down toggle-icon"></i>
                        </div>
                        <div class="faq-answer">
                            <p>To upgrade to Premium:</p>
                            <ol>
                                <li>Go to Settings > Billing</li>
                                <li>Choose your preferred plan (Monthly/Yearly)</li>
                                <li>Enter payment information</li>
                                <li>Confirm subscription</li>
                            </ol>
                            <p><strong>Premium benefits:</strong> Unlimited video uploads, advanced AI analysis, priority support, and more.</p>
                            <div class="faq-actions">
                                <button class="btn-primary" onclick="upgradeToPremium()">Upgrade Now</button>
                            </div>
                        </div>
                    </div>

                    <!-- FAQ Item 6 -->
                    <div class="faq-item" data-category="account">
                        <div class="faq-question" onclick="toggleFAQ(this)">
                            <h4><i class="fas fa-key"></i> I forgot my password. What should I do?</h4>
                            <i class="fas fa-chevron-down toggle-icon"></i>
                        </div>
                        <div class="faq-answer">
                            <p>To reset your password:</p>
                            <ol>
                                <li>Go to the login page</li>
                                <li>Click "Forgot Password?"</li>
                                <li>Enter your email address</li>
                                <li>Check your email for reset link</li>
                                <li>Follow the instructions to create a new password</li>
                            </ol>
                            <p>If you don't receive the email, check your spam folder or contact support.</p>
                            <div class="faq-actions">
                                <button class="btn-secondary" onclick="resetPassword()">Reset Password</button>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

            <!-- Contact Support Section -->
            <div class="contact-section">
                <h3><i class="fas fa-headset"></i> Contact Our Support Team</h3>
                <div class="contact-options">
                    
                    <div class="contact-card">
                        <div class="contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <h4>Email Support</h4>
                        <p>Get help via email within 24 hours</p>
                        <p class="contact-info"><EMAIL></p>
                        <button class="contact-btn" onclick="sendEmail()">Send Email</button>
                    </div>

                    <div class="contact-card">
                        <div class="contact-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <h4>Phone Support</h4>
                        <p>Call us Monday to Friday, 9 AM - 6 PM</p>
                        <p class="contact-info">+****************</p>
                        <button class="contact-btn" onclick="callSupport()">Call Now</button>
                    </div>

                    <div class="contact-card">
                        <div class="contact-icon">
                            <i class="fas fa-comments"></i>
                        </div>
                        <h4>Live Chat</h4>
                        <p>Chat with our team in real-time</p>
                        <p class="contact-info">Available 24/7</p>
                        <button class="contact-btn" onclick="startLiveChat()">Start Chat</button>
                    </div>

                </div>
            </div>

        </div>
    </div>

    <!-- Tooltip elements -->
    <div id="feature-tooltip" class="js-tooltip"></div>
    <div id="profile-tooltip" class="js-tooltip"></div>

    <script src="support.js"></script>
</body>
</html>