/**
 * Database Configuration
 * 
 * Configuration and connection setup for MongoDB.
 */

const mongoose = require('mongoose');
const config = require('./index');

/**
 * Connect to MongoDB
 * 
 * @returns {Promise} Mongoose connection
 */
const connect = async () => {
  try {
    const connection = await mongoose.connect(config.database.uri, config.database.options);
    
    console.log(`MongoDB connected: ${connection.connection.host}`);
    
    return connection;
  } catch (error) {
    console.error(`MongoDB connection error: ${error.message}`);
    process.exit(1);
  }
};

/**
 * Disconnect from MongoDB
 * 
 * @returns {Promise} Promise that resolves when disconnected
 */
const disconnect = async () => {
  try {
    await mongoose.disconnect();
    console.log('MongoDB disconnected');
  } catch (error) {
    console.error(`MongoDB disconnection error: ${error.message}`);
  }
};

/**
 * Get the current connection
 * 
 * @returns {Object} Mongoose connection
 */
const getConnection = () => {
  return mongoose.connection;
};

/**
 * Check if connected to MongoDB
 * 
 * @returns {boolean} Whether connected to MongoDB
 */
const isConnected = () => {
  return mongoose.connection.readyState === 1;
};

/**
 * Set up connection event handlers
 */
const setupConnectionHandlers = () => {
  mongoose.connection.on('connected', () => {
    console.log('MongoDB connected');
  });
  
  mongoose.connection.on('error', (error) => {
    console.error(`MongoDB connection error: ${error.message}`);
  });
  
  mongoose.connection.on('disconnected', () => {
    console.log('MongoDB disconnected');
  });
  
  // If Node process ends, close the MongoDB connection
  process.on('SIGINT', async () => {
    await disconnect();
    process.exit(0);
  });
};

module.exports = {
  connect,
  disconnect,
  getConnection,
  isConnected,
  setupConnectionHandlers,
};
