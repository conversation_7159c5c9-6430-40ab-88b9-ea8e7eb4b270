/**
 * Sidebar Navigation Component
 * 
 * Handles the functionality of the sidebar navigation, including:
 * - Submenu dropdowns
 * - Active menu items
 * - Tooltips for menu items
 */

document.addEventListener('DOMContentLoaded', function() {
    // AI Analysis menu functionality
    initializeAIMenu();
    
    // Sports menu functionality
    initializeSportsMenu();
    
    // Initialize tooltips
    initializeTooltips();
});

/**
 * Initialize AI Analysis menu functionality
 */
function initializeAIMenu() {
    const aiMenuItem = document.getElementById('aiMenuItem');
    if (!aiMenuItem) return;
    
    const aiAnalysisLink = aiMenuItem.querySelector('a');
    const aiDropdownMenu = document.getElementById('aiDropdownMenu');
    
    // Ensure the NEW badge is always visible
    const badge = aiAnalysisLink.querySelector('.new-badge');
    if (badge) {
        badge.style.display = 'inline-block';
        badge.style.opacity = '1';
    }
    
    // Add events for dropdown menu items
    const aiMenuItems = aiDropdownMenu.querySelectorAll('li a');
    
    aiMenuItems.forEach((item) => {
        // Click on menu item
        item.addEventListener('click', function(event) {
            // Ensure the NEW badge remains visible
            const badge = aiAnalysisLink.querySelector('.new-badge');
            if (badge) {
                badge.style.display = 'inline-block';
                badge.style.opacity = '1';
            }
            
            // If the item doesn't have a valid href, prevent navigation
            if (this.getAttribute('href') === '#') {
                event.preventDefault();
                // Show "Coming Soon" message
                const featureName = this.querySelector('span').textContent.trim();
                const featureDescription = this.getAttribute('data-description');
                showFeatureComingSoon(featureName, featureDescription);
            }
        });
    });
}

/**
 * Initialize Sports menu functionality
 */
function initializeSportsMenu() {
    const sportsMenuItem = document.getElementById('sportsMenuItem');
    if (!sportsMenuItem) return;
    
    const sportsSubmenu = document.getElementById('sportsSubmenu');
    
    // Add events for sports menu items
    const sportsMenuItems = sportsSubmenu.querySelectorAll('li a');
    
    sportsMenuItems.forEach((item) => {
        // Click on menu item
        item.addEventListener('click', function(event) {
            // If the item doesn't have a valid href, prevent navigation
            if (this.getAttribute('href') === '#') {
                event.preventDefault();
                // Show "Coming Soon" message
                const sportName = this.textContent.trim();
                showFeatureComingSoon(sportName, `${sportName} section is coming soon!`);
            }
        });
    });
}

/**
 * Initialize tooltips for menu items
 */
function initializeTooltips() {
    const tooltip = document.getElementById('feature-tooltip');
    if (!tooltip) return;
    
    // AI Analysis menu tooltips
    const aiDropdownMenu = document.getElementById('aiDropdownMenu');
    if (aiDropdownMenu) {
        const aiMenuItems = aiDropdownMenu.querySelectorAll('li a');
        
        aiMenuItems.forEach((item) => {
            // Show tooltip on hover
            item.addEventListener('mouseenter', function(event) {
                const description = this.getAttribute('data-description');
                const color = this.getAttribute('data-color');
                const rect = this.getBoundingClientRect();
                
                // Configure tooltip
                tooltip.textContent = description;
                tooltip.style.backgroundColor = color + 'E6'; // Color with 90% opacity
                tooltip.style.left = (rect.right + 15) + 'px';
                tooltip.style.top = (rect.top + rect.height/2 - 20) + 'px';
                
                // Configure arrow color
                tooltip.style.setProperty('--tooltip-color', color + 'E6');
                
                // Show tooltip
                tooltip.classList.add('visible');
            });
            
            // Hide tooltip when mouse leaves
            item.addEventListener('mouseleave', function() {
                tooltip.classList.remove('visible');
            });
        });
    }
}

/**
 * Show a "Coming Soon" notification for features that are not yet implemented
 * 
 * @param {string} featureName - The name of the feature
 * @param {string} featureDescription - Description of the feature
 */
function showFeatureComingSoon(featureName, featureDescription = '') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = 'feature-notification';
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-info-circle"></i>
            <div class="notification-text">
                <strong>${featureName}</strong>
                <p>${featureDescription}</p>
                <p class="coming-soon-text">This feature is coming soon! We're working hard to bring it to you.</p>
            </div>
            <button class="notification-close"><i class="fas fa-times"></i></button>
        </div>
    `;
    
    // Add inline styles for the notification
    notification.style.position = 'fixed';
    notification.style.bottom = '20px';
    notification.style.right = '20px';
    notification.style.backgroundColor = 'white';
    notification.style.borderRadius = '8px';
    notification.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
    notification.style.zIndex = '9999';
    notification.style.overflow = 'hidden';
    notification.style.maxWidth = '350px';
    notification.style.animation = 'slideIn 0.3s forwards';
    
    // Styles for notification content
    const notificationContent = notification.querySelector('.notification-content');
    notificationContent.style.display = 'flex';
    notificationContent.style.padding = '15px';
    notificationContent.style.alignItems = 'center';
    
    // Styles for icon
    const icon = notification.querySelector('.fas.fa-info-circle');
    icon.style.color = '#6a0dad';
    icon.style.fontSize = '24px';
    icon.style.marginRight = '15px';
    
    // Styles for text
    const notificationText = notification.querySelector('.notification-text');
    notificationText.style.flex = '1';
    
    // Styles for close button
    const closeButton = notification.querySelector('.notification-close');
    closeButton.style.background = 'none';
    closeButton.style.border = 'none';
    closeButton.style.cursor = 'pointer';
    closeButton.style.fontSize = '16px';
    closeButton.style.color = '#666';
    
    // Add notification to DOM
    document.body.appendChild(notification);
    
    // Add keyframes for animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    `;
    document.head.appendChild(style);
    
    // Add event to close notification
    closeButton.addEventListener('click', function() {
        notification.style.animation = 'slideOut 0.3s forwards';
        setTimeout(() => {
            notification.remove();
        }, 300);
    });
    
    // Auto-close after 5 seconds
    setTimeout(() => {
        if (document.body.contains(notification)) {
            notification.style.animation = 'slideOut 0.3s forwards';
            setTimeout(() => {
                notification.remove();
            }, 300);
        }
    }, 5000);
}
