document.addEventListener('DOMContentLoaded', function() {
    // Tab switching functionality
    const tabButtons = document.querySelectorAll('.login-tab-btn');
    const formSections = document.querySelectorAll('.login-form-section');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons and sections
            tabButtons.forEach(btn => btn.classList.remove('active'));
            formSections.forEach(section => section.classList.remove('active'));

            // Add active class to clicked button
            this.classList.add('active');

            // Show corresponding form section
            const tabName = this.getAttribute('data-tab');
            document.getElementById(`${tabName}-section`).classList.add('active');
        });
    });

    // Password visibility toggle
    const togglePasswordButtons = document.querySelectorAll('.toggle-password');

    togglePasswordButtons.forEach(button => {
        button.addEventListener('click', function() {
            const passwordInput = this.previousElementSibling;

            // Toggle password visibility
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                this.classList.remove('fa-eye');
                this.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                this.classList.remove('fa-eye-slash');
                this.classList.add('fa-eye');
            }
        });
    });

    // Password strength meter
    const passwordInput = document.getElementById('register-password');
    const strengthSegments = document.querySelectorAll('.strength-segment');
    const strengthText = document.querySelector('.strength-text');

    if (passwordInput) {
        passwordInput.addEventListener('input', function() {
            const password = this.value;
            const strength = calculatePasswordStrength(password);

            // Update strength meter
            updateStrengthMeter(strength);
        });
    }

    function calculatePasswordStrength(password) {
        // Password strength calculation
        let strength = 0;

        // Length check
        if (password.length >= 8) {
            strength += 1;
        }

        // Contains lowercase letters
        if (/[a-z]/.test(password)) {
            strength += 1;
        }

        // Contains uppercase letters
        if (/[A-Z]/.test(password)) {
            strength += 1;
        }

        // Contains numbers
        if (/[0-9]/.test(password)) {
            strength += 1;
        }

        // Contains special characters
        if (/[^A-Za-z0-9]/.test(password)) {
            strength += 1;
        }

        return Math.min(strength, 4);
    }

    function updateStrengthMeter(strength) {
        // Reset all segments and classes
        strengthSegments.forEach(segment => {
            segment.classList.remove('weak', 'medium', 'strong');
            segment.style.backgroundColor = '';
        });

        strengthText.classList.remove('weak', 'medium', 'strong');

        // Update text and classes based on strength
        let strengthClass = '';

        switch(strength) {
            case 0:
                strengthText.textContent = 'Password strength';
                break;
            case 1:
                strengthText.textContent = 'Weak password';
                strengthClass = 'weak';
                break;
            case 2:
                strengthText.textContent = 'Fair password';
                strengthClass = 'medium';
                break;
            case 3:
                strengthText.textContent = 'Good password';
                strengthClass = 'medium';
                break;
            case 4:
                strengthText.textContent = 'Strong password';
                strengthClass = 'strong';
                break;
        }

        // Add appropriate class to text
        if (strengthClass) {
            strengthText.classList.add(strengthClass);
        }

        // Update segments
        for (let i = 0; i < strengthSegments.length; i++) {
            if (i < strength) {
                strengthSegments[i].classList.add(strengthClass);
            }
        }
    }

    // Registration steps navigation
    const nextButtons = document.querySelectorAll('.next-step-btn');
    const prevButtons = document.querySelectorAll('.prev-step-btn');
    const steps = document.querySelectorAll('.registration-step');
    const stepIndicators = document.querySelectorAll('.step');

    // Next step buttons
    nextButtons.forEach(button => {
        button.addEventListener('click', function() {
            const currentStep = parseInt(this.getAttribute('data-next')) - 1;
            const nextStep = parseInt(this.getAttribute('data-next'));

            // Hide current step
            steps[currentStep].classList.remove('active');

            // Show next step
            steps[nextStep].classList.add('active');

            // Update step indicators
            stepIndicators[currentStep].classList.add('completed');
            stepIndicators[nextStep].classList.add('active');

            // Special handling for step 3 (user details)
            if (nextStep === 2) { // 0-based index, so 2 is the third step
                console.log('Moving to step 3 (user details)');
                // Ensure name fields are correctly displayed based on user type
                setTimeout(initializeNameFields, 100);
            }

            // Scroll to top of form
            document.querySelector('.registration-steps').scrollIntoView({ behavior: 'smooth' });
        });
    });

    // Previous step buttons
    prevButtons.forEach(button => {
        button.addEventListener('click', function() {
            const currentStep = parseInt(this.getAttribute('data-prev')) + 1;
            const prevStep = parseInt(this.getAttribute('data-prev'));

            // Hide current step
            steps[currentStep].classList.remove('active');

            // Show previous step
            steps[prevStep].classList.add('active');

            // Update step indicators
            stepIndicators[currentStep].classList.remove('active');
            stepIndicators[prevStep].classList.remove('completed');
            stepIndicators[prevStep].classList.add('active');

            // Scroll to top of form
            document.querySelector('.registration-steps').scrollIntoView({ behavior: 'smooth' });
        });
    });

    // User type selection - Completely rewritten for reliability
    const userTypeRadios = document.querySelectorAll('input[name="user-type"]');
    const otherRoleInput = document.querySelector('.other-role-input');

    // Direct references to DOM elements
    const personalNameFields = document.getElementById('personal-name-fields');
    const clubNameField = document.getElementById('club-name-field');
    const nicknameField = document.getElementById('nickname-field');
    const firstNameInput = document.getElementById('register-first-name');
    const lastNameInput = document.getElementById('register-last-name');
    const clubNameInput = document.getElementById('register-club-name');
    const nicknameInput = document.getElementById('register-nickname');

    // Function to handle name fields display based on user type
    function updateNameFields(userType) {
        console.log('Updating name fields for user type:', userType);

        // Handle "Other" role input field
        if (userType === 'other') {
            otherRoleInput.style.display = 'block';
            setTimeout(() => document.getElementById('other-role').focus(), 100);
        } else {
            otherRoleInput.style.display = 'none';
        }

        // Handle name fields based on user type
        if (userType === 'club') {
            // Show club name field, hide personal name fields and nickname
            personalNameFields.style.display = 'none';
            clubNameField.style.display = 'block';
            nicknameField.style.display = 'none';

            // Update form validation
            firstNameInput.disabled = true;
            lastNameInput.disabled = true;
            firstNameInput.required = false;
            lastNameInput.required = false;
            nicknameInput.disabled = true;

            clubNameInput.disabled = false;
            clubNameInput.required = true;
            setTimeout(() => clubNameInput.focus(), 100);

            console.log('Switched to club name field');
        } else {
            // Show personal name fields and nickname, hide club name field
            personalNameFields.style.display = 'flex';
            clubNameField.style.display = 'none';
            nicknameField.style.display = 'block';

            // Update form validation
            firstNameInput.disabled = false;
            lastNameInput.disabled = false;
            firstNameInput.required = true;
            lastNameInput.required = true;
            nicknameInput.disabled = false;

            clubNameInput.disabled = true;
            clubNameInput.required = false;

            console.log('Switched to personal name fields');
        }
    }

    // Add change event listeners to all user type radio buttons
    userTypeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            const userType = this.value;
            console.log('User type changed to:', userType);
            updateNameFields(userType);
        });
    });

    // Initialize name fields based on selected user type
    function initializeNameFields() {
        const selectedUserType = document.querySelector('input[name="user-type"]:checked')?.value || 'athlete';
        console.log('Initializing name fields with user type:', selectedUserType);
        updateNameFields(selectedUserType);
    }

    // Note: We're now handling step 3 initialization in the main navigation code above

    // Initialize on page load if already on step 3
    window.addEventListener('load', function() {
        console.log('Page loaded, checking if on step 3');
        const step3 = document.getElementById('step-3');
        if (step3 && step3.classList.contains('active')) {
            console.log('Already on step 3, initializing name fields');
            initializeNameFields();
        }
    });

    // Sync level selection between step 2 and step 3
    const levelRadios = document.querySelectorAll('input[name="level-type"]');
    const levelSelect = document.getElementById('level-select');

    levelRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            const levelValue = this.value;
            levelSelect.value = levelValue;
        });
    });

    // Set initial level value when reaching step 3
    document.querySelector('.next-step-btn[data-next="3"]').addEventListener('click', function() {
        const selectedLevel = document.querySelector('input[name="level-type"]:checked').value;
        levelSelect.value = selectedLevel;
    });

    // Form submission
    const loginForm = document.getElementById('login-form');
    const registerForm = document.getElementById('register-form');

    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Get form data
            const email = document.getElementById('login-email').value;
            const password = document.getElementById('login-password').value;
            const rememberMe = document.getElementById('remember-me').checked;

            // Here you would typically send the data to your server
            console.log('Login form submitted:', { email, password, rememberMe });

            // For demo purposes, redirect to index page
            showNotification('Login successful!', 'success');
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 1500);
        });
    }

    if (registerForm) {
        registerForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Get form data
            const email = document.getElementById('register-email').value;
            const password = document.getElementById('register-password').value;
            const userType = document.querySelector('input[name="user-type"]:checked').value;
            const sport = document.getElementById('sport-select').value;
            const level = document.getElementById('level-select').value;

            // Get name data based on user type
            let firstName = '';
            let lastName = '';
            let clubName = '';
            let nickname = '';

            if (userType === 'club') {
                clubName = document.getElementById('register-club-name').value;
            } else {
                firstName = document.getElementById('register-first-name').value;
                lastName = document.getElementById('register-last-name').value;
                nickname = document.getElementById('register-nickname').value;
            }

            // Get other role if selected
            let otherRole = '';
            if (userType === 'other') {
                otherRole = document.getElementById('other-role').value;
            }

            // Here you would typically send the data to your server
            console.log('Registration form submitted:', {
                userType,
                firstName,
                lastName,
                nickname,
                clubName,
                email,
                password,
                sport,
                level,
                otherRole
            });

            // For demo purposes, show success and redirect
            showNotification('Account created successfully!', 'success');
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 1500);
        });
    }

    // Modern notification function
    function showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;

        // Set icon based on notification type
        let icon, bgColor, borderColor;

        switch(type) {
            case 'success':
                icon = 'fa-check-circle';
                bgColor = 'linear-gradient(135deg, #4caf50, #2e7d32)';
                borderColor = '#2e7d32';
                break;
            case 'error':
                icon = 'fa-exclamation-circle';
                bgColor = 'linear-gradient(135deg, #f44336, #c62828)';
                borderColor = '#c62828';
                break;
            case 'warning':
                icon = 'fa-exclamation-triangle';
                bgColor = 'linear-gradient(135deg, #ff9800, #ef6c00)';
                borderColor = '#ef6c00';
                break;
            default: // info
                icon = 'fa-info-circle';
                bgColor = 'linear-gradient(135deg, #2196f3, #0d47a1)';
                borderColor = '#0d47a1';
        }

        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-icon">
                    <i class="fas ${icon}"></i>
                </div>
                <div class="notification-message">
                    <span>${message}</span>
                </div>
                <div class="notification-close">
                    <i class="fas fa-times"></i>
                </div>
            </div>
            <div class="notification-progress"></div>
        `;

        // Add styles
        notification.style.position = 'fixed';
        notification.style.top = '20px';
        notification.style.right = '20px';
        notification.style.background = bgColor;
        notification.style.color = 'white';
        notification.style.padding = '15px 20px';
        notification.style.borderRadius = '12px';
        notification.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.2)';
        notification.style.zIndex = '9999';
        notification.style.minWidth = '300px';
        notification.style.maxWidth = '400px';
        notification.style.backdropFilter = 'blur(10px)';
        notification.style.border = `1px solid ${borderColor}`;
        notification.style.overflow = 'hidden';

        // Content styles
        const content = notification.querySelector('.notification-content');
        content.style.display = 'flex';
        content.style.alignItems = 'center';
        content.style.justifyContent = 'space-between';

        // Icon styles
        const iconDiv = notification.querySelector('.notification-icon');
        iconDiv.style.marginRight = '15px';
        iconDiv.style.fontSize = '24px';

        // Close button styles
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.style.cursor = 'pointer';
        closeBtn.style.opacity = '0.7';
        closeBtn.style.transition = 'opacity 0.3s';
        closeBtn.style.marginLeft = '15px';

        closeBtn.addEventListener('mouseenter', () => {
            closeBtn.style.opacity = '1';
        });

        closeBtn.addEventListener('mouseleave', () => {
            closeBtn.style.opacity = '0.7';
        });

        closeBtn.addEventListener('click', () => {
            closeNotification();
        });

        // Progress bar
        const progress = notification.querySelector('.notification-progress');
        progress.style.position = 'absolute';
        progress.style.bottom = '0';
        progress.style.left = '0';
        progress.style.height = '3px';
        progress.style.width = '100%';
        progress.style.backgroundColor = 'rgba(255, 255, 255, 0.3)';

        const progressInner = document.createElement('div');
        progressInner.style.height = '100%';
        progressInner.style.width = '100%';
        progressInner.style.backgroundColor = 'rgba(255, 255, 255, 0.7)';
        progressInner.style.transformOrigin = 'left';
        progress.appendChild(progressInner);

        // Add animation keyframes
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateY(-20px); opacity: 0; }
                to { transform: translateY(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateY(0); opacity: 1; }
                to { transform: translateY(-20px); opacity: 0; }
            }
            @keyframes progress {
                from { transform: scaleX(1); }
                to { transform: scaleX(0); }
            }
        `;
        document.head.appendChild(style);

        // Animation
        notification.style.animation = 'slideIn 0.3s forwards';
        progressInner.style.animation = 'progress 3s linear forwards';

        // Add to DOM
        document.body.appendChild(notification);

        // Auto remove after 3 seconds
        const timeout = setTimeout(() => {
            closeNotification();
        }, 3000);

        // Close notification function
        function closeNotification() {
            clearTimeout(timeout);
            notification.style.animation = 'slideOut 0.3s forwards';
            setTimeout(() => {
                notification.remove();
            }, 300);
        }
    }
});
