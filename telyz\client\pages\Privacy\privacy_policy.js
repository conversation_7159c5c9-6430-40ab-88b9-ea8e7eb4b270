// Privacy Policy page JavaScript functionality
document.addEventListener('DOMContentLoaded', function() {
    
    // ================ NAVIGATION FUNCTIONALITY ================ //
    const navLinks = document.querySelectorAll('.nav-link');
    const sections = document.querySelectorAll('.privacy-section');

    // Handle navigation clicks
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active class from all links
            navLinks.forEach(l => l.classList.remove('active'));
            // Add active class to clicked link
            this.classList.add('active');
            
            // Hide all sections
            sections.forEach(section => section.classList.remove('active'));
            
            // Show target section
            const targetSection = this.getAttribute('data-section');
            const section = document.getElementById(targetSection);
            if (section) {
                section.classList.add('active');
                section.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
        });
    });

    // ================ SMOOTH SCROLLING FOR ANCHOR LINKS ================ //
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });

    // ================ DOWNLOAD PDF FUNCTIONALITY ================ //
    window.downloadPolicy = function() {
        showNotification('Generating PDF...', 'info');
        
        // Simulate PDF generation
        setTimeout(() => {
            showNotification('Privacy Policy PDF downloaded successfully! 📄', 'success');
            // In real implementation, this would trigger actual PDF download
        }, 2000);
    };

    // Add event listener to download button
    const downloadBtn = document.getElementById('downloadPolicyBtn');
    if (downloadBtn) {
        downloadBtn.addEventListener('click', downloadPolicy);
    }

    // ================ COOKIE SETTINGS FUNCTIONALITY ================ //
    window.openCookieSettings = function() {
        showCookieModal();
    };

    function showCookieModal() {
        const modal = createCookieModal();
        document.body.appendChild(modal);
        
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
    }

    function createCookieModal() {
        const modal = document.createElement('div');
        modal.className = 'cookie-modal';
        modal.innerHTML = `
            <div class="cookie-modal-content">
                <div class="cookie-modal-header">
                    <h3><i class="fas fa-cookie-bite"></i> Cookie Preferences</h3>
                    <button class="close-modal" onclick="closeCookieModal(this)">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="cookie-modal-body">
                    <p>Manage your cookie preferences. Essential cookies cannot be disabled as they are required for the platform to function.</p>
                    
                    <div class="cookie-option">
                        <div class="cookie-option-header">
                            <label>
                                <input type="checkbox" checked disabled>
                                <span class="cookie-name">Essential Cookies</span>
                                <span class="cookie-badge required">Required</span>
                            </label>
                        </div>
                        <p class="cookie-description">Necessary for authentication, security, and basic functionality.</p>
                    </div>
                    
                    <div class="cookie-option">
                        <div class="cookie-option-header">
                            <label>
                                <input type="checkbox" id="analytics-cookies" checked>
                                <span class="cookie-name">Analytics Cookies</span>
                                <span class="cookie-badge optional">Optional</span>
                            </label>
                        </div>
                        <p class="cookie-description">Help us understand how you use our platform to improve our services.</p>
                    </div>
                    
                    <div class="cookie-option">
                        <div class="cookie-option-header">
                            <label>
                                <input type="checkbox" id="personalization-cookies" checked>
                                <span class="cookie-name">Personalization Cookies</span>
                                <span class="cookie-badge optional">Optional</span>
                            </label>
                        </div>
                        <p class="cookie-description">Provide personalized content and recommendations based on your preferences.</p>
                    </div>
                </div>
                <div class="cookie-modal-footer">
                    <button class="btn-secondary" onclick="closeCookieModal(this)">Cancel</button>
                    <button class="btn-primary" onclick="saveCookiePreferences()">Save Preferences</button>
                </div>
            </div>
        `;
        return modal;
    }

    window.closeCookieModal = function(element) {
        const modal = element.closest('.cookie-modal');
        modal.classList.remove('show');
        setTimeout(() => {
            if (modal.parentNode) {
                modal.parentNode.removeChild(modal);
            }
        }, 300);
    };

    window.saveCookiePreferences = function() {
        const analyticsEnabled = document.getElementById('analytics-cookies').checked;
        const personalizationEnabled = document.getElementById('personalization-cookies').checked;
        
        // Save preferences (in real implementation, this would save to backend/localStorage)
        localStorage.setItem('cookiePreferences', JSON.stringify({
            analytics: analyticsEnabled,
            personalization: personalizationEnabled,
            timestamp: new Date().toISOString()
        }));
        
        showNotification('Cookie preferences saved successfully! 🍪', 'success');
        
        // Close modal
        const modal = document.querySelector('.cookie-modal');
        if (modal) {
            closeCookieModal(modal.querySelector('.close-modal'));
        }
    };

    // ================ USER RIGHTS FUNCTIONS ================ //
    window.requestDataAccess = function() {
        showNotification('Data access request submitted. You will receive your data within 30 days.', 'info');
    };

    window.editProfile = function() {
        showNotification('Redirecting to profile settings...', 'info');
        setTimeout(() => {
            window.location.href = '../Profile/athletic_profile.html';
        }, 1500);
    };

    window.requestDataDeletion = function() {
        if (confirm('Are you sure you want to delete all your data? This action cannot be undone.')) {
            showNotification('Data deletion request submitted. Your account will be deleted within 30 days.', 'warning');
        }
    };

    window.exportData = function() {
        showNotification('Preparing your data export...', 'info');
        setTimeout(() => {
            showNotification('Data export ready! Download link sent to your email. 📧', 'success');
        }, 3000);
    };

    window.restrictProcessing = function() {
        showNotification('Processing restriction request submitted.', 'info');
    };

    window.withdrawConsent = function() {
        if (confirm('Are you sure you want to withdraw consent? This may limit some features.')) {
            showNotification('Consent withdrawal processed. Some features may be limited.', 'warning');
        }
    };

    // ================ NOTIFICATION SUBSCRIPTION ================ //
    window.subscribeToUpdates = function() {
        const email = document.getElementById('policyNotificationEmail').value;
        
        if (!email) {
            showNotification('Please enter your email address.', 'warning');
            return;
        }
        
        if (!isValidEmail(email)) {
            showNotification('Please enter a valid email address.', 'error');
            return;
        }
        
        showNotification('Thank you! You will receive notifications about policy updates.', 'success');
        document.getElementById('policyNotificationEmail').value = '';
    };

    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // ================ SCROLL PROGRESS INDICATOR ================ //
    function createScrollProgress() {
        const progressBar = document.createElement('div');
        progressBar.className = 'scroll-progress';
        progressBar.innerHTML = '<div class="scroll-progress-fill"></div>';
        document.body.appendChild(progressBar);
        
        const progressFill = progressBar.querySelector('.scroll-progress-fill');
        
        window.addEventListener('scroll', () => {
            const scrollTop = window.pageYOffset;
            const docHeight = document.body.scrollHeight - window.innerHeight;
            const scrollPercent = (scrollTop / docHeight) * 100;
            
            progressFill.style.width = scrollPercent + '%';
        });
    }

    createScrollProgress();

    // ================ SECTION ANIMATIONS ================ //
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // Observe all privacy sections
    sections.forEach(section => {
        observer.observe(section);
    });

    // ================ READING TIME CALCULATOR ================ //
    function calculateReadingTime() {
        const sections = document.querySelectorAll('.privacy-section');
        
        sections.forEach(section => {
            const text = section.textContent || section.innerText;
            const wordCount = text.trim().split(/\s+/).length;
            const readingTime = Math.ceil(wordCount / 200); // Average reading speed: 200 words/minute
            
            const timeElement = section.querySelector('.reading-time');
            if (timeElement) {
                timeElement.innerHTML = `<i class="fas fa-clock"></i> ${readingTime} min read`;
            }
        });
    }

    calculateReadingTime();

    // ================ PRINT FUNCTIONALITY ================ //
    window.printPolicy = function() {
        window.print();
    };

    // ================ NOTIFICATION SYSTEM ================ //
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close" onclick="closeNotification(this)">
                <i class="fas fa-times"></i>
            </button>
        `;

        document.body.appendChild(notification);
        positionNotification(notification);

        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        setTimeout(() => {
            closeNotification(notification.querySelector('.notification-close'));
        }, 5000);
    }

    function getNotificationIcon(type) {
        const icons = {
            'success': 'check-circle',
            'error': 'exclamation-circle',
            'warning': 'exclamation-triangle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    function positionNotification(notification) {
        const existingNotifications = document.querySelectorAll('.notification');
        const topOffset = 20 + (existingNotifications.length - 1) * 80;
        notification.style.top = `${topOffset}px`;
        notification.style.right = '20px';
    }

    window.closeNotification = function(closeButton) {
        const notification = closeButton.parentElement;
        notification.classList.remove('show');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
            repositionNotifications();
        }, 300);
    };

    function repositionNotifications() {
        const notifications = document.querySelectorAll('.notification');
        notifications.forEach((notification, index) => {
            notification.style.top = `${20 + index * 80}px`;
        });
    }

    // ================ KEYBOARD NAVIGATION ================ //
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            // Focus on search if available, or scroll to top
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }
        
        if (e.key === 'Escape') {
            // Close any open modals
            const modal = document.querySelector('.cookie-modal');
            if (modal) {
                closeCookieModal(modal.querySelector('.close-modal'));
            }
        }
    });

    // ================ ACCESSIBILITY IMPROVEMENTS ================ //
    // Add skip to content link
    const skipLink = document.createElement('a');
    skipLink.href = '#main-content';
    skipLink.className = 'skip-link';
    skipLink.textContent = 'Skip to main content';
    document.body.insertBefore(skipLink, document.body.firstChild);

    // Improve focus management
    document.addEventListener('focusin', function(e) {
        if (e.target.matches('.nav-link')) {
            e.target.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
    });

    // ================ THEME DETECTION ================ //
    function detectPreferredTheme() {
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            document.body.classList.add('dark-theme-preferred');
        }
    }

    detectPreferredTheme();

    // Listen for theme changes
    if (window.matchMedia) {
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', detectPreferredTheme);
    }

});

// ================ ADDITIONAL STYLES FOR DYNAMIC ELEMENTS ================ //
const dynamicStyles = `
<style>
/* Cookie Modal Styles */
.cookie-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.cookie-modal.show {
    opacity: 1;
    visibility: visible;
}

.cookie-modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    transform: translateY(-20px);
    transition: transform 0.3s ease;
}

.cookie-modal.show .cookie-modal-content {
    transform: translateY(0);
}

.cookie-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid #e9ecef;
}

.cookie-modal-header h3 {
    margin: 0;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 10px;
}

.close-modal {
    background: none;
    border: none;
    font-size: 20px;
    color: #95a5a6;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.close-modal:hover {
    background: #f8f9fa;
    color: #7f8c8d;
}

.cookie-modal-body {
    padding: 25px;
}

.cookie-modal-body p {
    color: #5d6d7e;
    margin-bottom: 20px;
    line-height: 1.6;
}

.cookie-option {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.cookie-option:hover {
    background: #f8f9fa;
}

.cookie-option-header {
    margin-bottom: 8px;
}

.cookie-option label {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    font-weight: 600;
    color: #2c3e50;
}

.cookie-option input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #6a0dad;
}

.cookie-name {
    flex: 1;
}

.cookie-badge {
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
}

.cookie-badge.required {
    background: #fdf2f2;
    color: #e74c3c;
}

.cookie-badge.optional {
    background: #e8f5e8;
    color: #27ae60;
}

.cookie-description {
    color: #7f8c8d;
    font-size: 13px;
    margin: 0;
    line-height: 1.4;
}

.cookie-modal-footer {
    padding: 20px 25px;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.btn-primary, .btn-secondary {
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, #6a0dad, #8b4bcf);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a0b8a, #7a40b8);
    transform: translateY(-1px);
}

.btn-secondary {
    background: #ecf0f1;
    color: #2c3e50;
    border: 1px solid #bdc3c7;
}

.btn-secondary:hover {
    background: #d5dbdb;
}

/* Scroll Progress Bar */
.scroll-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: rgba(106, 13, 173, 0.1);
    z-index: 9999;
}

.scroll-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #6a0dad, #8b4bcf);
    width: 0%;
    transition: width 0.1s ease;
}

/* Skip Link */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #6a0dad;
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 10001;
    transition: top 0.3s ease;
}

.skip-link:focus {
    top: 6px;
}

/* Notification Styles */
.notification {
    position: fixed;
    right: -400px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-left: 4px solid #6a0dad;
    z-index: 10000;
    max-width: 350px;
    transition: all 0.3s ease;
    overflow: hidden;
}

.notification.show {
    right: 20px;
}

.notification-success {
    border-left-color: #27ae60;
}

.notification-error {
    border-left-color: #e74c3c;
}

.notification-warning {
    border-left-color: #f39c12;
}

.notification-info {
    border-left-color: #3498db;
}

.notification-content {
    padding: 16px 20px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.notification-content i {
    font-size: 18px;
    color: #6a0dad;
}

.notification-success .notification-content i {
    color: #27ae60;
}

.notification-error .notification-content i {
    color: #e74c3c;
}

.notification-warning .notification-content i {
    color: #f39c12;
}

.notification-info .notification-content i {
    color: #3498db;
}

.notification-content span {
    flex: 1;
    color: #2c3e50;
    font-weight: 500;
}

.notification-close {
    position: absolute;
    top: 8px;
    right: 8px;
    background: none;
    border: none;
    color: #95a5a6;
    cursor: pointer;
    padding: 4px;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.notification-close:hover {
    background: #ecf0f1;
    color: #7f8c8d;
}

/* Animation for sections */
.privacy-section {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease;
}

.privacy-section.animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
    .dark-theme-preferred .cookie-modal-content {
        background: #2c3e50;
        color: white;
    }
    
    .dark-theme-preferred .cookie-modal-header {
        border-color: #34495e;
    }
    
    .dark-theme-preferred .cookie-option {
        border-color: #34495e;
        background: #34495e;
    }
    
    .dark-theme-preferred .close-modal:hover {
        background: #34495e;
    }
}

/* Mobile responsiveness for modal */
@media (max-width: 768px) {
    .cookie-modal-content {
        width: 95%;
        margin: 20px;
    }
    
    .cookie-modal-footer {
        flex-direction: column;
    }
    
    .cookie-modal-footer button {
        width: 100%;
    }
}
</style>
`;

// Add dynamic styles to head
document.head.insertAdjacentHTML('beforeend', dynamicStyles);