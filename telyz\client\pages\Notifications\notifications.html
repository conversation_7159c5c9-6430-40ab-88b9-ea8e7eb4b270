<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notifications - Telez</title>
    <link rel="stylesheet" href="notifications_new.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Barra lateral izquierda -->
        <div class="sidebar-left">
            <div class="logo">
                <a href="../Home/index.html" class="logo-link">
                    <div class="logo-container">T</div>
                </a>
            </div>
            <div class="sidebar-menu">
                <ul>
                    <li class="has-submenu" id="aiMenuItem">
    <a href="#"><i class="fas fa-robot"></i> AI Analysis <span class="badge new-badge">NEW</span></a>
    <ul class="submenu ai-submenu" id="aiDropdownMenu">
        <li>
            <a href="../AiAnalysisSystem/ai_analysis_system.html" data-description="Upload game footage for AI breakdown of techniques and plays" data-color="#FF5722">
                <i class="fas fa-video"></i> <span>Video Analysis</span>
            </a>
        </li>
        <li>
            <a href="#" data-description="View weekly stats of star players and compare with your performance" data-color="#4CAF50">
                <i class="fas fa-chart-line"></i> <span>Performance Stats</span>
            </a>
        </li>
        <li>
            <a href="#" data-description="Compare your metrics with other players in your position" data-color="#2196F3">
                <i class="fas fa-users"></i> <span>Player Comparison</span>
            </a>
        </li>
        <li>
            <a href="#" data-description="Get personalized training plans based on your performance data" data-color="#FFC107">
                <i class="fas fa-dumbbell"></i> <span>Training Recommendations</span>
            </a>
        </li>
        <li>
            <a href="#" data-description="See how you measure against professional standards in your sport" data-color="#9C27B0">
                <i class="fas fa-trophy"></i> <span>Talent Benchmarks</span>
            </a>
        </li>
    </ul>
</li>
                    <li><a href="#"><i class="fas fa-hashtag"></i> Explore</a></li>
                    <li><a href="#"><i class="fas fa-bullhorn"></i> Announcements</a></li>
                    <li><a href="../Messages/index.html"><i class="fas fa-envelope"></i> Messages</a></li>
                    <li class="has-submenu" id="sportsMenuItem">
                        <a href="#"><i class="fas fa-running"></i> Sports</a>
                        <ul class="submenu sports-submenu" id="sportsSubmenu">
                            <li><a href="#"><i class="fas fa-futbol"></i> Football</a></li>
                            <li><a href="#"><i class="fas fa-basketball-ball"></i> Basketball</a></li>
                            <li><a href="#"><i class="fas fa-volleyball-ball"></i> Volleyball</a></li>
                            <li><a href="#"><i class="fas fa-baseball-ball"></i> Baseball</a></li>
                            <li><a href="#"><i class="fas fa-table-tennis"></i> Cricket</a></li>
                            <li><a href="#"><i class="fas fa-hockey-puck"></i> Field Hockey</a></li>
                        </ul>
                    </li>
                    <li><a href="../Opportunities/opportunities.html"><i class="fas fa-briefcase"></i> Opportunities</a></li>
                    <li><a href="#"><i class="fas fa-trophy"></i> Achievements</a></li>
                    <li><a href="#"><i class="fas fa-dumbbell"></i> Training</a></li>
                </ul>
                <div class="user-profile-sidebar">
                    <div class="profile-pic-small">
                        <img src="https://via.placeholder.com/40" alt="Profile" class="profile-pic-img">
                    </div>
                    <div class="profile-info-sidebar">
                        <span>John Doe</span>
                        <span class="teams-clubs">Teams & Clubs</span>
                    </div>
                    <div class="profile-dropdown-toggle" id="profileDropdownToggle">
                        <i class="fas fa-ellipsis-v"></i>
                    </div>
                    <div class="profile-dropdown-menu" id="profileDropdownMenu">
                        <a href="../Profile/athletic_profile.html" class="profile-dropdown-item" data-tooltip="View your complete athletic profile and career history"><i class="fas fa-user"></i> View Athletic Profile</a>
                        <a href="#" class="profile-dropdown-item" data-tooltip="Edit your athletic profile information, skills and achievements"><i class="fas fa-cog"></i> Edit Athletic Profile</a>
                        <a href="#" class="profile-dropdown-item" data-tooltip="View and manage your sports achievements, trophies and awards"><i class="fas fa-medal"></i> Achievements</a>
                        <a href="../SportsRecommendations/sports_recommendations.html" class="profile-dropdown-item" data-tooltip="View and manage recommendations from coaches, scouts and sports experts"><i class="fas fa-star"></i> Expert Recommendations</a>
                        <a href="#" class="profile-dropdown-item" data-tooltip="Control your athletic profile privacy and who can view it"><i class="fas fa-shield-alt"></i> Privacy Settings</a>
                        <div class="dropdown-divider"></div>
                        <a href="#" class="profile-dropdown-item logout-item" data-tooltip="Sign out from your account"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </div>
                </div>

                <script>
                    // JavaScript para controlar el menú desplegable
                    document.addEventListener('DOMContentLoaded', function() {
                        const toggleButton = document.getElementById('profileDropdownToggle');
                        const dropdownMenu = document.getElementById('profileDropdownMenu');
                        const userProfile = document.querySelector('.user-profile-sidebar');

                        let isMenuOpen = false;

                        // Función para mostrar/ocultar el menú
                        toggleButton.addEventListener('click', function(e) {
                            e.stopPropagation();
                            isMenuOpen = !isMenuOpen;
                            dropdownMenu.classList.toggle('show', isMenuOpen);

                            // Asegurarse de que los iconos sean visibles
                            if (isMenuOpen) {
                                setTimeout(() => {
                                    const icons = dropdownMenu.querySelectorAll('i');
                                    icons.forEach(icon => {
                                        icon.style.display = 'inline-block';
                                    });
                                }, 50);
                            }
                        });

                        // Cerrar el menú al hacer clic fuera de él
                        document.addEventListener('click', function(event) {
                            if (!userProfile.contains(event.target)) {
                                isMenuOpen = false;
                                dropdownMenu.classList.remove('show');
                            }
                        });

                        // Configurar tooltips para las opciones del menú AI Analysis
                        const featureTooltip = document.getElementById('feature-tooltip');
                        const featureMenuItems = document.querySelectorAll('#aiDropdownMenu a');

                        featureMenuItems.forEach((item, index) => {
                            const colors = ['#FF5722', '#4CAF50', '#2196F3', '#FFC107', '#9C27B0'];
                            const color = colors[index] || '#6a0dad';

                            // Mostrar tooltip al pasar el ratón
                            item.addEventListener('mouseenter', function(event) {
                                const description = this.getAttribute('data-description');
                                const rect = this.getBoundingClientRect();

                                // Configurar el tooltip
                                featureTooltip.textContent = description;
                                featureTooltip.style.backgroundColor = color;
                                featureTooltip.style.left = (rect.right + 15) + 'px';
                                featureTooltip.style.top = (rect.top + rect.height/2 - 20) + 'px';

                                // Configurar el color de la flecha
                                featureTooltip.style.setProperty('--tooltip-color', color);

                                // Mostrar el tooltip
                                featureTooltip.classList.add('visible');
                            });

                            // Ocultar tooltip al quitar el ratón
                            item.addEventListener('mouseleave', function() {
                                featureTooltip.classList.remove('visible');
                            });
                        });

                        // Configurar tooltips para el menú del perfil
                        const profileTooltip = document.getElementById('profile-tooltip');
                        const profileMenuItems = dropdownMenu.querySelectorAll('.profile-dropdown-item');

                        profileMenuItems.forEach((item) => {
                            // Mostrar tooltip al pasar el ratón
                            item.addEventListener('mouseenter', function(event) {
                                const description = this.getAttribute('data-tooltip');
                                const rect = this.getBoundingClientRect();

                                // Configurar el tooltip
                                profileTooltip.textContent = description;
                                profileTooltip.style.backgroundColor = 'rgba(106, 13, 173, 0.9)';
                                profileTooltip.style.left = (rect.right + 15) + 'px';
                                profileTooltip.style.top = (rect.top + rect.height/2 - 20) + 'px';

                                // Configurar el color de la flecha
                                profileTooltip.style.setProperty('--tooltip-color', 'rgba(106, 13, 173, 0.9)');

                                // Mostrar el tooltip
                                profileTooltip.classList.add('visible');
                            });

                            // Ocultar tooltip al quitar el ratón
                            item.addEventListener('mouseleave', function() {
                                profileTooltip.classList.remove('visible');
                            });
                        });
                    });
                </script>
            </div>
        </div>

        <!-- Contenido central -->
        <div class="main-content">
            <!-- Notifications Header -->
            <div class="notifications-header">
                <div class="header-content">
                    <div class="header-title">
                        <h1><i class="fas fa-bell"></i> Notifications</h1>
                        <p>Stay updated with your latest activities and opportunities</p>
                    </div>
                    <div class="header-actions">
                        <button class="mark-all-read-btn" id="markAllReadBtn">
                            <i class="fas fa-check-double"></i> Mark All Read
                        </button>
                        <div class="notification-counter">
                            <span class="unread-count" id="unreadCount">7</span> unread
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notification Filters -->
            <div class="notification-filters">
                <div class="filters-header">
                    <h3><i class="fas fa-filter"></i> Filter Notifications</h3>
                </div>
                <div class="filter-tabs">
                    <button class="filter-tab active" data-filter="all">
                        <i class="fas fa-list"></i> All
                    </button>
                    <button class="filter-tab" data-filter="unread">
                        <i class="fas fa-envelope"></i> Unread
                    </button>
                    <button class="filter-tab" data-filter="important">
                        <i class="fas fa-exclamation-triangle"></i> Important
                    </button>
                    <button class="filter-tab" data-filter="messages">
                        <i class="fas fa-comment"></i> Messages
                    </button>
                    <button class="filter-tab" data-filter="opportunities">
                        <i class="fas fa-briefcase"></i> Opportunities
                    </button>
                    <button class="filter-tab" data-filter="achievements">
                        <i class="fas fa-trophy"></i> Achievements
                    </button>
                </div>
            </div>

            <!-- Notifications List -->
            <div class="notifications-container">
                <div class="notifications-list" id="notificationsList">
                    <!-- Notification Item 1 -->
                    <div class="notification-item unread high-priority" data-type="opportunity">
                        <div class="notification-icon opportunity">
                            <i class="fas fa-briefcase"></i>
                        </div>
                        <div class="notification-content">
                            <div class="notification-header">
                                <h4>New Scholarship Opportunity Available</h4>
                                <span class="notification-time">2 minutes ago</span>
                            </div>
                            <p class="notification-text">University of Sports Excellence is offering a full scholarship for talented athletes in your field. Application deadline is in 3 days.</p>
                            <div class="notification-actions">
                                <button class="btn-primary action-btn">View Details</button>
                                <button class="btn-secondary action-btn">Save for Later</button>
                            </div>
                        </div>
                        <div class="notification-options">
                            <button class="options-btn" data-notification-id="1">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Notification Item 2 -->
                    <div class="notification-item unread" data-type="message">
                        <div class="notification-icon message">
                            <i class="fas fa-comment"></i>
                        </div>
                        <div class="notification-content">
                            <div class="notification-header">
                                <h4>Message from Coach Martinez</h4>
                                <span class="notification-time">15 minutes ago</span>
                            </div>
                            <p class="notification-text">"Great performance in yesterday's training session! I'd like to discuss your progression plan for next month."</p>
                            <div class="notification-actions">
                                <button class="btn-primary action-btn">Reply</button>
                                <button class="btn-secondary action-btn">View Profile</button>
                            </div>
                        </div>
                        <div class="notification-options">
                            <button class="options-btn" data-notification-id="2">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Notification Item 3 -->
                    <div class="notification-item unread" data-type="achievement">
                        <div class="notification-icon achievement">
                            <i class="fas fa-trophy"></i>
                        </div>
                        <div class="notification-content">
                            <div class="notification-header">
                                <h4>Achievement Unlocked: Performance Milestone</h4>
                                <span class="notification-time">1 hour ago</span>
                            </div>
                            <p class="notification-text">Congratulations! You've completed 50 training sessions this month and improved your performance by 15%.</p>
                            <div class="notification-actions">
                                <button class="btn-primary action-btn">View Stats</button>
                                <button class="btn-secondary action-btn">Share Achievement</button>
                            </div>
                        </div>
                        <div class="notification-options">
                            <button class="options-btn" data-notification-id="3">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Notification Item 4 -->
                    <div class="notification-item unread" data-type="opportunity">
                        <div class="notification-icon opportunity">
                            <i class="fas fa-handshake"></i>
                        </div>
                        <div class="notification-content">
                            <div class="notification-header">
                                <h4>Team Tryout Invitation</h4>
                                <span class="notification-time">3 hours ago</span>
                            </div>
                            <p class="notification-text">Lions FC has invited you to their upcoming tryouts based on your recent performance. Tryouts are scheduled for next weekend.</p>
                            <div class="notification-actions">
                                <button class="btn-primary action-btn">Accept Invitation</button>
                                <button class="btn-secondary action-btn">Learn More</button>
                            </div>
                        </div>
                        <div class="notification-options">
                            <button class="options-btn" data-notification-id="4">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Notification Item 5 -->
                    <div class="notification-item read" data-type="system">
                        <div class="notification-icon system">
                            <i class="fas fa-cog"></i>
                        </div>
                        <div class="notification-content">
                            <div class="notification-header">
                                <h4>Profile Update Reminder</h4>
                                <span class="notification-time">1 day ago</span>
                            </div>
                            <p class="notification-text">Don't forget to update your athletic profile with your latest achievements and statistics.</p>
                            <div class="notification-actions">
                                <button class="btn-primary action-btn">Update Profile</button>
                            </div>
                        </div>
                        <div class="notification-options">
                            <button class="options-btn" data-notification-id="5">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                        </div>
                    </div>

                    <!-- More notifications... -->
                </div>
            </div>
        </div>
    </div>

    <!-- Tooltip elements -->
    <div id="feature-tooltip" class="js-tooltip"></div>
    <div id="profile-tooltip" class="js-tooltip"></div>

    <script>
        // Additional JavaScript for notifications functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Filter functionality
            const filterTabs = document.querySelectorAll('.filter-tab');
            const notifications = document.querySelectorAll('.notification-item');

            filterTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // Remove active class from all tabs
                    filterTabs.forEach(t => t.classList.remove('active'));
                    // Add active class to clicked tab
                    this.classList.add('active');

                    const filter = this.getAttribute('data-filter');
                    
                    notifications.forEach(notification => {
                        if (filter === 'all') {
                            notification.style.display = 'flex';
                        } else if (filter === 'unread') {
                            notification.style.display = notification.classList.contains('unread') ? 'flex' : 'none';
                        } else if (filter === 'important') {
                            notification.style.display = notification.classList.contains('high-priority') ? 'flex' : 'none';
                        } else {
                            const type = notification.getAttribute('data-type');
                            notification.style.display = type === filter || (filter === 'messages' && type === 'message') ? 'flex' : 'none';
                        }
                    });
                });
            });

            // Mark all as read functionality
            const markAllReadBtn = document.getElementById('markAllReadBtn');
            const unreadCount = document.getElementById('unreadCount');

            markAllReadBtn.addEventListener('click', function() {
                notifications.forEach(notification => {
                    notification.classList.remove('unread');
                    notification.classList.add('read');
                });
                unreadCount.textContent = '0';
            });

            // Individual notification actions
            notifications.forEach(notification => {
                notification.addEventListener('click', function(e) {
                    if (!e.target.closest('.notification-actions') && !e.target.closest('.notification-options')) {
                        this.classList.remove('unread');
                        this.classList.add('read');
                        
                        // Update unread count
                        const currentUnread = parseInt(unreadCount.textContent);
                        if (currentUnread > 0) {
                            unreadCount.textContent = (currentUnread - 1).toString();
                        }
                    }
                });
            });
        });
    </script>
</body>
</html>