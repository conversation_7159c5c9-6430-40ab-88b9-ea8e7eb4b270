// JavaScript principal para la plataforma Telyz

document.addEventListener('DOMContentLoaded', function() {
    // Inicializar tooltips para el menú AI Analysis
    initializeAITooltips();
    
    // Inicializar menú desplegable del perfil
    initializeProfileDropdown();
    
    // Inicializar menús desplegables
    initializeDropdowns();
    
    // Inicializar botones flotantes
    initializeFloatingButtons();
    
    // Inicializar área de creación de publicaciones
    initializePostCreation();
});

// Función para inicializar tooltips del menú AI Analysis
function initializeAITooltips() {
    const aiMenuItems = document.querySelectorAll('.ai-submenu a[data-description]');
    const tooltip = document.getElementById('feature-tooltip');
    
    if (!tooltip) return;
    
    aiMenuItems.forEach(item => {
        item.addEventListener('mouseenter', function(e) {
            const description = this.getAttribute('data-description');
            const color = this.getAttribute('data-color') || 'rgba(0, 0, 0, 0.8)';
            
            tooltip.textContent = description;
            tooltip.style.setProperty('--tooltip-color', color);
            tooltip.style.backgroundColor = color;
            
            // Posicionar el tooltip
            const rect = this.getBoundingClientRect();
            tooltip.style.left = (rect.right + 10) + 'px';
            tooltip.style.top = (rect.top + rect.height / 2 - tooltip.offsetHeight / 2) + 'px';
            
            tooltip.classList.add('visible');
        });
        
        item.addEventListener('mouseleave', function() {
            tooltip.classList.remove('visible');
        });
    });
}

// Función para inicializar el menú desplegable del perfil
function initializeProfileDropdown() {
    const toggleButton = document.getElementById('profileDropdownToggle');
    const dropdownMenu = document.getElementById('profileDropdownMenu');
    const userProfile = document.querySelector('.user-profile-sidebar');
    const profileTooltip = document.getElementById('profile-tooltip');
    
    if (!toggleButton || !dropdownMenu) return;
    
    let isMenuOpen = false;
    
    // Función para mostrar/ocultar el menú
    toggleButton.addEventListener('click', function(e) {
        e.stopPropagation();
        isMenuOpen = !isMenuOpen;
        dropdownMenu.classList.toggle('show', isMenuOpen);
        
        // Asegurarse de que los iconos sean visibles
        if (isMenuOpen) {
            setTimeout(() => {
                const icons = dropdownMenu.querySelectorAll('i');
                icons.forEach(icon => {
                    icon.style.display = 'inline-block';
                });
            }, 50);
        }
    });
    
    // Cerrar el menú al hacer clic fuera de él
    document.addEventListener('click', function(e) {
        if (!dropdownMenu.contains(e.target) && !toggleButton.contains(e.target) && !userProfile.contains(e.target)) {
            isMenuOpen = false;
            dropdownMenu.classList.remove('show');
        }
    });
    
    // Evitar que el menú se cierre al hacer clic dentro de él
    dropdownMenu.addEventListener('click', function(e) {
        e.stopPropagation();
    });
    
    // Tooltips para elementos del menú desplegable del perfil
    if (profileTooltip) {
        const dropdownItems = document.querySelectorAll('.profile-dropdown-item[data-tooltip]');
        
        dropdownItems.forEach(item => {
            item.addEventListener('mouseenter', function(e) {
                const tooltipText = this.getAttribute('data-tooltip');
                profileTooltip.textContent = tooltipText;
                
                // Posicionar el tooltip
                const rect = this.getBoundingClientRect();
                profileTooltip.style.left = (rect.left - profileTooltip.offsetWidth - 10) + 'px';
                profileTooltip.style.top = (rect.top + rect.height / 2 - profileTooltip.offsetHeight / 2) + 'px';
                
                profileTooltip.classList.add('visible');
            });
            
            item.addEventListener('mouseleave', function() {
                profileTooltip.classList.remove('visible');
            });
        });
    }
}

// Función para inicializar menús desplegables
function initializeDropdowns() {
    // Menú de deportes
    const sportsMenuItem = document.getElementById('sportsMenuItem');
    const sportsSubmenu = document.getElementById('sportsSubmenu');
    
    if (sportsMenuItem && sportsSubmenu) {
        sportsMenuItem.addEventListener('mouseenter', function() {
            sportsSubmenu.style.display = 'block';
        });
        
        sportsMenuItem.addEventListener('mouseleave', function() {
            setTimeout(() => {
                if (!sportsSubmenu.matches(':hover')) {
                    sportsSubmenu.style.display = 'none';
                }
            }, 100);
        });
        
        sportsSubmenu.addEventListener('mouseleave', function() {
            sportsSubmenu.style.display = 'none';
        });
    }
    
    // Menú AI Analysis
    const aiMenuItem = document.getElementById('aiMenuItem');
    const aiDropdownMenu = document.getElementById('aiDropdownMenu');
    
    if (aiMenuItem && aiDropdownMenu) {
        aiMenuItem.addEventListener('mouseenter', function() {
            aiDropdownMenu.style.display = 'block';
        });
        
        aiMenuItem.addEventListener('mouseleave', function() {
            setTimeout(() => {
                if (!aiDropdownMenu.matches(':hover')) {
                    aiDropdownMenu.style.display = 'none';
                }
            }, 100);
        });
        
        aiDropdownMenu.addEventListener('mouseleave', function() {
            aiDropdownMenu.style.display = 'none';
        });
    }
}

// Función para inicializar botones flotantes
function initializeFloatingButtons() {
    const chatButton = document.querySelector('.chat-button');
    const topButton = document.querySelector('.top-button');
    
    if (chatButton) {
        chatButton.addEventListener('click', function() {
            // Aquí iría la lógica para abrir el chat
            console.log('Abrir chat');
        });
    }
    
    if (topButton) {
        topButton.addEventListener('click', function() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
        
        // Mostrar/ocultar botón según scroll
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                topButton.style.display = 'flex';
            } else {
                topButton.style.display = 'none';
            }
        });
    }
}

// Función para inicializar área de creación de publicaciones
function initializePostCreation() {
    const textarea = document.querySelector('.simple-textarea');
    const postButton = document.querySelector('.post-button');
    const postIcons = document.querySelectorAll('.post-icon-btn');
    
    if (textarea) {
        textarea.addEventListener('focus', function() {
            this.style.minHeight = '80px';
        });
        
        textarea.addEventListener('blur', function() {
            if (!this.value.trim()) {
                this.style.minHeight = '50px';
            }
        });
        
        textarea.addEventListener('input', function() {
            if (postButton) {
                postButton.disabled = !this.value.trim();
            }
        });
    }
    
    if (postButton) {
        postButton.addEventListener('click', function() {
            if (textarea && textarea.value.trim()) {
                // Aquí iría la lógica para crear la publicación
                console.log('Crear publicación:', textarea.value);
                textarea.value = '';
                this.disabled = true;
            }
        });
    }
    
    // Eventos para iconos de publicación
    postIcons.forEach(icon => {
        icon.addEventListener('click', function() {
            const title = this.getAttribute('title');
            console.log('Acción:', title);
            // Aquí iría la lógica específica para cada tipo de contenido
        });
    });
}

// Función para mostrar notificaciones
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // Estilos inline para la notificación
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.padding = '15px 20px';
    notification.style.borderRadius = '8px';
    notification.style.color = 'white';
    notification.style.zIndex = '9999';
    notification.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.2)';
    notification.style.animation = 'slideInRight 0.3s ease';
    
    // Colores según el tipo
    switch (type) {
        case 'success':
            notification.style.backgroundColor = '#4CAF50';
            break;
        case 'error':
            notification.style.backgroundColor = '#f44336';
            break;
        case 'warning':
            notification.style.backgroundColor = '#ff9800';
            break;
        default:
            notification.style.backgroundColor = '#2196F3';
    }
    
    document.body.appendChild(notification);
    
    // Eliminar después de 3 segundos
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Añadir animaciones CSS para las notificaciones
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);
