// Messages Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize messages functionality
    initializeMessages();
});

function initializeMessages() {
    // Handle conversation selection
    handleConversationSelection();
    
    // Handle message sending
    handleMessageSending();
    
    // Handle filter tabs
    handleFilterTabs();
    
    // Handle search functionality
    handleSearch();
    
    // Auto-resize textarea
    handleTextareaResize();
}

// Handle conversation selection
function handleConversationSelection() {
    const conversationCards = document.querySelectorAll('.conversation-card');
    
    conversationCards.forEach(card => {
        card.addEventListener('click', function() {
            // Remove active class from all conversations
            conversationCards.forEach(conv => conv.classList.remove('active'));
            
            // Add active class to clicked conversation
            this.classList.add('active');
            
            // Update chat header and messages based on selected conversation
            const conversationId = this.dataset.conversation;
            updateChatArea(conversationId);
            
            // Remove unread indicator if present
            const unreadCount = this.querySelector('.unread-count');
            if (unreadCount) {
                unreadCount.style.display = 'none';
            }
        });
    });
}

// Handle message sending
function handleMessageSending() {
    const messageInput = document.getElementById('messageInput');
    const sendBtn = document.getElementById('sendMessageBtn');
    const chatMessages = document.getElementById('chatMessages');
    
    function sendMessage() {
        const messageText = messageInput.value.trim();
        if (messageText === '') return;
        
        // Create new message group for sent message
        const messageGroup = document.createElement('div');
        messageGroup.className = 'message-group sent';
        
        // Get current time
        const now = new Date();
        const currentTime = now.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
        
        // Create message HTML
        messageGroup.innerHTML = `
            <div class="message-content">
                <div class="message-bubble">
                    <p>${messageText}</p>
                </div>
                <div class="message-time">Today, ${currentTime}</div>
            </div>
        `;
        
        // Remove typing indicator if exists
        const typingIndicator = document.querySelector('.typing-indicator');
        if (typingIndicator) {
            typingIndicator.style.display = 'none';
        }
        
        // Add message to chat
        chatMessages.appendChild(messageGroup);
        
        // Clear input
        messageInput.value = '';
        messageInput.style.height = 'auto';
        
        // Scroll to bottom
        chatMessages.scrollTop = chatMessages.scrollHeight;
        
        // Show typing indicator
        showTypingIndicator();
        
        // Simulate response after 2 seconds
        setTimeout(() => {
            // Hide typing indicator
            if (typingIndicator) {
                typingIndicator.style.display = 'none';
            }
            
            // Send response
            simulateResponse();
        }, 2000);
    }
    
    // Send message on button click
    if (sendBtn) {
        sendBtn.addEventListener('click', sendMessage);
    }
    
    // Send message on Enter key (but allow Shift+Enter for new line)
    if (messageInput) {
        messageInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
    }
}

// Show typing indicator
function showTypingIndicator() {
    const typingIndicator = document.querySelector('.typing-indicator');
    if (typingIndicator) {
        typingIndicator.style.display = 'flex';
    }
}

// Simulate response
function simulateResponse() {
    const responses = [
        "That sounds great! Let me check my schedule.",
        "I'll get back to you on that shortly.",
        "Thanks for the update. Looking forward to it!",
        "Perfect! I'll prepare the necessary documents.",
        "Absolutely! When would be a good time for you?",
        "I've forwarded your information to our coaching staff.",
        "Your performance metrics are impressive. We'd like to schedule a meeting.",
        "Can you send me your latest training videos?",
        "Our team will be in your area next month. Would you be available for a tryout?"
    ];
    
    const randomResponse = responses[Math.floor(Math.random() * responses.length)];
    
    // Create new message group for received message
    const messageGroup = document.createElement('div');
    messageGroup.className = 'message-group received';
    
    // Get current time
    const now = new Date();
    const currentTime = now.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    
    // Get current active conversation avatar
    const activeConversation = document.querySelector('.conversation-card.active');
    let avatarSrc = 'https://i.pravatar.cc/30?img=1';
    
    if (activeConversation) {
        const conversationId = activeConversation.dataset.conversation;
        avatarSrc = `https://i.pravatar.cc/30?img=${conversationId}`;
    }
    
    // Create message HTML
    messageGroup.innerHTML = `
        <div class="message-avatar">
            <img src="${avatarSrc}" alt="Contact">
        </div>
        <div class="message-content">
            <div class="message-bubble">
                <p>${randomResponse}</p>
            </div>
            <div class="message-time">Today, ${currentTime}</div>
        </div>
    `;
    
    const chatMessages = document.getElementById('chatMessages');
    chatMessages.appendChild(messageGroup);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// Handle filter tabs
function handleFilterTabs() {
    const filterTabs = document.querySelectorAll('.tab-btn');
    
    filterTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Remove active class from all tabs
            filterTabs.forEach(t => t.classList.remove('active'));
            
            // Add active class to clicked tab
            this.classList.add('active');
            
            // Filter conversations based on selected filter
            const filter = this.dataset.filter;
            filterConversations(filter);
        });
    });
}

// Filter conversations
function filterConversations(filter) {
    const conversations = document.querySelectorAll('.conversation-card');
    
    conversations.forEach(conversation => {
        const role = conversation.querySelector('.conversation-meta .role').textContent.toLowerCase();
        
        switch(filter) {
            case 'all':
                conversation.style.display = 'flex';
                break;
            case 'unread':
                const hasUnread = conversation.querySelector('.unread-count');
                conversation.style.display = hasUnread ? 'flex' : 'none';
                break;
            case 'scouts':
                conversation.style.display = role.includes('scout') ? 'flex' : 'none';
                break;
            case 'clubs':
                conversation.style.display = role.includes('club') || role.includes('coach') ? 'flex' : 'none';
                break;
            default:
                conversation.style.display = 'flex';
        }
    });
}

// Handle search functionality
function handleSearch() {
    const searchInput = document.getElementById('searchInput');
    
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const conversations = document.querySelectorAll('.conversation-card');
            
            conversations.forEach(conversation => {
                const name = conversation.querySelector('h4').textContent.toLowerCase();
                const lastMessage = conversation.querySelector('.last-message').textContent.toLowerCase();
                const role = conversation.querySelector('.conversation-meta .role').textContent.toLowerCase();
                
                if (name.includes(searchTerm) || lastMessage.includes(searchTerm) || role.includes(searchTerm)) {
                    conversation.style.display = 'flex';
                } else {
                    conversation.style.display = 'none';
                }
            });
        });
    }
}

// Handle textarea auto-resize
function handleTextareaResize() {
    const textarea = document.getElementById('messageInput');
    
    if (textarea) {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 100) + 'px';
        });
    }
}

// Update chat area based on conversation
function updateChatArea(conversationId) {
    // This would typically fetch conversation data from an API
    // For now, we'll just update the header info
    
    const conversations = {
        '1': {
            name: 'Marcus Rodriguez',
            role: 'Scout • FC Barcelona',
            status: 'Online now',
            avatar: 'https://i.pravatar.cc/40?img=1'
        },
        '2': {
            name: 'Sarah Johnson',
            role: 'Coach • Valencia CF',
            status: 'Last seen 2h ago',
            avatar: 'https://i.pravatar.cc/40?img=2'
        },
        '3': {
            name: 'David Chen',
            role: 'Sports Agent',
            status: 'Online now',
            avatar: 'https://i.pravatar.cc/40?img=3'
        },
        '4': {
            name: 'Alex Thompson',
            role: 'Professional Player',
            status: 'Online now',
            avatar: 'https://i.pravatar.cc/40?img=4'
        }
    };
    
    const conversation = conversations[conversationId];
    if (conversation) {
        // Update chat header
        const contactAvatar = document.querySelector('.contact-avatar img');
        const contactName = document.querySelector('.contact-details h3');
        const contactRole = document.querySelector('.contact-role');
        const contactStatus = document.querySelector('.contact-status');
        const statusDot = document.querySelector('.contact-avatar .status-dot');
        
        if (contactAvatar) contactAvatar.src = conversation.avatar;
        if (contactName) contactName.textContent = conversation.name;
        if (contactRole) contactRole.textContent = conversation.role;
        if (contactStatus) contactStatus.textContent = conversation.status;
        
        // Update status dot
        if (statusDot) {
            if (conversation.status.includes('Online')) {
                statusDot.className = 'status-dot online';
            } else {
                statusDot.className = 'status-dot offline';
            }
        }
    }
}

// Initialize UI enhancements
function initializeUIEnhancements() {
    // Add smooth scrolling to chat messages
    const chatMessages = document.getElementById('chatMessages');
    if (chatMessages) {
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
}

// Call UI enhancements after DOM is loaded
document.addEventListener('DOMContentLoaded', initializeUIEnhancements);