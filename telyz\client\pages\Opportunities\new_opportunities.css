/* تنسيقات خاصة للصفحة الجديدة */

.sidebar-logo {
    text-align: center;
    padding: 20px 0;
    font-size: 24px;
    font-weight: bold;
    color: #6a0dad;
}

.user-profile-widget {
    background-color: #fff;
    border-radius: 15px;
    margin-bottom: 20px;
    text-align: center;
    padding-top: 20px;
    overflow: hidden;
}

.profile-background {
    background: linear-gradient(135deg, #6a0dad 0%, #8e44ad 100%);
    height: 60px;
}

.profile-picture {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 4px solid #fff;
    margin-top: -40px;
    box-shadow: 0 4px 10px rgba(0,0,0,0.15);
}

.user-name {
    margin-top: 10px;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.user-title {
    color: #666;
    font-size: 14px;
    margin-bottom: 15px;
}

.profile-stats {
    display: flex;
    justify-content: space-around;
    padding: 15px 0;
    border-top: 1px solid #f0f0f0;
    border-bottom: 1px solid #f0f0f0;
}

.profile-stats .stat {
    display: flex;
    flex-direction: column;
}

.profile-stats .stat span {
    color: #666;
    font-size: 12px;
}

.profile-stats .stat strong {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.view-profile-btn {
    display: block;
    padding: 15px;
    background-color: #f8f9fa;
    color: #6a0dad;
    font-weight: 600;
    text-decoration: none;
    transition: background-color 0.3s;
}

.view-profile-btn:hover {
    background-color: #e9ecef;
}

/* تعديل السايدبار الأيسر ليحتوي على محتوى السايدبار الأيمن */
.sidebar-left {
    background-color: #f0f2f5;
    padding: 10px 15px 20px 15px;
    position: fixed;
    top: 0;
    left: calc((100% - 1155px) / 2);
    height: 100vh;
    width: 270px; /* نفس العرض الأصلي للقالب */
    overflow-y: auto;
}

/* تنسيقات Your Applications */
.your-applications {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid #f0f0f0;
}

.your-applications h3 {
    font-size: 20px;
    color: #333;
    margin-bottom: 20px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 12px;
}

.application-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 15px;
    transition: background-color 0.3s;
    border: 1px solid #f0f0f0;
}

.application-item:hover {
    background-color: #f8f9fa;
}

.application-status {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    position: relative;
}

.application-status.youth {
    background-color: #3498db;
}

.application-status.academy {
    background-color: #f39c12;
}

.application-status.position {
    background-color: #e74c3c;
}

.status-dot {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: inherit;
}

.application-details h4 {
    font-size: 16px;
    color: #333;
    margin-bottom: 5px;
    font-weight: 600;
}

.application-details p {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
}

.application-badge {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.application-badge.under-review {
    background-color: #fff3cd;
    color: #856404;
}

.application-badge.interview {
    background-color: #d1ecf1;
    color: #0c5460;
}

.application-badge.not-selected {
    background-color: #f8d7da;
    color: #721c24;
}

.view-all-link {
    display: block;
    text-align: center;
    color: #6a0dad;
    text-decoration: none;
    font-weight: 500;
    margin-top: 15px;
    padding: 10px;
    border-radius: 8px;
    transition: background-color 0.3s;
}

.view-all-link:hover {
    background-color: rgba(106, 13, 173, 0.1);
}

/* تنسيقات Create Alert */
.create-alert {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid #f0f0f0;
}

.create-alert h3 {
    font-size: 20px;
    color: #333;
    margin-bottom: 10px;
    font-weight: 600;
}

.create-alert p {
    font-size: 14px;
    color: #666;
    margin-bottom: 20px;
}

.alert-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.alert-input {
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.alert-input:focus {
    outline: none;
    border-color: #6a0dad;
}

.create-alert-button {
    background: linear-gradient(135deg, #6a0dad 0%, #8e44ad 100%);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.3s, box-shadow 0.3s;
}

.create-alert-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(106, 13, 173, 0.3);
}

/* استخدام نفس إعدادات القالب الأصلي */
.container {
    display: flex;
    min-height: 100vh;
    max-width: 1155px; /* نفس العرض الأصلي للقالب */
    margin: 0 auto;
    background-color: #f0f2f5;
}

/* تعديل المحتوى الرئيسي */
.main-content {
    flex: 1;
    padding: 20px;
    background-color: #f0f2f5; /* نفس خلفية القالب الأصلي */
    max-width: calc(100% - 270px); /* نفس حساب العرض الأصلي */
    width: 100%;
}

/* تنسيقات الفرص */
.opportunities-header {
    background: linear-gradient(135deg, #6a0dad 0%, #8e44ad 100%);
    border-radius: 20px;
    padding: 40px;
    text-align: center;
    color: white;
    margin-bottom: 30px;
}

/* إضافة خلفية بيضاء للأقسام */
.search-container,
.filter-tabs,
.filter-options,
.featured-opportunities,
.recent-opportunities {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.opportunities-header h1 {
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 10px;
}

.opportunities-subtitle {
    font-size: 18px;
    opacity: 0.9;
    margin-bottom: 30px;
}

.opportunities-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
}

.stat-value {
    display: block;
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 14px;
    opacity: 0.8;
}

/* تنسيقات البحث والفلاتر */
.search-container {
    display: flex;
    gap: 15px;
    align-items: center;
}

.search-input-container {
    flex: 1;
    position: relative;
}

.search-input-container i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}

.search-input-container input {
    width: 100%;
    padding: 15px 15px 15px 45px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 16px;
    transition: border-color 0.3s;
}

.search-input-container input:focus {
    outline: none;
    border-color: #6a0dad;
}

.search-button {
    background: linear-gradient(135deg, #6a0dad 0%, #8e44ad 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 10px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.3s, box-shadow 0.3s;
}

.search-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(106, 13, 173, 0.3);
}

/* تنسيقات التبويبات */

.filter-tabs-row {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.filter-tabs-row:last-child {
    margin-bottom: 0;
}

.filter-tab {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border: 2px solid transparent;
    color: #666;
    padding: 12px 24px;
    border-radius: 30px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.filter-tab::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: left 0.3s ease;
    z-index: -1;
}

.filter-tab:hover {
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

.filter-tab:hover::before {
    left: 0;
}

.filter-tab.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.filter-tab.active::before {
    left: 0;
}

/* تنسيقات خيارات الفلترة */

.filter-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

.filter-group label {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.filter-group select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 14px;
    background: white;
    cursor: pointer;
    transition: border-color 0.3s;
}

.filter-group select:focus {
    outline: none;
    border-color: #6a0dad;
}

.filter-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
}

.reset-button, .apply-button {
    padding: 12px 25px;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    gap: 8px;
}

.reset-button {
    background: #f8f9fa;
    border: 2px solid #e1e5e9;
    color: #666;
}

.reset-button:hover {
    background: #e9ecef;
    border-color: #6a0dad;
}

.apply-button {
    background: linear-gradient(135deg, #6a0dad 0%, #8e44ad 100%);
    border: none;
    color: white;
}

.apply-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(106, 13, 173, 0.3);
}

/* تنسيقات الفرص المميزة */

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.section-header h2 {
    font-size: 24px;
    font-weight: 700;
    color: #333;
}

.view-all {
    color: #6a0dad;
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: color 0.3s;
}

.view-all:hover {
    color: #8e44ad;
}

.sort-options select {
    padding: 8px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    background: white;
    cursor: pointer;
}

/* تنسيقات البطاقات المميزة */
.featured-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 25px;
    margin-bottom: 25px;
}

.featured-card {
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 25px;
    overflow: hidden;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
}

.featured-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.featured-card:hover::before {
    opacity: 1;
}

.featured-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 60px rgba(102, 126, 234, 0.2);
}

.opportunity-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    z-index: 2;
}

.opportunity-badge.premium {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #333;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
    animation: pulse 2s infinite;
}

.opportunity-badge.scholarship {
    background: linear-gradient(135deg, #3498db 0%, #5dade2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.4);
}

.opportunity-badge.trial {
    background: linear-gradient(135deg, #2ecc71 0%, #58d68d 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(46, 204, 113, 0.4);
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.card-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.card-logo {
    position: absolute;
    bottom: 15px;
    left: 15px;
    width: 50px;
    height: 50px;
    background: white;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.card-logo img {
    width: 35px;
    height: 35px;
    object-fit: contain;
}

.card-content {
    padding: 25px;
}

.card-content h3 {
    font-size: 20px;
    font-weight: 700;
    color: #333;
    margin-bottom: 8px;
}

.card-organization {
    font-size: 16px;
    color: #6a0dad;
    font-weight: 600;
    margin-bottom: 8px;
}

.card-location {
    font-size: 14px;
    color: #666;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.card-tags {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.tag {
    background: #f8f9fa;
    color: #666;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
}

.card-description {
    font-size: 14px;
    color: #666;
    line-height: 1.6;
    margin-bottom: 20px;
}

.card-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.view-details-btn {
    flex: 1;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 14px 24px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.view-details-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.view-details-btn:hover::before {
    left: 100%;
}

.view-details-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.save-btn {
    background: #f8f9fa;
    border: 2px solid #e1e5e9;
    color: #666;
    padding: 12px;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s;
}

.save-btn:hover {
    background: #e9ecef;
    border-color: #6a0dad;
    color: #6a0dad;
}

/* تنسيقات قائمة الفرص الأخيرة */
.opportunities-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 25px;
    margin-bottom: 25px;
}

.opportunity-card {
    background: white;
    border: 1px solid #f0f0f0;
    border-radius: 15px;
    padding: 25px;
    transition: transform 0.3s, box-shadow 0.3s;
}

.opportunity-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.organization-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.org-logo {
    width: 50px;
    height: 50px;
    object-fit: contain;
    border-radius: 8px;
    border: 1px solid #f0f0f0;
    padding: 5px;
}

.org-details h3 {
    font-size: 18px;
    font-weight: 700;
    color: #333;
    margin-bottom: 5px;
}

.org-details p {
    font-size: 14px;
    color: #6a0dad;
    font-weight: 600;
}

.opportunity-type {
    display: flex;
    align-items: center;
}

.type-badge {
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
}

.type-badge.contract {
    background: linear-gradient(135deg, #e74c3c 0%, #ec7063 100%);
    color: white;
}

.type-badge.coaching {
    background: linear-gradient(135deg, #f39c12 0%, #f7dc6f 100%);
    color: white;
}

.card-body {
    margin-bottom: 20px;
}

.card-body .card-location {
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.card-body .card-description {
    font-size: 14px;
    color: #666;
    line-height: 1.6;
}

.card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 20px;
    border-top: 1px solid #f0f0f0;
}

.deadline {
    font-size: 14px;
    color: #666;
    display: flex;
    align-items: center;
    gap: 8px;
}

.apply-now-btn {
    background: linear-gradient(135deg, #6a0dad 0%, #8e44ad 100%);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.3s, box-shadow 0.3s;
}

.apply-now-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(106, 13, 173, 0.3);
}

/* تنسيقات زر Load More */
.load-more {
    text-align: center;
    margin-top: 30px;
}

.load-more-btn {
    background: #f8f9fa;
    border: 2px solid #e1e5e9;
    color: #666;
    padding: 15px 30px;
    border-radius: 10px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    display: inline-flex;
    align-items: center;
    gap: 10px;
}

.load-more-btn:hover {
    background: #e9ecef;
    border-color: #6a0dad;
    color: #6a0dad;
    transform: translateY(-2px);
}

/* تنسيقات responsive */
@media (max-width: 768px) {
    .container {
        flex-direction: column;
        max-width: 100%;
    }

    .sidebar-left {
        width: 100%;
        height: auto;
        position: relative;
        order: 2;
    }

    .main-content {
        width: 100%;
        max-width: 100%;
        order: 1;
        margin: 10px;
        padding: 15px;
    }

    .featured-cards {
        grid-template-columns: 1fr;
    }

    .opportunities-stats {
        gap: 20px;
    }

    .filter-row {
        grid-template-columns: 1fr;
    }

    .search-container {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-actions {
        justify-content: center;
    }

    .card-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
}
