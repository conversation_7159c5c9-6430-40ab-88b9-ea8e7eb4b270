/**
 * Authentication Controller
 * 
 * Controller functions for authentication.
 */

const User = require('../../models/User');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const config = require('../../config');

/**
 * Login user
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response
 */
exports.login = async (req, res) => {
  try {
    const { email, password } = req.body;
    
    // Find user
    const user = await User.findOne({ email }).select('+password');
    
    if (!user) {
      return res.status(400).json({ message: 'Invalid credentials' });
    }
    
    // Check if user is active
    if (!user.isActive) {
      return res.status(401).json({ message: 'Account is inactive' });
    }
    
    // Check password
    const isMatch = await bcrypt.compare(password, user.password);
    
    if (!isMatch) {
      return res.status(400).json({ message: 'Invalid credentials' });
    }
    
    // Generate JWT token
    const payload = {
      user: {
        id: user.id,
        role: user.role,
      },
    };
    
    const token = jwt.sign(payload, config.jwt.secret, {
      expiresIn: config.jwt.expiresIn,
    });
    
    // Generate refresh token
    const refreshToken = jwt.sign(
      { userId: user.id },
      config.jwt.refreshSecret,
      { expiresIn: config.jwt.refreshExpiresIn }
    );
    
    return res.status(200).json({
      token,
      refreshToken,
      user: {
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        role: user.role,
        sport: user.sport,
        isVerified: user.isVerified,
        isPremium: user.isPremium,
      },
    });
  } catch (error) {
    console.error('Login error:', error);
    return res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Logout user
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response
 */
exports.logout = async (req, res) => {
  try {
    // In a real implementation, you would invalidate the token
    // For now, we'll just return a success message
    
    return res.status(200).json({ message: 'Logout successful' });
  } catch (error) {
    console.error('Logout error:', error);
    return res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Get current user
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response
 */
exports.getCurrentUser = async (req, res) => {
  try {
    // User is already set in req.user by the authenticate middleware
    return res.status(200).json(req.user);
  } catch (error) {
    console.error('Get current user error:', error);
    return res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Refresh token
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response
 */
exports.refreshToken = async (req, res) => {
  try {
    const { refreshToken } = req.body;
    
    if (!refreshToken) {
      return res.status(400).json({ message: 'Refresh token is required' });
    }
    
    // Verify refresh token
    const decoded = jwt.verify(refreshToken, config.jwt.refreshSecret);
    
    // Find user
    const user = await User.findById(decoded.userId);
    
    if (!user) {
      return res.status(401).json({ message: 'Invalid refresh token' });
    }
    
    // Check if user is active
    if (!user.isActive) {
      return res.status(401).json({ message: 'Account is inactive' });
    }
    
    // Generate new JWT token
    const payload = {
      user: {
        id: user.id,
        role: user.role,
      },
    };
    
    const token = jwt.sign(payload, config.jwt.secret, {
      expiresIn: config.jwt.expiresIn,
    });
    
    return res.status(200).json({
      token,
    });
  } catch (error) {
    console.error('Refresh token error:', error);
    return res.status(401).json({ message: 'Invalid refresh token' });
  }
};

/**
 * Google OAuth login
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response
 */
exports.googleLogin = async (req, res) => {
  try {
    const { idToken } = req.body;
    
    // In a real implementation, you would verify the Google ID token
    // and create or update the user in your database
    
    return res.status(200).json({ message: 'Google login endpoint' });
  } catch (error) {
    console.error('Google login error:', error);
    return res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Facebook OAuth login
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response
 */
exports.facebookLogin = async (req, res) => {
  try {
    const { accessToken } = req.body;
    
    // In a real implementation, you would verify the Facebook access token
    // and create or update the user in your database
    
    return res.status(200).json({ message: 'Facebook login endpoint' });
  } catch (error) {
    console.error('Facebook login error:', error);
    return res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Apple OAuth login
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} JSON response
 */
exports.appleLogin = async (req, res) => {
  try {
    const { identityToken } = req.body;
    
    // In a real implementation, you would verify the Apple identity token
    // and create or update the user in your database
    
    return res.status(200).json({ message: 'Apple login endpoint' });
  } catch (error) {
    console.error('Apple login error:', error);
    return res.status(500).json({ message: 'Server error' });
  }
};
