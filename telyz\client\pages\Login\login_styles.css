/* Login Page Styles for Telez - Modern Design */

:root {
    --primary-color: #7b68ee;
    --primary-light: #9370db;
    --primary-dark: #5d43a6;
    --primary-gradient: linear-gradient(135deg, #7b68ee, #9370db);
    --secondary-color: #4ecdc4;
    --accent-color: #ff7f50;
    --accent-light: #ffa07a;
    --accent-dark: #ff6347;
    --success-color: #50c878;
    --warning-color: #ffaa33;
    --error-color: #ff6b6b;
    --text-color: #2d3748;
    --text-light: #718096;
    --background-color: #f8f9fc;
    --white: #ffffff;
    --border-color: #e2e8f0;
    --shadow-color: rgba(0, 0, 0, 0.05);
    --card-shadow: 0 12px 35px rgba(0, 0, 0, 0.1);
    --google-color: #DB4437;
    --facebook-color: #4267B2;
    --apple-color: #000000;
    --twitter-color: #1DA1F2;
    --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

/* General Styles */
body {
    margin: 0;
    font-family: 'Roboto', sans-serif;
    color: var(--text-color);
    background-color: var(--background-color);
    background-image: linear-gradient(135deg, #f5f7fa 0%, #e4e7f0 100%);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.login-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    max-width: 1000px;
    margin: 40px auto;
    padding: 0 20px;
    flex: 1;
}

@media (min-width: 992px) {
    .login-container {
        flex-direction: row;
        align-items: flex-start;
        justify-content: space-between;
    }
}

/* Branding Section */
.login-branding {
    text-align: center;
    margin-bottom: 30px;
}

.login-logo {
    display: inline-flex;
    margin-bottom: 20px;
}

.logo-container {
    width: 70px;
    height: 70px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 36px;
    font-weight: bold;
    box-shadow: 0 8px 20px rgba(123, 104, 238, 0.4);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.logo-container:hover {
    transform: rotate(5deg) scale(1.05);
    box-shadow: 0 12px 30px rgba(123, 104, 238, 0.5);
}

.login-title {
    font-size: 32px;
    font-weight: 700;
    color: var(--primary-color);
    margin: 10px 0 5px;
}

.login-tagline {
    font-size: 16px;
    color: var(--text-light);
    margin: 0 0 20px;
}

@media (min-width: 992px) {
    .login-branding {
        flex: 1;
        padding-right: 40px;
        text-align: left;
    }
}

/* Form Container */
.login-form-container {
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    box-shadow: var(--card-shadow);
    width: 100%;
    max-width: 500px;
    overflow: hidden;
    transition: var(--transition);
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(12px);
}

/* Tabs */
.login-tabs {
    display: flex;
    padding: 10px;
    background-color: #f9fafc;
    border-bottom: 1px solid var(--border-color);
}

.login-tab-btn {
    flex: 1;
    background: none;
    border: none;
    padding: 15px 20px;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-light);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    border-radius: 10px;
    z-index: 1;
    overflow: hidden;
}

.login-tab-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(123, 104, 238, 0.1);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
    z-index: -1;
    border-radius: 10px;
}

.login-tab-btn:hover::before {
    transform: scaleX(1);
}

.login-tab-btn.active {
    color: var(--primary-color);
    background-color: rgba(123, 104, 238, 0.1);
    box-shadow: 0 4px 10px rgba(123, 104, 238, 0.2);
}

.login-tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 3px;
    background-image: var(--primary-gradient);
    border-radius: 3px 3px 0 0;
    transform: none;
    left: 0;
}

/* Form Sections */
.login-form-section {
    padding: 30px;
    display: none;
}

.login-form-section.active {
    display: block;
}

.login-form-section h2 {
    font-size: 24px;
    font-weight: 700;
    margin: 0 0 5px;
    color: var(--text-color);
}

.login-subtitle {
    font-size: 14px;
    color: var(--text-light);
    margin: 0 0 25px;
}

/* Form Elements */
.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: flex;
    gap: 15px;
}

.form-row .form-group {
    flex: 1;
}

label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
    color: var(--text-color);
}

input[type="text"],
input[type="email"],
input[type="password"],
select {
    width: 100%;
    padding: 12px 15px; /* Adjusted padding */
    border: 1px solid var(--border-color); /* Unified border */
    border-radius: 8px; /* Consistent border radius */
    font-size: 16px;
    transition: all 0.3s ease;
    background-color: var(--background-color); /* Consistent background */
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
    box-sizing: border-box; /* Include padding and border in element's total width and height */
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
select:focus {
    border-color: var(--primary-color); /* Primary color on focus */
    outline: none;
    box-shadow: 0 0 0 3px rgba(106, 13, 173, 0.1); /* Consistent focus shadow */
    background-color: var(--white); /* White background on focus */
}

.input-with-icon {
    position: relative;
}

.input-with-icon i {
    position: absolute;
    left: 15px; /* Adjusted icon position */
    top: 50%;
    transform: translateY(-50%);
    color: var(--primary-color);
    font-size: 16px;
    transition: all 0.3s ease;
    pointer-events: none; /* Prevent icon from interfering with input clicks */
}

.input-with-icon input {
    padding-left: 45px;
    transition: var(--transition);
    border: 1px solid var(--border-color);
    background-color: rgba(255, 255, 255, 0.8);
}

.input-with-icon input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(123, 104, 238, 0.15);
    background-color: white;
    transform: translateY(-1px);
}

/* Adjust password toggle icon specifically */
.input-with-icon .toggle-password {
    left: auto;
    right: 15px; /* Adjusted icon position */
    cursor: pointer; /* Make it clickable */
    color: var(--text-light);
    transition: color 0.3s ease;
    pointer-events: auto; /* Re-enable pointer events for clicking */
}

.input-with-icon .toggle-password:hover {
    color: var(--primary-color);
}

/* Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    font-size: 14px;
}

.remember-me {
    display: flex;
    align-items: center;
}

.remember-me input {
    margin-right: 8px;
}

.forgot-password {
    color: var(--primary-color);
    text-decoration: none;
}

.forgot-password:hover {
    text-decoration: underline;
}

/* Buttons */
.login-button,
.register-button {
    width: 100%;
    padding: 16px;
    background-image: var(--primary-gradient);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 0 6px 15px rgba(123, 104, 238, 0.3);
    position: relative;
    overflow: hidden;
    z-index: 1;
    letter-spacing: 0.5px;
}

.login-button:hover,
.register-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(123, 104, 238, 0.4);
}

.login-button:active,
.register-button:active {
    transform: translateY(1px);
    box-shadow: 0 4px 10px rgba(123, 104, 238, 0.3);
}

/* Social Login */
.social-login,
.social-register {
    margin-top: 25px;
    text-align: center;
}

.social-login p,
.social-register p {
    font-size: 14px;
    color: var(--text-light);
    margin-bottom: 15px;
    position: relative;
}

.social-login p::before,
.social-login p::after,
.social-register p::before,
.social-register p::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 70px;
    height: 1px;
    background-color: var(--border-color);
}

.social-login p::before,
.social-register p::before {
    left: 0;
}

.social-login p::after,
.social-register p::after {
    right: 0;
}

.social-buttons {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.social-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px 20px;
    border: 1px solid var(--border-color);
    border-radius: 12px;
    background-color: white;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.social-btn i {
    margin-right: 10px;
    font-size: 18px;
    transition: transform 0.3s;
}

.social-btn.google {
    background-color: rgba(219, 68, 55, 0.1);
    color: var(--google-color);
}

.social-btn.facebook {
    background-color: rgba(66, 103, 178, 0.1);
    color: var(--facebook-color);
}

.social-btn.apple {
    background-color: rgba(0, 0, 0, 0.1);
    color: var(--apple-color);
}

.social-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.social-btn:hover i {
    transform: scale(1.1);
}

.social-btn:active {
    transform: translateY(1px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Registration Steps */
.registration-steps {
    position: relative;
    margin-bottom: 20px;
}

.step-indicator {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
    position: relative;
    z-index: 1;
    padding: 0 20px;
}

.step-indicator::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 10%;
    width: 80%;
    height: 3px;
    background: linear-gradient(to right, var(--primary-color), var(--primary-light));
    background-size: 200% 100%;
    background-position: left;
    z-index: -1;
    border-radius: 3px;
    transition: background-position 0.5s ease;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: white;
    border: 2px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--text-light);
    transition: all 0.3s ease;
}

.step.active .step-number {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    box-shadow: 0 0 0 5px rgba(106, 13, 173, 0.1);
}

.step.completed .step-number {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: white;
}

.step.completed .step-number::after {
    content: '\f00c';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
}

.step-label {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-light);
    transition: all 0.3s ease;
}

.step.active .step-label {
    color: var(--primary-color);
    font-weight: 600;
}

.step.completed .step-label {
    color: var(--success-color);
}

.registration-step {
    display: none;
    animation: fadeIn 0.5s ease;
}

.registration-step.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.step-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--text-color);
    text-align: center;
}

.step-navigation {
    display: flex;
    justify-content: space-between;
    margin-top: 25px;
}

.prev-step-btn,
.next-step-btn {
    padding: 12px 20px;
    border-radius: 30px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.prev-step-btn {
    background-color: white;
    border: 2px solid var(--border-color);
    color: var(--text-color);
}

.prev-step-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.next-step-btn {
    background-image: var(--primary-gradient);
    color: white;
    border: none;
    box-shadow: 0 4px 15px rgba(106, 13, 173, 0.3);
}

.next-step-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(106, 13, 173, 0.4);
}

/* Level Selection */
.level-selection {
    margin-bottom: 20px;
}

.level-options {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.level-option {
    width: 100%;
}

.level-option input[type="radio"] {
    display: none;
}

.level-option label {
    display: flex;
    flex-direction: column;
    padding: 20px;
    border: 2px solid var(--border-color);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s;
    background-color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
    height: 100%;
}

.level-option label i {
    font-size: 24px;
    margin-bottom: 10px;
    color: var(--text-light);
    transition: all 0.3s;
}

.level-option label span {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 5px;
    transition: all 0.3s;
}

.level-description {
    font-size: 14px;
    color: var(--text-light);
    margin: 5px 0 0;
}

.level-option input[type="radio"]:checked + label {
    border-color: var(--primary-color);
    background-color: rgba(106, 13, 173, 0.05);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(106, 13, 173, 0.1);
}

.level-option input[type="radio"]:checked + label i {
    color: var(--primary-color);
}

.level-option input[type="radio"]:checked + label span {
    color: var(--primary-color);
}

.level-option:hover label {
    border-color: var(--primary-light);
    transform: translateY(-2px);
}

/* Sports Fields */
.sports-fields {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.sport-field, .level-field {
    flex: 1;
}

/* Club Name Field */
.club-name-field {
    margin-bottom: 20px;
}

.club-name-field input {
    font-weight: 500;
    border-color: var(--primary-light);
}

/* Nickname Field */
.nickname-field {
    margin-bottom: 20px;
}

.optional-label {
    font-size: 12px;
    color: var(--text-light);
    font-weight: normal;
    font-style: italic;
}

/* User Type Selection */
.user-type-selection {
    margin-bottom: 30px;
}

.user-type-options {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    justify-content: center;
}

.user-type-option {
    flex: 1;
    min-width: 90px;
    max-width: 120px;
}

.user-type-option input[type="radio"] {
    display: none;
}

.user-type-option label {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px;
    border: 2px solid var(--border-color);
    border-radius: 12px;
    cursor: pointer;
    transition: var(--transition);
    background-color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.user-type-option label i {
    font-size: 28px;
    margin-bottom: 10px; /* Reduced margin below icon */
    color: var(--primary-color);
    opacity: 0.7;
    transition: all 0.3s;
}

.user-type-option input[type="radio"]:checked + label {
    border-color: var(--primary-color);
    background-color: rgba(106, 13, 173, 0.05);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(106, 13, 173, 0.1);
}

.user-type-option input[type="radio"]:checked + label i {
    color: var(--primary-color);
    opacity: 1;
    transform: scale(1.1);
}

.user-type-option input[type="radio"]:checked + label span {
    color: var(--primary-color);
    font-weight: 600;
}

.user-type-option:hover label {
    border-color: var(--primary-light);
    transform: translateY(-2px);
}

.other-role-input {
    width: 100%;
    margin-top: 15px;
    padding: 0 10px;
}

.other-role-input input {
    width: 100%;
    padding: 14px 18px;
    border: 2px solid var(--primary-light);
    border-radius: 12px;
    font-size: 15px;
    background-color: white;
    box-shadow: 0 2px 8px rgba(106, 13, 173, 0.1);
    transition: all 0.3s;
}

.other-role-input input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(106, 13, 173, 0.1);
    outline: none;
}

/* Footer */
.login-footer {
    text-align: center;
    padding: 25px 20px;
    margin-top: auto;
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    border-radius: 20px 20px 0 0;
    box-shadow: 0 -5px 15px rgba(0, 0, 0, 0.03);
}

.footer-links {
    margin-bottom: 10px;
}

.footer-links a {
    color: var(--text-light);
    text-decoration: none;
    margin: 0 10px;
    font-size: 14px;
}

.footer-links a:hover {
    color: var(--primary-color);
}

.footer-copyright {
    font-size: 12px;
    color: var(--text-light);
}

/* Password Strength Meter */
.password-strength {
    margin-top: 12px;
    padding: 10px;
    background-color: #f9fafc;
    border-radius: 10px;
}

.strength-meter {
    display: flex;
    gap: 6px;
    margin-bottom: 8px;
}

.strength-segment {
    height: 6px;
    flex: 1;
    background-color: var(--border-color);
    border-radius: 3px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.strength-segment::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 0;
    background-image: linear-gradient(to right, var(--error-color), var(--warning-color), var(--success-color));
    transition: width 0.5s ease;
}

.strength-segment.weak::before {
    width: 100%;
    background-image: linear-gradient(to right, var(--error-color), var(--error-color));
}

.strength-segment.medium::before {
    width: 100%;
    background-image: linear-gradient(to right, var(--warning-color), var(--warning-color));
}

.strength-segment.strong::before {
    width: 100%;
    background-image: linear-gradient(to right, var(--success-color), var(--success-color));
}

.strength-text {
    font-size: 13px;
    color: var(--text-light);
    font-weight: 500;
    display: flex;
    align-items: center;
}

.strength-text::before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
    background-color: var(--border-color);
}

.strength-text.weak::before {
    background-color: var(--error-color);
}

.strength-text.medium::before {
    background-color: var(--warning-color);
}

.strength_text.strong::before {
    background-color: var(--success-color);
}

/* Terms and Privacy */
.terms-privacy {
    display: flex;
    align-items: flex-start;
}

.terms-privacy input {
    margin-right: 10px;
    margin-top: 3px;
}

.terms-privacy label {
    font-size: 13px;
    margin: 0;
}

.terms-privacy a {
    color: var(--primary-color);
    text-decoration: none;
}

.terms-privacy a:hover {
    text-decoration: underline;
}

/* Basic Responsive adjustments */
@media (max-width: 768px) {
    .login-container {
        padding: 20px;
        margin: 20px auto;
    }
    
    .login-form-container {
        max-width: 100%;
    }
    
    .login-branding {
        margin-bottom: 20px;
    }
    
    .login-title {
        font-size: 28px;
    }
    
    .login-form-section {
        padding: 20px;
    }
    
    .form-row {
        flex-direction: column;
        gap: 10px;
    }
    
    .social-buttons {
        flex-direction: column;
    }
    
    .social-btn {
        width: 100%;
    }
    
    .step-indicator {
        flex-wrap: wrap;
        justify-content: center;
        gap: 10px;
    }
    
    .step {
        flex: 0 0 calc(33.333% - 10px);
    }
    
    .user-type-options {
        flex-wrap: wrap;
    }
    
    .user-type-option {
        flex: 0 0 calc(50% - 10px);
    }
}