/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.checkout-container {
    min-height: 100vh;
    background: #f8fafc;
}

/* Header */
.checkout-header {
    background: white;
    border-bottom: 1px solid #e2e8f0;
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2rem;
}

.logo {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.back-btn {
    font-size: 1.2rem;
    color: #6b7280;
    cursor: pointer;
    transition: color 0.3s ease;
}

.back-btn:hover {
    color: #6a0dad;
}

.logo h1 {
    font-size: 1.8rem;
    font-weight: 700;
    color: #6a0dad;
}

.security-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #10b981;
    font-size: 0.9rem;
    font-weight: 500;
}

.security-badge i {
    font-size: 1rem;
}

/* Progress Bar */
.progress-container {
    background: white;
    border-bottom: 1px solid #e2e8f0;
    padding: 2rem 0;
}

.progress-bar {
    max-width: 800px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    position: relative;
    padding: 0 2rem;
}

.progress-bar::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 10%;
    right: 10%;
    height: 2px;
    background: #e2e8f0;
    z-index: 1;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 20px;
    left: 10%;
    width: 0%;
    height: 2px;
    background: #6a0dad;
    z-index: 2;
    transition: width 0.5s ease;
}

.progress-bar.step-1::after { width: 0%; }
.progress-bar.step-2::after { width: 50%; }
.progress-bar.step-3::after { width: 100%; }

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 3;
    position: relative;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: #6b7280;
    transition: all 0.3s ease;
    margin-bottom: 0.5rem;
}

.progress-step.active .step-number {
    background: #6a0dad;
    color: white;
}

.progress-step.completed .step-number {
    background: #10b981;
    color: white;
}

.step-label {
    font-size: 0.9rem;
    color: #6b7280;
    font-weight: 500;
    text-align: center;
}

.progress-step.active .step-label {
    color: #6a0dad;
    font-weight: 600;
}

/* Main Content */
.checkout-main {
    max-width: 1200px;
    margin: 0 auto;
    padding: 3rem 2rem;
}

.step-content {
    display: none;
    animation: fadeIn 0.5s ease-in-out;
}

.step-content.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.step-container {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 3rem;
}

.step-left {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.step-right {
    position: sticky;
    top: 120px;
    height: fit-content;
}

.step-header {
    margin-bottom: 2rem;
}

.step-header h2 {
    font-size: 1.8rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.step-header p {
    color: #6b7280;
    font-size: 1rem;
}

/* Step 1 - Plan Confirmation */
.selected-plan {
    margin-bottom: 2rem;
}

.plan-card {
    border: 2px solid #6a0dad;
    border-radius: 12px;
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(106, 13, 173, 0.05), rgba(106, 13, 173, 0.1));
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.plan-header {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.plan-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #6a0dad, #8e44ad);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.plan-info h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.2rem;
}

.plan-info p {
    color: #6b7280;
    font-size: 0.9rem;
}

.plan-price {
    display: flex;
    align-items: flex-end;
    gap: 0.2rem;
}

.currency {
    font-size: 1.2rem;
    font-weight: 600;
    color: #6a0dad;
}

.amount {
    font-size: 2rem;
    font-weight: 800;
    color: #1f2937;
}

.period {
    font-size: 1rem;
    color: #6b7280;
    margin-bottom: 0.2rem;
}

/* Billing Cycle */
.billing-cycle {
    margin-bottom: 2rem;
}

.billing-cycle h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
}

.cycle-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.cycle-option {
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.cycle-option:hover {
    border-color: #6a0dad;
    background: rgba(106, 13, 173, 0.02);
}

.cycle-option.active {
    border-color: #6a0dad;
    background: rgba(106, 13, 173, 0.05);
}

.cycle-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.cycle-name {
    font-weight: 600;
    color: #1f2937;
}

.cycle-price {
    color: #6a0dad;
    font-weight: 600;
}

.discount-badge {
    background: #10b981;
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-left: 0.5rem;
}

.cycle-description {
    color: #6b7280;
    font-size: 0.9rem;
}

/* Add-ons */
.addons-section h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
}

.addons-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.addon-item {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 1rem;
    transition: all 0.3s ease;
}

.addon-item:hover {
    border-color: #6a0dad;
    background: rgba(106, 13, 173, 0.02);
}

.addon-checkbox {
    display: flex;
    align-items: center;
}

.addon-checkbox input[type="checkbox"] {
    display: none;
}

.addon-checkbox label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    cursor: pointer;
}

.addon-checkbox input[type="checkbox"]:checked + label {
    color: #6a0dad;
}

.addon-checkbox label::before {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    margin-right: 1rem;
    transition: all 0.3s ease;
}

.addon-checkbox input[type="checkbox"]:checked + label::before {
    background: #6a0dad;
    border-color: #6a0dad;
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='m13.854 3.646-7.5 7.5a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6 10.293l7.146-7.147a.5.5 0 0 1 .708.708z'/%3e%3c/svg%3e");
}

.addon-info {
    flex: 1;
}

.addon-name {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.2rem;
}

.addon-description {
    color: #6b7280;
    font-size: 0.9rem;
}

.addon-price {
    color: #6a0dad;
    font-weight: 600;
    font-size: 0.9rem;
}

/* Step 2 - Payment */
.payment-methods h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
}

.payment-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
}

.payment-option {
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.payment-option:hover {
    border-color: #6a0dad;
    background: rgba(106, 13, 173, 0.02);
}

.payment-option.active {
    border-color: #6a0dad;
    background: rgba(106, 13, 173, 0.05);
}

.payment-header {
    display: flex;
    align-items: center;
    gap: 0.8rem;
}

.payment-header i {
    font-size: 1.2rem;
    color: #6a0dad;
}

.payment-header span {
    font-weight: 600;
    color: #1f2937;
}

.card-types {
    display: flex;
    gap: 0.5rem;
}

.card-types img {
    width: 32px;
    height: 20px;
    object-fit: contain;
}

/* Payment Forms */
.payment-form {
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #d1d5db;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #6a0dad;
    box-shadow: 0 0 0 3px rgba(106, 13, 173, 0.1);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.card-input {
    position: relative;
    border: 2px solid #d1d5db;
    border-radius: 8px;
    padding: 0.75rem;
    transition: border-color 0.3s ease;
}

.card-input:focus-within {
    border-color: #6a0dad;
    box-shadow: 0 0 0 3px rgba(106, 13, 173, 0.1);
}

.card-type-icon {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    width: 32px;
    height: 20px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

/* Stripe Elements Styling */
.StripeElement {
    height: 20px;
    background: transparent;
}

.StripeElement--focus {
    box-shadow: none;
}

.StripeElement--invalid {
    border-color: #ef4444;
}

/* Billing Address */
.billing-address h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
}

/* PayPal */
.paypal-container {
    padding: 2rem;
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    text-align: center;
    background: #f9fafb;
}

/* Order Summary */
.order-summary {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.order-summary h3 {
    font-size: 1.3rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1.5rem;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
}

.summary-item span:first-child {
    color: #6b7280;
}

.summary-item span:last-child {
    font-weight: 600;
    color: #1f2937;
}

.summary-addons {
    border-left: 3px solid #e5e7eb;
    padding-left: 1rem;
    margin: 1rem 0;
}

.addon-summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.3rem 0;
    font-size: 0.9rem;
}

.addon-summary-item span:first-child {
    color: #6b7280;
}

.addon-summary-item span:last-child {
    color: #6a0dad;
    font-weight: 500;
}

.summary-discount {
    color: #10b981;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
}

.summary-divider {
    height: 1px;
    background: #e5e7eb;
    margin: 1rem 0;
}

.summary-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    font-size: 1.1rem;
    font-weight: 700;
    color: #1f2937;
    border-top: 2px solid #e5e7eb;
}

.continue-btn,
.complete-btn {
    width: 100%;
    background: linear-gradient(135deg, #6a0dad, #8e44ad);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 1rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.continue-btn:hover,
.complete-btn:hover {
    background: linear-gradient(135deg, #5a0a8a, #7a3d98);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(106, 13, 173, 0.3);
}

.continue-btn:disabled,
.complete-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.security-info {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
}

.security-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.85rem;
    color: #6b7280;
}

.security-item i {
    color: #10b981;
}

.money-back-guarantee {
    background: #f0fdf4;
    border: 1px solid #bbf7d0;
    border-radius: 6px;
    padding: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
    color: #15803d;
    font-size: 0.9rem;
    font-weight: 500;
}

.money-back-guarantee i {
    color: #15803d;
}

/* Step 3 - Final Review */
.order-details {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-bottom: 2rem;
}

.detail-section {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 1.5rem;
}

.detail-section h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 0.5rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
}

.detail-item span:first-child {
    color: #6b7280;
    font-weight: 500;
}

.detail-item span:last-child {
    color: #1f2937;
    font-weight: 600;
}

.terms-section {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 1.5rem;
}

.checkbox-group {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.checkbox-group input[type="checkbox"] {
    margin-top: 0.2rem;
    width: 18px;
    height: 18px;
    accent-color: #6a0dad;
}

.checkbox-group label {
    font-size: 0.9rem;
    color: #374151;
    line-height: 1.5;
}

.checkbox-group a {
    color: #6a0dad;
    text-decoration: none;
    font-weight: 500;
}

.checkbox-group a:hover {
    text-decoration: underline;
}

/* Success Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 12px;
    padding: 3rem;
    text-align: center;
    max-width: 500px;
    margin: 2rem;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.success-icon {
    font-size: 4rem;
    color: #10b981;
    margin-bottom: 1rem;
}

.modal-content h2 {
    font-size: 1.8rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
}

.modal-content p {
    color: #6b7280;
    font-size: 1.1rem;
    margin-bottom: 2rem;
}

.modal-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.primary-btn,
.secondary-btn {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.primary-btn {
    background: #6a0dad;
    color: white;
    border: none;
}

.primary-btn:hover {
    background: #5a0a8a;
}

.secondary-btn {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.secondary-btn:hover {
    background: #e5e7eb;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .step-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .step-right {
        position: static;
    }
    
    .checkout-main {
        padding: 2rem 1rem;
    }
}

@media (max-width: 768px) {
    .header-content {
        padding: 0 1rem;
    }
    
    .logo h1 {
        font-size: 1.5rem;
    }
    
    .progress-bar {
        padding: 0 1rem;
    }
    
    .step-label {
        font-size: 0.8rem;
    }
    
    .step-left {
        padding: 1.5rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .cycle-options {
        gap: 0.8rem;
    }
    
    .cycle-option {
        padding: 0.8rem;
    }
    
    .modal-content {
        margin: 1rem;
        padding: 2rem;
    }
    
    .modal-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .step-header h2 {
        font-size: 1.5rem;
    }
    
    .plan-card {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .payment-option {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
    
    .summary-item,
    .detail-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
}