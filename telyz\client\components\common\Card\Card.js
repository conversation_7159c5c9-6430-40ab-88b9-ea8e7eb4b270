/**
 * Card Component
 * 
 * A reusable card component for displaying content in a container with various styles.
 */

class Card {
  /**
   * Create a card component
   * 
   * @param {Object} options - Card options
   * @param {string} options.title - Card title
   * @param {string} options.subtitle - Card subtitle
   * @param {string} options.content - Card content
   * @param {string} options.image - Card image URL
   * @param {string} options.variant - Card variant (default, outlined, elevated)
   * @param {boolean} options.clickable - Whether the card is clickable
   * @param {Function} options.onClick - Click event handler
   */
  constructor(options) {
    this.title = options.title || '';
    this.subtitle = options.subtitle || '';
    this.content = options.content || '';
    this.image = options.image || null;
    this.variant = options.variant || 'default';
    this.clickable = options.clickable || false;
    this.onClick = options.onClick || null;
    this.footer = options.footer || null;
    
    this.element = this.createCardElement();
  }
  
  /**
   * Create the card element
   * 
   * @returns {HTMLElement} Card element
   */
  createCardElement() {
    const card = document.createElement('div');
    
    // Add classes
    card.classList.add('card');
    card.classList.add(`card-${this.variant}`);
    
    if (this.clickable) {
      card.classList.add('card-clickable');
      
      if (this.onClick) {
        card.addEventListener('click', this.onClick);
      }
    }
    
    // Add image if provided
    if (this.image) {
      const imageContainer = document.createElement('div');
      imageContainer.classList.add('card-image');
      
      const img = document.createElement('img');
      img.src = this.image;
      img.alt = this.title;
      
      imageContainer.appendChild(img);
      card.appendChild(imageContainer);
    }
    
    // Create card content container
    const cardContent = document.createElement('div');
    cardContent.classList.add('card-content');
    
    // Add title if provided
    if (this.title) {
      const titleElement = document.createElement('h3');
      titleElement.classList.add('card-title');
      titleElement.textContent = this.title;
      cardContent.appendChild(titleElement);
    }
    
    // Add subtitle if provided
    if (this.subtitle) {
      const subtitleElement = document.createElement('h4');
      subtitleElement.classList.add('card-subtitle');
      subtitleElement.textContent = this.subtitle;
      cardContent.appendChild(subtitleElement);
    }
    
    // Add content if provided
    if (this.content) {
      const contentElement = document.createElement('div');
      contentElement.classList.add('card-text');
      contentElement.innerHTML = this.content;
      cardContent.appendChild(contentElement);
    }
    
    card.appendChild(cardContent);
    
    // Add footer if provided
    if (this.footer) {
      const footerElement = document.createElement('div');
      footerElement.classList.add('card-footer');
      
      if (typeof this.footer === 'string') {
        footerElement.innerHTML = this.footer;
      } else if (this.footer instanceof HTMLElement) {
        footerElement.appendChild(this.footer);
      }
      
      card.appendChild(footerElement);
    }
    
    return card;
  }
  
  /**
   * Render the card to a container
   * 
   * @param {HTMLElement} container - Container element
   */
  render(container) {
    if (container) {
      container.appendChild(this.element);
    }
  }
  
  /**
   * Update card title
   * 
   * @param {string} title - New card title
   */
  setTitle(title) {
    this.title = title;
    
    let titleElement = this.element.querySelector('.card-title');
    
    if (titleElement) {
      titleElement.textContent = this.title;
    } else if (this.title) {
      titleElement = document.createElement('h3');
      titleElement.classList.add('card-title');
      titleElement.textContent = this.title;
      
      const cardContent = this.element.querySelector('.card-content');
      cardContent.insertBefore(titleElement, cardContent.firstChild);
    }
  }
  
  /**
   * Update card content
   * 
   * @param {string} content - New card content
   */
  setContent(content) {
    this.content = content;
    
    let contentElement = this.element.querySelector('.card-text');
    
    if (contentElement) {
      contentElement.innerHTML = this.content;
    } else if (this.content) {
      contentElement = document.createElement('div');
      contentElement.classList.add('card-text');
      contentElement.innerHTML = this.content;
      
      const cardContent = this.element.querySelector('.card-content');
      cardContent.appendChild(contentElement);
    }
  }
  
  /**
   * Update card image
   * 
   * @param {string} image - New card image URL
   */
  setImage(image) {
    this.image = image;
    
    let imageContainer = this.element.querySelector('.card-image');
    
    if (imageContainer) {
      if (this.image) {
        const img = imageContainer.querySelector('img');
        img.src = this.image;
        img.alt = this.title;
      } else {
        this.element.removeChild(imageContainer);
      }
    } else if (this.image) {
      imageContainer = document.createElement('div');
      imageContainer.classList.add('card-image');
      
      const img = document.createElement('img');
      img.src = this.image;
      img.alt = this.title;
      
      imageContainer.appendChild(img);
      this.element.insertBefore(imageContainer, this.element.firstChild);
    }
  }
  
  /**
   * Make the card clickable or not
   * 
   * @param {boolean} clickable - Whether the card should be clickable
   * @param {Function} onClick - Click event handler
   */
  setClickable(clickable, onClick) {
    this.clickable = clickable;
    
    if (onClick) {
      this.onClick = onClick;
    }
    
    if (clickable) {
      this.element.classList.add('card-clickable');
      
      if (this.onClick) {
        this.element.addEventListener('click', this.onClick);
      }
    } else {
      this.element.classList.remove('card-clickable');
      
      if (this.onClick) {
        this.element.removeEventListener('click', this.onClick);
      }
    }
  }
}

// Export the Card class
if (typeof module !== 'undefined' && module.exports) {
  module.exports = Card;
}
