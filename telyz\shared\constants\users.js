/**
 * Users Constants
 * 
 * Defines constants related to users in the Telyz platform.
 */

/**
 * User roles
 */
const USER_ROLES = {
  ATHLETE: 'athlete',
  COACH: 'coach',
  SCOUT: 'scout',
  CLUB: 'club',
  JOURNALIST: 'journalist',
  DOCTOR: 'doctor',
  ADMIN: 'admin',
};

/**
 * User status values
 */
const USER_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  SUSPENDED: 'suspended',
  DELETED: 'deleted',
};

/**
 * Verification status values
 */
const VERIFICATION_STATUS = {
  UNVERIFIED: 'unverified',
  PENDING: 'pending',
  VERIFIED: 'verified',
  REJECTED: 'rejected',
};

/**
 * Premium tiers
 */
const PREMIUM_TIERS = {
  FREE: 'free',
  BASIC: 'basic',
  PRO: 'pro',
  ELITE: 'elite',
};

/**
 * Profile visibility options
 */
const PROFILE_VISIBILITY = {
  PUBLIC: 'public',
  CONNECTIONS: 'connections',
  PRIVATE: 'private',
};

/**
 * Connection status values
 */
const CONNECTION_STATUS = {
  PENDING: 'pending',
  ACCEPTED: 'accepted',
  REJECTED: 'rejected',
  BLOCKED: 'blocked',
};

/**
 * Authentication providers
 */
const AUTH_PROVIDERS = {
  LOCAL: 'local',
  GOOGLE: 'google',
  FACEBOOK: 'facebook',
  APPLE: 'apple',
  TWITTER: 'twitter',
};

/**
 * Notification types
 */
const NOTIFICATION_TYPES = {
  CONNECTION_REQUEST: 'connection_request',
  CONNECTION_ACCEPTED: 'connection_accepted',
  MESSAGE: 'message',
  OPPORTUNITY: 'opportunity',
  APPLICATION_UPDATE: 'application_update',
  MENTION: 'mention',
  COMMENT: 'comment',
  LIKE: 'like',
  SHARE: 'share',
  SYSTEM: 'system',
  AI_ANALYSIS_COMPLETE: 'ai_analysis_complete',
};

/**
 * Notification delivery methods
 */
const NOTIFICATION_DELIVERY = {
  IN_APP: 'in_app',
  EMAIL: 'email',
  PUSH: 'push',
  SMS: 'sms',
};

/**
 * User preferences categories
 */
const USER_PREFERENCES = {
  NOTIFICATIONS: 'notifications',
  PRIVACY: 'privacy',
  LANGUAGE: 'language',
  THEME: 'theme',
  EMAIL_FREQUENCY: 'email_frequency',
  OPPORTUNITY_ALERTS: 'opportunity_alerts',
};

/**
 * Achievement types
 */
const ACHIEVEMENT_TYPES = {
  TROPHY: 'trophy',
  MEDAL: 'medal',
  AWARD: 'award',
  CERTIFICATE: 'certificate',
  RECORD: 'record',
  MILESTONE: 'milestone',
};

/**
 * Recommendation types
 */
const RECOMMENDATION_TYPES = {
  COACH: 'coach',
  TEAMMATE: 'teammate',
  SCOUT: 'scout',
  CLUB: 'club',
  MENTOR: 'mentor',
};

module.exports = {
  USER_ROLES,
  USER_STATUS,
  VERIFICATION_STATUS,
  PREMIUM_TIERS,
  PROFILE_VISIBILITY,
  CONNECTION_STATUS,
  AUTH_PROVIDERS,
  NOTIFICATION_TYPES,
  NOTIFICATION_DELIVERY,
  USER_PREFERENCES,
  ACHIEVEMENT_TYPES,
  RECOMMENDATION_TYPES,
};
