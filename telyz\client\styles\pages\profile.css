/* Athletic Profile Styles */

/* Main content container */
.athletic-profile-content {
    padding: 0;
    background-color: #f0f2f5;
    width: 100%;
    max-width: 750px; /* Reducido de 900px a 750px para dar más espacio al sidebar */
    margin: 2% auto 0; /* <PERSON><PERSON><PERSON><PERSON> margen superior del 2% */
}

/* Profile Header */
.profile-header {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    overflow: hidden;
    padding-top: 0;
}

.profile-top-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px 0;
}

.profile-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-bottom: 0;
}

.profile-action-btn {
    background-color: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 20px;
    padding: 8px 15px;
    font-size: 13px;
    font-weight: 500;
    color: #333;
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: all 0.2s;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

.profile-action-btn i {
    margin-right: 5px;
    font-size: 14px;
}

.profile-action-btn:hover {
    background-color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.profile-action-btn.primary {
    background-color: #6a0dad;
    color: white;
    border: none;
}

.profile-action-btn.primary:hover {
    background-color: #8e44ad;
    transform: translateY(-2px);
}

.profile-info-container {
    padding: 0 20px 20px;
    position: relative;
}

.profile-photo-container {
    position: relative;
    margin: 0;
    margin-right: 20px; /* Añadido margen derecho para mover la foto hacia la derecha */
    width: 120px;
    height: 120px;
}

.profile-photo {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 4px solid white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    object-fit: cover;
}

.verification-badge {
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 25px;
    height: 25px;
    background-color: #6a0dad;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    border: 2px solid white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.profile-details {
    margin-bottom: 20px;
}

.profile-name-container {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    margin-right: 20px; /* Añadido margen derecho para mover los elementos hacia la derecha */
}

.profile-name-container h1 {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin: 0;
    margin-right: 10px;
}

.profile-title {
    font-size: 16px;
    color: #666;
    margin-bottom: 5px;
}

.profile-location {
    font-size: 14px;
    color: #777;
    margin-bottom: 8px; /* Reducido de 15px a 8px para acercar el equipo a la ubicación */
}

.profile-location i {
    margin-right: 5px;
    color: #6a0dad;
}

.profile-teams {
    display: flex;
    align-items: center;
    margin-top: 5px; /* Reducido de 10px a 5px para acercar el equipo a la información del jugador */
}

.current-team {
    display: flex;
    align-items: center;
    background-color: #f8f9fa;
    padding: 8px 15px;
    border-radius: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.team-logo {
    width: 24px;
    height: 24px;
    margin-right: 8px;
}

.team-info {
    display: flex;
    flex-direction: column;
}

.team-name {
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.team-duration {
    font-size: 12px;
    color: #777;
}

.profile-stats-summary {
    display: flex;
    justify-content: space-between;
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-top: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    padding: 0 10px;
    border-right: 1px solid #e0e0e0;
}

.stat-item:last-child {
    border-right: none;
}

.stat-value {
    font-size: 22px;
    font-weight: 700;
    color: #6a0dad;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Profile Navigation */
.profile-navigation {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    overflow: hidden;
}

.profile-nav-tabs {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    overflow-x: auto;
    gap: 2px;
}

.profile-nav-tabs li {
    flex: 1;
    min-width: 120px;
    text-align: center;
}

.profile-nav-tabs li a {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 15px 18px;
    color: #555;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s;
    border-bottom: 3px solid transparent;
}

.profile-nav-tabs li a i {
    margin-right: 8px;
    font-size: 16px;
}

/* Colores modernos para cada pestaña */
.profile-nav-tabs li a[href="#overview"] {
    color: #5c6bc0;
}

.profile-nav-tabs li a[href="#overview"] i {
    color: #5c6bc0;
}

.profile-nav-tabs li a[href="#career"] {
    color: #26a69a;
}

.profile-nav-tabs li a[href="#career"] i {
    color: #26a69a;
}

.profile-nav-tabs li a[href="#stats"] {
    color: #ec407a;
}

.profile-nav-tabs li a[href="#stats"] i {
    color: #ec407a;
}

.profile-nav-tabs li a[href="#skills"] {
    color: #7e57c2;
}

.profile-nav-tabs li a[href="#skills"] i {
    color: #7e57c2;
}

.profile-nav-tabs li a[href="#achievements"] {
    color: #ffa726;
}

.profile-nav-tabs li a[href="#achievements"] i {
    color: #ffa726;
}

.profile-nav-tabs li a[href="#media"] {
    color: #42a5f5;
}

.profile-nav-tabs li a[href="#media"] i {
    color: #42a5f5;
}

.profile-nav-tabs li a[href="#recommendations"] {
    color: #66bb6a;
}

.profile-nav-tabs li a[href="#recommendations"] i {
    color: #66bb6a;
}

.profile-nav-tabs li a[href="#ai-analysis"] {
    color: #00b8d4;
}

.profile-nav-tabs li a[href="#ai-analysis"] i {
    color: #00b8d4;
}

.profile-nav-tabs li a:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

/* Estilos para pestañas activas */
.profile-nav-tabs li.active a[href="#overview"] {
    border-bottom: 3px solid #5c6bc0;
}

.profile-nav-tabs li.active a[href="#career"] {
    border-bottom: 3px solid #26a69a;
}

.profile-nav-tabs li.active a[href="#stats"] {
    border-bottom: 3px solid #ec407a;
}

.profile-nav-tabs li.active a[href="#skills"] {
    border-bottom: 3px solid #7e57c2;
}

.profile-nav-tabs li.active a[href="#achievements"] {
    border-bottom: 3px solid #ffa726;
}

.profile-nav-tabs li.active a[href="#media"] {
    border-bottom: 3px solid #42a5f5;
}

.profile-nav-tabs li.active a[href="#recommendations"] {
    border-bottom: 3px solid #66bb6a;
}

.profile-nav-tabs li.active a[href="#ai-analysis"] {
    border-bottom: 3px solid #00b8d4;
}

/* Profile Content Sections */
.profile-content-sections {
    margin-bottom: 20px;
}

.profile-section {
    display: none;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;
    animation: fadeIn 0.3s ease-in-out;
}

.profile-section.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.profile-section h2 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-top: 0;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;
}

/* About Section */
.profile-about-section p {
    font-size: 14px;
    line-height: 1.6;
    color: #555;
    margin-bottom: 25px;
}

.profile-about-section h3 {
    font-size: 16px;
    font-weight: 600;
    color: #444;
    margin: 25px 0 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
}

/* Personal Information Section */
.personal-info-section {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.personal-info-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}

.personal-info-item {
    padding: 10px;
    transition: background-color 0.2s;
    border-radius: 6px;
}

.personal-info-item:hover {
    background-color: #f0f0f0;
}

.info-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
}

.info-label i {
    margin-right: 8px;
    color: #6a0dad;
    width: 16px;
    text-align: center;
}

.info-value {
    font-size: 15px;
    font-weight: 500;
    color: #333;
}

/* Player Specialties */
.player-specialties {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: 25px;
}

.specialty-tag {
    display: inline-flex;
    align-items: center;
    background-color: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
    padding: 8px 14px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s, box-shadow 0.2s;
}

.specialty-tag:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.specialty-tag i {
    margin-right: 6px;
    font-size: 14px;
}

/* Two-column layout */
.profile-two-column-layout {
    display: flex;
    gap: 25px;
    margin-top: 25px;
}

.profile-column {
    flex: 1;
}

.profile-column h3 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-top: 0;
    margin-bottom: 18px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;
}

.key-attributes {
    display: grid;
    grid-template-columns: 1fr;
    gap: 18px;
}

.attribute {
    display: flex;
    flex-direction: column;
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
    transition: transform 0.2s;
}

.attribute:hover {
    transform: translateY(-2px);
}

.attribute-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.attribute i {
    color: #6a0dad;
    font-size: 16px;
    margin-right: 8px;
}

.attribute-header span {
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.attribute-value {
    margin-left: auto;
    background-color: #6a0dad;
    color: white;
    font-weight: 600;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 12px;
}

.attribute-bar {
    height: 8px;
    background-color: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
}

.attribute-fill {
    height: 100%;
    background-color: #6a0dad;
    border-radius: 4px;
    transition: width 0.5s ease-in-out;
}

/* Career Highlights */
.highlights-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
}

.highlight-card {
    display: flex;
    align-items: center;
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.highlight-icon {
    width: 40px;
    height: 40px;
    background-color: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    margin-right: 15px;
}

.highlight-content h3 {
    font-size: 15px;
    font-weight: 600;
    color: #333;
    margin: 0 0 5px 0;
}

.highlight-content p {
    font-size: 13px;
    color: #666;
    margin: 0;
}

/* Recent Performance */
.recent-performance-section {
    margin-bottom: 30px;
}

.performance-chart {
    margin-bottom: 20px;
    overflow: hidden;
}

.chart-placeholder {
    width: 100%;
    max-height: 200px;
    object-fit: contain;
    border-radius: 8px;
    display: block;
}

.recent-matches {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.match-card {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    height: 100%;
}

.match-teams {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
}

.match-teams .team-logo {
    width: 30px;
    height: 30px;
}

.match-score {
    font-size: 18px;
    font-weight: 700;
    color: #333;
    margin: 0 15px;
}

.match-details {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.match-date {
    font-size: 13px;
    color: #666;
    margin-bottom: 8px;
}

.player-stats {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 10px;
}

.player-stats .stat {
    font-size: 13px;
    color: #555;
    background-color: white;
    padding: 5px 10px;
    border-radius: 15px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.player-stats .stat i {
    color: #6a0dad;
    margin-right: 5px;
}

/* Media & Highlights Section */
.media-highlights-content {
    padding: 0;
}

.media-navigation {
    display: flex;
    gap: 10px;
    margin-bottom: 25px;
    flex-wrap: wrap;
}

.media-nav-button {
    padding: 10px 20px;
    border: 1px solid #e0e0e0;
    border-radius: 20px;
    background-color: white;
    color: #555;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.media-nav-button:hover {
    background-color: #f5f5f5;
}

.media-nav-button.active {
    background-color: #6a0dad;
    color: white;
    border-color: #6a0dad;
}

.featured-media {
    margin-bottom: 30px;
}

.featured-media-container {
    background-color: #f8f9fa;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.featured-media-player {
    position: relative;
    width: 100%;
    height: 300px;
    overflow: hidden;
}

.featured-media-player img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background-color: rgba(106, 13, 173, 0.9);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    cursor: pointer;
    transition: all 0.2s;
}

.play-button:hover {
    background-color: #6a0dad;
    transform: translate(-50%, -50%) scale(1.1);
}

.featured-media-info {
    padding: 20px;
}

.featured-media-info h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0 0 10px 0;
}

.media-metadata {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
    font-size: 13px;
    color: #666;
}

.media-views {
    display: flex;
    align-items: center;
    gap: 5px;
}

.media-description {
    font-size: 14px;
    line-height: 1.6;
    color: #555;
    margin: 0;
}

.media-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.media-item {
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s, box-shadow 0.2s;
}

.media-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.media-thumbnail {
    position: relative;
    width: 100%;
    height: 180px;
    overflow: hidden;
}

.media-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.media-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.2s;
}

.media-item:hover .media-overlay {
    opacity: 1;
}

.media-play-button,
.media-zoom-button {
    width: 50px;
    height: 50px;
    background-color: rgba(106, 13, 173, 0.9);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    cursor: pointer;
    transition: all 0.2s;
}

.media-play-button:hover,
.media-zoom-button:hover {
    background-color: #6a0dad;
    transform: scale(1.1);
}

.media-duration {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.media-info {
    padding: 15px;
}

.media-info h4 {
    font-size: 15px;
    font-weight: 600;
    color: #333;
    margin: 0 0 8px 0;
    line-height: 1.3;
}

.load-more-container {
    text-align: center;
    margin-bottom: 30px;
}

.load-more-button {
    padding: 12px 25px;
    border: 1px solid #e0e0e0;
    border-radius: 20px;
    background-color: white;
    color: #555;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.load-more-button:hover {
    background-color: #f5f5f5;
    transform: translateY(-2px);
}

.media-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.media-stat-item {
    text-align: center;
}

.media-stat-value {
    font-size: 20px;
    font-weight: 700;
    color: #6a0dad;
    margin-bottom: 5px;
}

.media-stat-label {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Eye Icon Styles */
.eye-icon-container {
    position: relative;
    display: inline-flex;
    align-items: center;
    background-color: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
    padding: 6px 12px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 14px;
    font-weight: 500;
}

.eye-icon-container:hover {
    background-color: rgba(106, 13, 173, 0.2);
    transform: translateY(-1px);
}

.eye-icon-container i {
    margin-right: 6px;
    font-size: 16px;
}

.watchers-count {
    font-weight: 600;
}

.eye-tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s;
    margin-bottom: 5px;
    z-index: 1000;
}

.eye-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.9);
}

.eye-icon-container:hover .eye-tooltip {
    opacity: 1;
    visibility: visible;
}

/* Media Lightbox */
.media-lightbox {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.9);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.lightbox-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
}

.lightbox-close {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 40px;
    height: 40px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10001;
    transition: background-color 0.2s;
}

.lightbox-close:hover {
    background-color: rgba(0, 0, 0, 0.9);
}

.lightbox-media-container {
    width: 100%;
    height: 70vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.lightbox-media-container img,
.lightbox-media-container video {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.lightbox-caption {
    padding: 20px;
    background-color: white;
    border-top: 1px solid #f0f0f0;
}

/* Animations */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .athletic-profile-content {
        max-width: 100%;
        margin: 1% auto 0;
        padding: 0 10px;
    }

    .profile-top-section {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }

    .profile-actions {
        width: 100%;
        justify-content: flex-start;
    }

    .profile-nav-tabs {
        flex-direction: column;
    }

    .profile-nav-tabs li {
        min-width: auto;
    }

    .profile-two-column-layout {
        flex-direction: column;
        gap: 20px;
    }

    .personal-info-grid {
        grid-template-columns: 1fr;
    }

    .recent-matches {
        grid-template-columns: 1fr;
    }

    .media-grid {
        grid-template-columns: 1fr;
    }

    .media-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .featured-media-player {
        height: 200px;
    }
}

@media (max-width: 480px) {
    .profile-action-btn {
        padding: 6px 12px;
        font-size: 12px;
    }

    .profile-name-container h1 {
        font-size: 20px;
    }

    .profile-stats-summary {
        flex-direction: column;
        gap: 15px;
    }

    .stat-item {
        border-right: none;
        border-bottom: 1px solid #e0e0e0;
        padding-bottom: 15px;
    }

    .stat-item:last-child {
        border-bottom: none;
        padding-bottom: 0;
    }

    .media-navigation {
        justify-content: center;
    }

    .media-nav-button {
        flex: 1;
        text-align: center;
        min-width: 80px;
    }
}
