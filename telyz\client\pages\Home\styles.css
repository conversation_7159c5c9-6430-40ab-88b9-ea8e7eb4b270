/* Estilos generales */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: '<PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

html {
    height: 100%;
    overflow-y: scroll;
}

body {
    background-color: #f0f2f5;
    color: #333;
}

.container {
    display: flex;
    max-width: 1050px; /* Reducido de 1110px a 1050px para una mejor distribución */
    margin: 0 auto;
    background-color: #f0f2f5;
}

/* Barra lateral izquierda */
.sidebar-left {
    background-color: #f0f2f5;
    padding: 15px 0;
    position: sticky;
    top: 0;
    width: 270px;
    height: 100vh;
    overflow-y: auto;
    align-self: flex-start;
}

/* Estilo para la barra de desplazamiento del sidebar */
.sidebar-left::-webkit-scrollbar {
    width: 6px;
}

.sidebar-left::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
}

.sidebar-left::-webkit-scrollbar-thumb {
    background: rgba(106, 13, 173, 0.3);
    border-radius: 3px;
}

.sidebar-left::-webkit-scrollbar-thumb:hover {
    background: rgba(106, 13, 173, 0.5);
}

.logo {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

.logo-link {
    text-decoration: none;
    cursor: pointer;
    transition: transform 0.2s ease;
    display: block;
}

.logo-link:hover {
    transform: scale(1.05);
}

.logo-container {
    width: 40px;
    height: 40px;
    background-color: #6a0dad; /* Cambiado a color púrpura */
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(106, 13, 173, 0.3); /* Sombra púrpura */
}

.sidebar-menu {
    padding: 0 10px; /* Reducido el padding horizontal para acercar más los elementos al borde */
    max-height: calc(100vh - 100px);
    width: 100%;
}

.menu-title {
    font-size: 12px;
    color: #666;
    margin: 20px 0 8px;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.sidebar-menu ul {
    list-style: none;
}

.sidebar-menu > ul > li {
    margin: 4px 0;
    padding: 8px 5px;
    border-radius: 10px;
    transition: all 0.3s;
}

.sidebar-menu li:hover {
    background-color: rgba(106, 13, 173, 0.05); /* Fondo púrpura muy suave al pasar el ratón */
}

/* Submenu de deportes */
.has-submenu {
    position: relative;
}

.submenu {
    display: none;
    list-style: none;
    padding-left: 0;
    margin-top: 5px;
    margin-bottom: 5px;
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 8px;
    overflow: visible;
    max-height: 0;
    transition: max-height 0.3s ease-in-out;
    width: 100%;
    min-width: 260px;
    padding: 8px 0;
}

.has-submenu:hover .submenu {
    display: block;
    max-height: 400px;
}

/* Estilos para los tooltips en el menú AI Analysis */
.js-tooltip {
    position: fixed;
    width: 220px;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px;
    border-radius: 6px;
    font-size: 12px;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
    z-index: 1001;
    line-height: 1.4;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    pointer-events: none;
    text-align: left;
}

.js-tooltip::before {
    content: "";
    position: absolute;
    top: 50%;
    left: -10px;
    transform: translateY(-50%);
    border-width: 5px;
    border-style: solid;
    border-color: transparent var(--tooltip-color, rgba(0, 0, 0, 0.8)) transparent transparent;
}

.js-tooltip.visible {
    opacity: 1;
    visibility: visible;
}

/* Colores específicos para cada opción en el menú de AI Analysis */
.ai-submenu li:nth-child(1) i { color: #FF5722; } /* Video Analysis */
.ai-submenu li:nth-child(2) i { color: #4CAF50; } /* Performance Stats */
.ai-submenu li:nth-child(3) i { color: #2196F3; } /* Player Comparison */
.ai-submenu li:nth-child(4) i { color: #FFC107; } /* Training Recommendations */
.ai-submenu li:nth-child(5) i { color: #9C27B0; } /* Talent Benchmarks */

.coming-soon-text {
    font-style: italic;
    color: #6a0dad; /* Cambiado a color púrpura */
    margin-top: 5px;
    font-size: 11px;
}

.submenu li {
    margin: 0;
    padding: 8px 12px 8px 5px;
    min-width: 240px;
    position: relative;
}

.submenu li:hover {
    background-color: rgba(106, 13, 173, 0.1);
    transform: translateX(5px);
}

.submenu a {
    font-size: 14px;
    padding: 4px 0;
    white-space: normal; /* Permitir que el texto se divida en varias líneas si es necesario */
    overflow: visible; /* Cambiado a visible para mostrar todo el texto */
    text-overflow: clip; /* Eliminado ellipsis */
    display: flex; /* Añadido display flex para mejor alineación */
    align-items: center; /* Centrado vertical */
    width: 100%; /* Ancho completo */
    line-height: 1.4; /* Mejorar la legibilidad */
}

.submenu a span {
    display: inline-block;
    white-space: normal; /* Permitir que el texto se divida en varias líneas si es necessary */
    overflow: visible;
    width: auto;
}

.submenu i {
    font-size: 16px;
    margin-right: 10px;
    width: 20px; /* Ancho ajustado */
    text-align: center;
    display: inline-flex; /* Cambiado a inline-flex para mejor alineación */
    justify-content: center; /* Centrado horizontal */
    align-items: center; /* Centrado vertical */
}

/* Ajuste específico para los iconos del menú AI Analysis */
.ai-submenu i {
    margin-left: 0; /* Eliminar cualquier margen izquierdo */
}

/* Ajustes específicos para el menú AI Analysis */
.ai-submenu {
    overflow: visible !important;
    width: 260px;
    padding: 8px 0;
}

/* Ajustes específicos para el menú Explore */
.explore-submenu {
    overflow: hidden; /* Changed to hidden to work with max-height */
    width: 260px;
    padding: 8px 0;
}

.explore-submenu li {
    overflow: visible;
    padding: 12px 15px 12px 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.03);
}

.explore-submenu li:last-child {
    border-bottom: none;
}

.explore-submenu a {
    align-items: flex-start;
}

.explore-submenu a span {
    padding-top: 2px;
    font-weight: 500;
}

.explore-submenu i {
    font-size: 18px;
    margin-right: 12px;
}

.ai-submenu li {
    overflow: visible;
    padding: 12px 15px 12px 10px; /* Aumentar el padding vertical y horizontal */
    border-bottom: 1px solid rgba(0, 0, 0, 0.03); /* Añadir separador sutil */
}

.ai-submenu li:last-child {
    border-bottom: none; /* Eliminar borde del último elemento */
}

.ai-submenu a {
    font-size: 14px;
    padding: 4px 0;
    white-space: normal;
    overflow: visible;
    text-overflow: clip;
    display: flex;
    align-items: flex-start;
    width: 100%;
    line-height: 1.4;
}

.ai-submenu a span {
    display: inline-block;
    white-space: normal;
    overflow: visible;
    width: auto;
    padding-top: 2px;
    font-weight: 500;
}

.ai-submenu i {
    font-size: 18px;
    margin-right: 12px;
    margin-left: 0;
    width: 20px;
    text-align: center;
    display: inline-flex;
    justify-content: center;
    align-items: center;
}

/* Ajustes específicos para el menú Sports */
.sports-submenu {
    overflow: visible !important;
    width: 260px;
    padding: 8px 0;
}

.sports-submenu li {
    overflow: visible;
    padding: 12px 15px 12px 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.03);
}

.sports-submenu li:last-child {
    border-bottom: none;
}

.sports-submenu a {
    align-items: flex-start;
}

.sports-submenu a span {
    padding-top: 2px;
    font-weight: 500;
}

.sports-submenu i {
    font-size: 18px;
    margin-right: 12px;
}

/* Ajustes específicos para el menú Categories */
.categories-submenu {
    overflow: visible !important;
    width: 260px;
    padding: 8px 0;
}

.categories-submenu li {
    overflow: visible;
    padding: 12px 15px 12px 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.03);
}

.categories-submenu li:last-child {
    border-bottom: none;
}

.categories-submenu a {
    font-size: 14px;
    padding: 4px 0;
    white-space: normal;
    overflow: visible;
    text-overflow: clip;
    display: flex;
    align-items: flex-start;
    width: 100%;
    line-height: 1.4;
}

.categories-submenu a span {
    display: inline-block;
    white-space: normal;
    overflow: visible;
    width: auto;
    padding-top: 2px;
    font-weight: 500;
}

.categories-submenu i {
    font-size: 18px;
    margin-right: 12px;
    margin-left: 0;
    width: 20px;
    text-align: center;
    display: inline-flex;
    justify-content: center;
    align-items: center;
}

/* Ajustes específicos para el menú Explore */
.explore-submenu {
    overflow: visible !important;
    width: 260px;
    padding: 8px 0;
}

.explore-submenu li {
    overflow: visible;
    padding: 12px 15px 12px 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.03);
}

.explore-submenu li:last-child {
    border-bottom: none;
}

.explore-submenu a {
    font-size: 14px;
    padding: 4px 0;
    white-space: normal;
    overflow: visible;
    text-overflow: clip;
    display: flex;
    align-items: flex-start;
    width: 100%;
    line-height: 1.4;
}

.explore-submenu a span {
    display: inline-block;
    white-space: normal;
    overflow: visible;
    width: auto;
    padding-top: 2px;
    font-weight: 500;
}

.explore-submenu i {
    font-size: 18px;
    margin-right: 12px;
    margin-left: 0;
    width: 20px;
    text-align: center;
    display: inline-flex;
    justify-content: center;
    align-items: center;
}

/* Colores específicos para cada deporte en el menú - Variaciones de púrpura */
.submenu li:nth-child(1) i { color: #8e44ad; } /* Football - Púrpura medio */
.submenu li:nth-child(2) i { color: #9b59b6; } /* Basketball - Púrpura claro */
.submenu li:nth-child(3) i { color: #6a0dad; } /* Volleyball - Púrpura principal */
.submenu li:nth-child(4) i { color: #5d3fd3; } /* Baseball - Púrpura azulado */
.submenu li:nth-child(5) i { color: #7d3c98; } /* Cricket - Púrpura oscuro */
.submenu li:nth-child(6) i { color: #a569bd; } /* Field Hockey - Púrpura rosado */

/* Estilo específico para el menú de AI Analysis - Ahora usando .ai-dropdown-item */

.sidebar-menu li.active {
    background-color: rgba(106, 13, 173, 0.08); /* Fondo púrpura suave */
}

.sidebar-menu li.active a {
    color: #6a0dad; /* Púrpura más intenso para elementos activos */
    font-weight: 600;
}

.sidebar-menu li.active i {
    color: #6a0dad; /* Mismo color púrpura para iconos activos */
}

.sidebar-menu a {
    text-decoration: none;
    color: #4a0080;
    display: flex;
    align-items: center;
    font-size: 16px;
    padding: 6px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 500;
    transition: all 0.3s;
    position: relative;
}

.sidebar-menu a:hover {
    background-color: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
    transform: translateX(2px);
}

.sidebar-menu i {
    margin-right: 10px;
    font-size: 19px;
    width: 22px;
    text-align: center;
    color: #6a0dad;
}

/* Badge para elementos nuevos */
.badge {
    display: inline-block;
    padding: 2px 6px;
    font-size: 10px;
    font-weight: bold;
    border-radius: 10px;
    margin-left: 5px;
}

.new-badge {
    background-color: #ff3366;
    color: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    animation: pulse 2s infinite;
    transition: opacity 0.5s ease;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Perfil de usuario en la barra lateral */
.user-profile-sidebar {
    display: flex;
    align-items: center;
    margin-top: 23px;
    padding: 12px 15px;
    border-radius: 10px;
    background-color: rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
}

.user-profile-sidebar:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.profile-dropdown-toggle {
    margin-left: auto;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    border-radius: 50%;
    transition: all 0.2s;
    background-color: rgba(0, 0, 0, 0.05);
    cursor: pointer;
}

.profile-dropdown-toggle:hover {
    background-color: rgba(0, 0, 0, 0.1);
    color: #0066cc;
    transform: scale(1.1);
}

.profile-dropdown-toggle i {
    font-size: 20px;
    color: #444;
}

.profile-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.2);
    width: 220px; /* Reducido de 230px a 220px para adaptarse al nuevo ancho del sidebar */
    z-index: 1000;
    overflow: hidden;
    display: none;
    border: 1px solid #eee;
    margin-top: 6px;
    max-height: 0;
    opacity: 0;
    transition: max-height 0.3s ease, opacity 0.3s ease;
    visibility: hidden;
    text-align: left;
}

.profile-dropdown-menu.show {
    display: block;
    max-height: 400px;
    opacity: 1;
    overflow-y: auto;
    z-index: 1001;
    visibility: visible;
}

.profile-dropdown-item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 14px 25px;
    text-decoration: none;
    color: #333;
    transition: all 0.2s ease;
    font-size: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.03);
    position: relative;
}

.profile-dropdown-item:last-child {
    border-bottom: none;
}

.profile-dropdown-item:hover {
    background-color: #f5f5f5;
    color: #6a0dad;
}

.profile-dropdown-item i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
    color: #6a0dad;
    font-size: 17px;
    display: inline-block;
}

/* Los tooltips ahora se manejan con JavaScript */

.dropdown-divider {
    height: 1px;
    background-color: #e0e0e0;
    margin: 8px 0;
    opacity: 0.7;
}

.logout-item {
    color: #e74c3c;
}

.logout-item i {
    color: #e74c3c;
}

.logout-item:hover {
    background-color: #feeaea;
    color: #c0392b;
}

.profile-pic-small {
    width: 46px;
    height: 46px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 12px;
    border: 2px solid #6a0dad; /* Cambiado a color púrpura */
}

.profile-pic-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-info-sidebar {
    display: flex;
    flex-direction: column;
}

.profile-info-sidebar span {
    font-size: 16px;
    font-weight: 600;
}

.teams-clubs {
    font-size: 14px !important;
    color: #666;
    font-weight: normal !important;
}

/* Contenido principal */
.main-content {
    padding: 20px 0;
    background-color: #f0f2f5;
    width: 100%; /* Make it take full width on smaller screens */
    max-width: 500px; /* Limit maximum width on larger screens */
    margin: 0 auto;
}

/* Área de creación de publicación */
.post-creation {
    background-color: white;
    border-radius: 8px;
    padding: 15px;
    margin: 0 20px 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    width: calc(100% - 40px);
}

/* Estilos para el área de información del usuario */

.user-info-layout {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    padding: 5px;
}

.profile-pic-container {
    margin-right: 12px;
}

.profile-pic-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 2px solid #6a0dad; /* Cambiado a color púrpura */
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f0f0f0;
    box-shadow: 0 2px 5px rgba(106, 13, 173, 0.2); /* Sombra púrpura */
    overflow: hidden;
}

.profile-pic-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-info-details {
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.user-name-container {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
}

.user-info-details h3 {
    font-size: 17px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
    margin-right: 10px;
}

/* Eye icon styles */
.eye-icon-container {
    display: flex;
    align-items: center;
    position: relative;
    cursor: pointer;
}

.eye-icon-container i {
    color: #2ecc71;
    font-size: 16px;
    margin-right: 4px;
    text-shadow: 0 0 3px rgba(46, 204, 113, 0.5);
}

.watchers-count {
    font-size: 12px;
    font-weight: 600;
    color: #2ecc71;
    background-color: rgba(46, 204, 113, 0.1);
    padding: 2px 5px;
    border-radius: 10px;
}

.eye-tooltip {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(46, 204, 113, 0.85);
    color: white;
    padding: 10px 15px;
    border-radius: 6px;
    font-size: 12px;
    white-space: normal;
    width: 220px;
    text-align: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
    margin-top: 8px;
    box-shadow: 0 2px 10px rgba(46, 204, 113, 0.4);
    line-height: 1.4;
    font-weight: 500;
    border: 1px solid rgba(46, 204, 113, 0.3);
}

.eye-tooltip::before {
    content: "";
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border-width: 6px;
    border-style: solid;
    border-color: transparent transparent rgba(46, 204, 113, 0.85) transparent;
}

.eye-icon-container:hover .eye-tooltip {
    opacity: 1;
    visibility: visible;
}

/* Pulse animation for eye icon */
.eye-icon-container {
    animation: eyePulse 3s infinite;
}

@keyframes eyePulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Animations for notifications */
@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOut {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}

.team-info {
    display: flex;
    align-items: center;
    margin-bottom: 3px;
}

.team-logo {
    width: 20px;
    height: 20px;
    margin-right: 4px;
}

.position {
    font-size: 12px;
    color: #666;
    font-weight: 500;
    margin-bottom: 0;
}

/* Estadísticas del perfil */
.profile-stats {
    display: flex;
    justify-content: center;
    margin-top: 10px;
    background-color: #f8f9fa;
    border-radius: 20px;
    padding: 8px 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.profile-stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 15px;
    position: relative;
    cursor: pointer;
}

.profile-stat-item i {
    font-size: 18px;
    color: #0066cc;
    margin-bottom: 5px;
}

.profile-stat-item .fa-eye {
    color: #6c5ce7;
}

.profile-stat-item .fa-video {
    color: #e74c3c;
}

.stat-count {
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.profile-visitors-stat::after {
    content: "Premium";
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: #f39c12;
    color: white;
    font-size: 8px;
    padding: 2px 5px;
    border-radius: 10px;
    font-weight: bold;
    opacity: 0.9;
}

.talent-watch-stat::after {
    content: "Premium";
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: #e74c3c;
    color: white;
    font-size: 8px;
    padding: 2px 5px;
    border-radius: 10px;
    font-weight: bold;
    opacity: 0.9;
}

.simple-textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    resize: none;
    font-size: 15px;
    outline: none;
    min-height: 40px;
    background-color: white;
    margin-top: 10px;
    margin-bottom: 10px;
    transition: border-color 0.3s, box-shadow 0.3s;
    font-family: 'Roboto', sans-serif;
}

.simple-textarea:focus {
    border-color: #6a0dad; /* Color púrpura */
    box-shadow: 0 0 0 2px rgba(106, 13, 173, 0.2); /* Sombra púrpura */
}

.post-actions-new {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 10px;
    background-color: #f5f5f5;
    border-radius: 30px;
    padding: 5px 10px;
}

.post-icons {
    display: flex;
    align-items: center;
}

.left-icons {
    margin-right: 10px;
}

.right-icons {
    margin-left: 10px;
}

.post-icon-btn {
    background: none;
    border: none;
    color: #6a0dad; /* Cambiado a color púrpura */
    margin-right: 15px;
    cursor: pointer;
    font-size: 18px;
    transition: all 0.3s;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(106, 13, 173, 0.1); /* Sombra púrpura */
    background-color: #f8f9fa;
}

.post-icon-btn:hover {
    background-color: #f5eeff; /* Fondo púrpura muy suave */
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 4px 8px rgba(106, 13, 173, 0.2); /* Sombra púrpura */
    color: #8e44ad; /* Púrpura más claro al pasar el ratón */
}

.post-button {
    background-color: #6a0dad;
    background-image: linear-gradient(135deg, #8e44ad, #6a0dad);
    color: white;
    border: none;
    padding: 10px 24px;
    border-radius: 20px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s;
    min-width: 120px;
    box-shadow: 0 4px 10px rgba(106, 13, 173, 0.3);
    font-size: 15px;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    margin: 0 10px;
    z-index: 1;
}

.post-button:hover {
    background-image: linear-gradient(135deg, #9b59b6, #8e44ad);
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(106, 13, 173, 0.4);
}

/* Publicaciones */
.post {
    background-color: white;
    border-radius: 8px;
    margin: 0 20px 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    border: 1px solid #e0e0e0;
    width: calc(100% - 40px);
}

.post-header {
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #f0f0f0;
}

.post-author {
    display: flex;
    align-items: center;
}

.author-pic {
    width: 42px;
    height: 42px;
    border-radius: 50%;
    margin-right: 12px;
    border: 1px solid #e0e0e0;
    object-fit: cover;
    background-color: #f8f9fa;
}

.post-author h4 {
    font-size: 15px;
    margin-bottom: 3px;
    font-weight: 600;
    color: #333;
}

.post-author p {
    font-size: 12px;
    color: #666;
    line-height: 1.3;
}

.follow-button {
    background-color: #6a0dad;
    color: white;
    border: none;
    padding: 6px 15px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: 0 2px 4px rgba(106, 13, 173, 0.2);
}

.follow-button:hover {
    background-color: #8e44ad;
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(106, 13, 173, 0.3);
}

.post-content {
    width: 100%;
}

.post-text {
    padding: 0 15px;
    margin-bottom: 10px;
}

.post-text p {
    font-size: 14px;
    line-height: 1.5;
    color: #333;
    margin: 0;
}

.post-image {
    display: block;
    width: 100%;
    max-width: 100%;
    height: auto;
    border-radius: 4px;
}

.post-video {
    width: 100%;
    max-height: 500px;
    height: auto;
    object-fit: contain;
    border-radius: 8px;
    background-color: #000;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.post-video:hover {
    transform: scale(1.02);
}

/* Video Thumbnail Styles */
.video-thumbnail {
    position: relative;
    cursor: pointer;
    overflow: hidden;
    border-radius: 8px;
    transition: transform 0.2s ease;
}

.video-thumbnail:hover {
    transform: scale(1.02);
}

.play-button-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    transition: all 0.3s ease;
    z-index: 2;
}

.play-button-overlay:hover {
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    transform: translate(-50%, -50%) scale(1.1);
}

.video-duration {
    position: absolute;
    bottom: 8px;
    right: 8px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    z-index: 2;
}

.post-stats {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 12px 15px;
    margin: 10px 15px 15px;
    border: 1px solid #e0e0e0;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 13px;
}

.stat-item:last-child {
    margin-bottom: 0;
}

.stat-label {
    font-weight: 600;
    color: #555;
}

.stat-value {
    color: #6a0dad; /* Color púrpura */
    font-weight: 500;
}

.post-actions-bar {
    display: flex;
    padding: 10px 15px;
    border-top: 1px solid #f0f0f0;
    justify-content: space-between;
}

.action-button {
    background: none;
    border: none;
    color: #555;
    margin-right: 20px;
    cursor: pointer;
    font-size: 15px;
    padding: 8px 15px;
    border-radius: 20px;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.action-button:hover {
    background-color: #f5eeff; /* Fondo púrpura muy suave */
    color: #6a0dad; /* Color púrpura */
    transform: translateY(-2px);
}

.action-button:active {
    transform: scale(0.85) translateY(4px);
    animation: button-pop 0.3s ease;
}

.post-actions-bar i {
    margin-right: 8px;
    font-size: 18px;
}

/* Estilos específicos para cada botón de acción - Variaciones de púrpura */
.action-like i {
    color: #9b59b6; /* Púrpura claro */
}

.action-like:hover {
    background-color: #f5eeff;
    color: #8e44ad;
}

.action-medal i {
    color: #8e44ad; /* Púrpura medio */
    font-size: 19px;
}

.action-medal:hover {
    background-color: #f5eeff;
    color: #6a0dad;
}

/* Animación para los botones */
@keyframes button-pop {
    0% {
        transform: scale(0.85) translateY(4px);
    }
    40% {
        transform: scale(1.15) translateY(-8px);
    }
    70% {
        transform: scale(0.95) translateY(2px);
    }
    100% {
        transform: scale(1) translateY(0);
    }
}

.action-comment i {
    color: #6a0dad; /* Púrpura principal */
}

.action-comment:hover {
    background-color: #f5eeff;
    color: #5d3fd3;
}

.action-repost i {
    color: #7d3c98; /* Púrpura oscuro */
}

.action-repost:hover {
    background-color: #f5eeff;
    color: #6a0dad;
}

.action-share i {
    color: #a569bd; /* Púrpura rosado */
}

.action-share:hover {
    background-color: #f5eeff;
    color: #9b59b6;
}

/* Estilos para el menú desplegable */
.action-more-container {
    position: relative;
    margin-left: auto;
}

.action-more {
    padding: 8px 10px;
    margin-right: 0;
}

.action-more i {
    color: #6a0dad; /* Color púrpura */
}

.action-dropdown {
    position: absolute;
    right: 0;
    top: 100%;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(106, 13, 173, 0.1); /* Sombra púrpura */
    width: 180px;
    z-index: 100;
    overflow: hidden;
    display: none;
    border: 1px solid #f5eeff; /* Borde púrpura muy suave */
}

.action-more-container:hover .action-dropdown {
    display: block;
}

.dropdown-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    font-size: 14px;
    color: #333;
    cursor: pointer;
    transition: background-color 0.2s;
}

.dropdown-item:hover {
    background-color: #f5eeff; /* Fondo púrpura muy suave */
}

.dropdown-item i {
    margin-right: 10px;
    width: 16px;
    text-align: center;
    color: #6a0dad; /* Color púrpura para todos los iconos */
}

.action-save i {
    color: #8e44ad; /* Púrpura medio */
}

.action-report i {
    color: #9b59b6; /* Púrpura claro */
}

/* Efecto de onda al hacer clic */
.action-button::after {
    content: "";
    display: block;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    pointer-events: none;
    background-image: radial-gradient(circle, #fff 10%, transparent 10.01%);
    background-repeat: no-repeat;
    background-position: 50%;
    transform: scale(10, 10);
    opacity: 0;
    transition: transform 0.5s, opacity 0.5s;
}

.action-button:active::after {
    transform: scale(0, 0);
    opacity: 0.3;
    transition: 0s;
}

.post-footer {
    padding: 5px 15px 15px;
    font-size: 12px;
    color: #888;
    text-align: right;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.post-footer span {
    display: flex;
    align-items: center;
}

.post-footer span::before {
    content: "•";
    margin-right: 5px;
    color: #ccc;
}

/* Barra lateral derecha */
.sidebar-right {
    padding: 20px 15px;
    background-color: #f0f2f5;
    width: 280px;
    margin-left: auto;
}

.advertisements-section {
    position: sticky;
    top: 20px;
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
    width: 250px;
    margin: 20px auto;
}

.search-bar {
    background-color: white;
    border-radius: 20px;
    padding: 10px 15px;
    display: flex;
    align-items: center;
    margin: 0 auto 20px;
    border: 1px solid #e0e0e0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    width: 250px;
}

.search-bar i {
    color: #666;
    margin-right: 10px;
}

.search-bar input {
    border: none;
    outline: none;
    width: 100%;
    font-size: 14px;
    color: #333;
}

.opportunities-section {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
    width: 250px;
    margin: 0 auto;
}

.opportunities-section h3 {
    font-size: 16px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    color: #333;
    font-weight: 600;
}

.opportunities-section h3 i {
    margin-right: 10px;
    color: #6a0dad; /* Cambiado a color púrpura */
    font-size: 18px;
}

.opportunities-subtitle {
    font-size: 13px;
    color: #666;
    margin-bottom: 20px;
    margin-top: -15px;
    padding-left: 28px;
}

.opportunity {
    padding: 15px;
    border-bottom: 1px solid #e0e0e0;
    transition: all 0.3s;
    cursor: pointer;
    border-radius: 8px;
    margin-bottom: 5px;
}

.opportunity:hover {
    background-color: #e6f0ff;
    transform: translateY(-2px);
    box-shadow: 0 3px 8px rgba(0, 102, 204, 0.1);
}

.opportunity:last-child {
    border-bottom: none;
}

.opportunity:not(:last-child) {
    margin-bottom: 5px;
}

.opportunity-header {
    margin-bottom: 8px;
}

.sport-tag {
    font-size: 12px;
    color: #666;
    display: flex;
    align-items: center;
}

.sport-tag i {
    margin-right: 5px;
    font-size: 14px;
}

/* Colores específicos para cada deporte - Variaciones de púrpura */
.tag-football i { color: #8e44ad; } /* Púrpura medio */
.tag-basketball i { color: #9b59b6; } /* Púrpura claro */
.tag-volleyball i { color: #6a0dad; } /* Púrpura principal */
.tag-baseball i { color: #5d3fd3; } /* Púrpura azulado */
.tag-cricket i { color: #7d3c98; } /* Púrpura oscuro */
.tag-hockey i { color: #a569bd; } /* Púrpura rosado */

.opportunity h4 {
    font-size: 15px;
    margin-bottom: 10px;
    font-weight: 600;
    color: #333;
    line-height: 1.4;
}

.opportunity-details {
    font-size: 12px;
    color: #666;
}

.opportunity-date, .opportunity-time {
    margin-bottom: 5px;
    display: flex;
    align-items: center;
}

.opportunity-date i, .opportunity-time i {
    margin-right: 5px;
    width: 14px;
}

.spots-available {
    font-size: 12px;
    color: #6a0dad; /* Cambiado a color púrpura */
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.spots-available i {
    margin-right: 5px;
}

.view-more-button {
    background-color: transparent;
    color: #6a0dad; /* Cambiado a color púrpura */
    border: none;
    padding: 12px 0;
    width: 100%;
    text-align: center;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    margin-top: 15px;
    border-top: 1px solid #e0e0e0;
    transition: all 0.3s;
    display: flex;
    justify-content: center;
    align-items: center;
}

.view-more-button i {
    margin-left: 8px;
    transition: transform 0.3s;
}

.view-more-button:hover {
    color: #8e44ad; /* Púrpura más claro al pasar el ratón */
}

.view-more-button:hover i {
    transform: translateX(5px);
}

/* Premium Card Styles */
.premium-card {
    background: linear-gradient(135deg, #6a0dad 0%, #8e44ad 100%);
    border-radius: 10px;
    padding: 15px;
    margin: 20px auto;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(106, 13, 173, 0.3);
    text-decoration: none;
    width: 250px;
    position: relative;
    overflow: hidden;
}

.premium-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
    z-index: 1;
}

.premium-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(106, 13, 173, 0.4);
}

.premium-card-icon {
    background-color: rgba(255, 255, 255, 0.2);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 20px;
    color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    z-index: 2;
}

.premium-card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    z-index: 2;
}

.premium-card-title {
    color: white;
    font-weight: 700;
    font-size: 16px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.premium-card-subtitle {
    color: rgba(255, 255, 255, 0.9);
    font-size: 12px;
    margin-top: 3px;
    line-height: 1.3;
}

.premium-card-arrow {
    color: white;
    font-size: 18px;
    opacity: 0.8;
    transition: transform 0.3s;
    z-index: 2;
}

.premium-card:hover .premium-card-arrow {
    transform: translateX(5px);
}

/* Advertisements Section Styles */
.advertisements-section {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
    width: 250px;
    margin: 20px auto;
}

.advertisements-section h3 {
    font-size: 16px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    color: #333;
    font-weight: 600;
}

.advertisements-section h3 i {
    margin-right: 10px;
    color: #6a0dad;
    font-size: 18px;
}

.ad-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 10px;
    border: 1px solid #eee;
    transition: all 0.2s ease;
}

.ad-item:hover {
    background-color: #f0f2f5;
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.ad-item:last-child {
    margin-bottom: 0;
}

.ad-image {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: 5px;
    margin-right: 10px;
    border: 1px solid #ddd;
}

.ad-text {
    font-size: 13px;
    color: #555;
    line-height: 1.4;
    margin: 0;
}

/* Sección de solicitudes de seguimiento */
.follow-requests-section {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
    width: 250px;
    margin: 0 auto;
}

.follow-requests-section h3 {
    font-size: 16px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    color: #333;
    font-weight: 600;
}

.follow-requests-section h3 i {
    margin-right: 10px;
    color: #0066cc;
    font-size: 18px;
}

.follow-requests-subtitle {
    font-size: 13px;
    color: #666;
    margin-bottom: 20px;
    margin-top: -15px;
    padding-left: 28px;
}

.follow-request {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid #e0e0e0;
    transition: all 0.3s;
    border-radius: 8px;
}

.follow-request:hover {
    background-color: #e6f0ff;
}

.follow-request:last-child {
    border-bottom: none;
    margin-bottom: 5px;
}

.request-user {
    display: flex;
    align-items: center;
}

.request-user-pic {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
    border: 1px solid #e0e0e0;
    object-fit: cover;
}

.request-user-info h4 {
    font-size: 14px;
    margin-bottom: 3px;
    font-weight: 600;
    color: #333;
}

.request-user-info p {
    font-size: 12px;
    color: #666;
    margin: 0;
}

.request-actions {
    display: flex;
}

.accept-button, .decline-button {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-left: 5px;
    transition: all 0.3s;
}

.accept-button {
    background-color: #2ecc71;
    color: white;
}

.decline-button {
    background-color: #e74c3c;
    color: white;
}

.accept-button:hover, .decline-button:hover {
    transform: scale(1.1);
}

.view-all-button {
    background-color: transparent;
    color: #6a0dad; /* Cambiado a color púrpura */
    border: none;
    padding: 12px 0;
    width: 100%;
    text-align: center;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    margin-top: 10px;
    border-top: 1px solid #e0e0e0;
    transition: all 0.3s;
    display: flex;
    justify-content: center;
    align-items: center;
}

.view-all-button:hover {
    color: #8e44ad; /* Púrpura más claro al pasar el ratón */
}

.request-count {
    background-color: #6a0dad; /* Cambiado a color púrpura */
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    margin-left: 8px;
}

/* Botones flotantes */
.floating-buttons {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    z-index: 100;
}

.chat-button, .top-button {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    border: none;
    margin-top: 10px;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: transform 0.2s, box-shadow 0.2s;
}

.chat-button:hover, .top-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
}

.chat-button {
    background-color: #6a0dad; /* Cambiado a color púrpura */
    color: white;
}

.top-button {
    background-color: white;
    color: #333;
    border: 1px solid #e0e0e0;
}

/* Premium Card Styles */
.premium-card {
    background: linear-gradient(135deg, #6a0dad 0%, #8e44ad 100%);
    border-radius: 10px;
    padding: 15px;
    margin: 20px auto;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(106, 13, 173, 0.3);
    text-decoration: none;
    width: 250px;
    position: relative;
    overflow: hidden;
}

.premium-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
    z-index: 1;
}

.premium-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(106, 13, 173, 0.4);
}

.premium-card-icon {
    background-color: rgba(255, 255, 255, 0.2);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 20px;
    color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    z-index: 2;
}

.premium-card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    z-index: 2;
}

.premium-card-title {
    color: white;
    font-weight: 700;
    font-size: 16px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.premium-card-subtitle {
    color: rgba(255, 255, 255, 0.9);
    font-size: 12px;
    margin-top: 3px;
    line-height: 1.3;
}

.premium-card-arrow {
    color: white;
    font-size: 18px;
    opacity: 0.8;
    transition: transform 0.3s;
    z-index: 2;
}

.premium-card:hover .premium-card-arrow {
    transform: translateX(5px);
}

/* Advertisements Section Styles */
.advertisements-section {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
    width: 250px;
    margin: 20px auto;
}

.advertisements-section h3 {
    font-size: 16px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    color: #333;
    font-weight: 600;
}

.advertisements-section h3 i {
    margin-right: 10px;
    color: #6a0dad;
    font-size: 18px;
}

.ad-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 10px;
    border: 1px solid #eee;
    transition: all 0.2s ease;
}

.ad-item:hover {
    background-color: #f0f2f5;
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.ad-item:last-child {
    margin-bottom: 0;
}

.ad-image {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: 5px;
    margin-right: 10px;
    border: 1px solid #ddd;
}

.ad-text {
    font-size: 13px;
    color: #555;
    line-height: 1.4;
    margin: 0;
}

/* Sección de solicitudes de seguimiento */
.follow-requests-section {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
    width: 250px;
    margin: 0 auto;
}

.follow-requests-section h3 {
    font-size: 16px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    color: #333;
    font-weight: 600;
}

.follow-requests-section h3 i {
    margin-right: 10px;
    color: #0066cc;
    font-size: 18px;
}

.follow-requests-subtitle {
    font-size: 13px;
    color: #666;
    margin-bottom: 20px;
    margin-top: -15px;
    padding-left: 28px;
}

.follow-request {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid #e0e0e0;
    transition: all 0.3s;
    border-radius: 8px;
}

.follow-request:hover {
    background-color: #e6f0ff;
}

.follow-request:last-child {
    border-bottom: none;
    margin-bottom: 5px;
}

.request-user {
    display: flex;
    align-items: center;
}

.request-user-pic {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
    border: 1px solid #e0e0e0;
    object-fit: cover;
}

.request-user-info h4 {
    font-size: 14px;
    margin-bottom: 3px;
    font-weight: 600;
    color: #333;
}

.request-user-info p {
    font-size: 12px;
    color: #666;
    margin: 0;
}

.request-actions {
    display: flex;
}

.accept-button, .decline-button {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-left: 5px;
    transition: all 0.3s;
}

.accept-button {
    background-color: #2ecc71;
    color: white;
}

.decline-button {
    background-color: #e74c3c;
    color: white;
}

.accept-button:hover, .decline-button:hover {
    transform: scale(1.1);
}

.view-all-button {
    background-color: transparent;
    color: #6a0dad; /* Cambiado a color púrpura */
    border: none;
    padding: 12px 0;
    width: 100%;
    text-align: center;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    margin-top: 10px;
    border-top: 1px solid #e0e0e0;
    transition: all 0.3s;
    display: flex;
    justify-content: center;
    align-items: center;
}

.view-all-button:hover {
    color: #8e44ad; /* Púrpura más claro al pasar el ratón */
}

.request-count {
    background-color: #6a0dad; /* Cambiado a color púrpura */
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    margin-left: 8px;
}

/* Botones flotantes */
.floating-buttons {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    z-index: 100;
}

.chat-button, .top-button {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    border: none;
    margin-top: 10px;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: transform 0.2s, box-shadow 0.2s;
}

.chat-button:hover, .top-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
}

.chat-button {
    background-color: #6a0dad; /* Cambiado a color púrpura */
    color: white;
}

.top-button {
    background-color: white;
    color: #333;
    border: 1px solid #e0e0e0;
}

/* Premium Card Styles */
.premium-card {
    background: linear-gradient(135deg, #6a0dad 0%, #8e44ad 100%);
    border-radius: 10px;
    padding: 15px;
    margin: 20px auto;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(106, 13, 173, 0.3);
    text-decoration: none;
    width: 250px;
    position: relative;
    overflow: hidden;
}

.premium-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
    z-index: 1;
}

.premium-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(106, 13, 173, 0.4);
}

.premium-card-icon {
    background-color: rgba(255, 255, 255, 0.2);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 20px;
    color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    z-index: 2;
}

.premium-card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    z-index: 2;
}

.premium-card-title {
    color: white;
    font-weight: 700;
    font-size: 16px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.premium-card-subtitle {
    color: rgba(255, 255, 255, 0.9);
    font-size: 12px;
    margin-top: 3px;
    line-height: 1.3;
}

.premium-card-arrow {
    color: white;
    font-size: 18px;
    opacity: 0.8;
    transition: transform 0.3s;
    z-index: 2;
}

.premium-card:hover .premium-card-arrow {
    transform: translateX(5px);
}

/* Advertisements Section Styles */
.advertisements-section {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
    width: 250px;
    margin: 20px auto;
}

.advertisements-section h3 {
    font-size: 16px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    color: #333;
    font-weight: 600;
}

.advertisements-section h3 i {
    margin-right: 10px;
    color: #6a0dad;
    font-size: 18px;
}

.ad-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 10px;
    border: 1px solid #eee;
    transition: all 0.2s ease;
}

.ad-item:hover {
    background-color: #f0f2f5;
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.ad-item:last-child {
    margin-bottom: 0;
}

.ad-image {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: 5px;
    margin-right: 10px;
    border: 1px solid #ddd;
}

.ad-text {
    font-size: 13px;
    color: #555;
    line-height: 1.4;
    margin: 0;
}

/* Sección de solicitudes de seguimiento */
.follow-requests-section {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
    width: 250px;
    margin: 0 auto;
}

.follow-requests-section h3 {
    font-size: 16px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    color: #333;
    font-weight: 600;
}

.follow-requests-section h3 i {
    margin-right: 10px;
    color: #0066cc;
    font-size: 18px;
}

.follow-requests-subtitle {
    font-size: 13px;
    color: #666;
    margin-bottom: 20px;
    margin-top: -15px;
    padding-left: 28px;
}

.follow-request {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid #e0e0e0;
    transition: all 0.3s;
    border-radius: 8px;
}

.follow-request:hover {
    background-color: #e6f0ff;
}

.follow-request:last-child {
    border-bottom: none;
    margin-bottom: 5px;
}

.request-user {
    display: flex;
    align-items: center;
}

.request-user-pic {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
    border: 1px solid #e0e0e0;
    object-fit: cover;
}

.request-user-info h4 {
    font-size: 14px;
    margin-bottom: 3px;
    font-weight: 600;
    color: #333;
}

.request-user-info p {
    font-size: 12px;
    color: #666;
    margin: 0;
}

.request-actions {
    display: flex;
}

.accept-button, .decline-button {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-left: 5px;
    transition: all 0.3s;
}

.accept-button {
    background-color: #2ecc71;
    color: white;
}

.decline-button {
    background-color: #e74c3c;
    color: white;
}

.accept-button:hover, .decline-button:hover {
    transform: scale(1.1);
}

.view-all-button {
    background-color: transparent;
    color: #6a0dad; /* Cambiado a color púrpura */
    border: none;
    padding: 12px 0;
    width: 100%;
    text-align: center;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    margin-top: 10px;
    border-top: 1px solid #e0e0e0;
    transition: all 0.3s;
    display: flex;
    justify-content: center;
    align-items: center;
}

.view-all-button:hover {
    color: #8e44ad; /* Púrpura más claro al pasar el ratón */
}

.request-count {
    background-color: #6a0dad; /* Cambiado a color púrpura */
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    margin-left: 8px;
}

/* Botones flotantes */
.floating-buttons {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    z-index: 100;
}

.chat-button, .top-button {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    border: none;
    margin-top: 10px;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: transform 0.2s, box-shadow 0.2s;
}

.chat-button:hover, .top-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
}

.chat-button {
    background-color: #6a0dad; /* Cambiado a color púrpura */
    color: white;
}

.top-button {
    background-color: white;
    color: #333;
    border: 1px solid #e0e0e0;
}

/* Premium Card Styles */
.premium-card {
    background: linear-gradient(135deg, #6a0dad 0%, #8e44ad 100%);
    border-radius: 10px;
    padding: 15px;
    margin: 20px auto;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(106, 13, 173, 0.3);
    text-decoration: none;
    width: 250px;
    position: relative;
    overflow: hidden;
}

.premium-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
    z-index: 1;
}

.premium-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(106, 13, 173, 0.4);
}

.premium-card-icon {
    background-color: rgba(255, 255, 255, 0.2);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 20px;
    color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    z-index: 2;
}

.premium-card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    z-index: 2;
}

.premium-card-title {
    color: white;
    font-weight: 700;
    font-size: 16px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.premium-card-subtitle {
    color: rgba(255, 255, 255, 0.9);
    font-size: 12px;
    margin-top: 3px;
    line-height: 1.3;
}

.premium-card-arrow {
    color: white;
    font-size: 18px;
    opacity: 0.8;
    transition: transform 0.3s;
    z-index: 2;
}

.premium-card:hover .premium-card-arrow {
    transform: translateX(5px);
}

/* Advertisements Section Styles */
.advertisements-section {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
    width: 250px;
    margin: 20px auto;
}

.advertisements-section h3 {
    font-size: 16px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    color: #333;
    font-weight: 600;
}

.advertisements-section h3 i {
    margin-right: 10px;
    color: #6a0dad;
    font-size: 18px;
}

.ad-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 10px;
    border: 1px solid #eee;
    transition: all 0.2s ease;
}

.ad-item:hover {
    background-color: #f0f2f5;
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.ad-item:last-child {
    margin-bottom: 0;
}

.ad-image {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: 5px;
    margin-right: 10px;
    border: 1px solid #ddd;
}

.ad-text {
    font-size: 13px;
    color: #555;
    line-height: 1.4;
    margin: 0;
}

/* Sección de solicitudes de seguimiento */
.follow-requests-section {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
    width: 250px;
    margin: 0 auto;
}

.follow-requests-section h3 {
    font-size: 16px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    color: #333;
    font-weight: 600;
}

.follow-requests-section h3 i {
    margin-right: 10px;
    color: #0066cc;
    font-size: 18px;
}

.follow-requests-subtitle {
    font-size: 13px;
    color: #666;
    margin-bottom: 20px;
    margin-top: -15px;
    padding-left: 28px;
}

.follow-request {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid #e0e0e0;
    transition: all 0.3s;
    border-radius: 8px;
}

.follow-request:hover {
    background-color: #e6f0ff;
}

.follow-request:last-child {
    border-bottom: none;
    margin-bottom: 5px;
}

.request-user {
    display: flex;
    align-items: center;
}

.request-user-pic {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
    border: 1px solid #e0e0e0;
    object-fit: cover;
}

.request-user-info h4 {
    font-size: 14px;
    margin-bottom: 3px;
    font-weight: 600;
    color: #333;
}

.request-user-info p {
    font-size: 12px;
    color: #666;
    margin: 0;
}

.request-actions {
    display: flex;
}

.accept-button, .decline-button {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-left: 5px;
    transition: all 0.3s;
}

.accept-button {
    background-color: #2ecc71;
    color: white;
}

.decline-button {
    background-color: #e74c3c;
    color: white;
}

.accept-button:hover, .decline-button:hover {
    transform: scale(1.1);
}

.view-all-button {
    background-color: transparent;
    color: #6a0dad; /* Cambiado a color púrpura */
    border: none;
    padding: 12px 0;
    width: 100%;
    text-align: center;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    margin-top: 10px;
    border-top: 1px solid #e0e0e0;
    transition: all 0.3s;
    display: flex;
    justify-content: center;
    align-items: center;
}

.view-all-button:hover {
    color: #8e44ad; /* Púrpura más claro al pasar el ratón */
}

.request-count {
    background-color: #6a0dad; /* Cambiado a color púrpura */
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    margin-left: 8px;
}

/* Botones flotantes */
.floating-buttons {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    z-index: 100;
}

.chat-button, .top-button {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    border: none;
    margin-top: 10px;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: transform 0.2s, box-shadow 0.2s;
}

.chat-button:hover, .top-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
}

.chat-button {
    background-color: #6a0dad; /* Cambiado a color púrpura */
    color: white;
}

.top-button {
    background-color: white;
    color: #333;
    border: 1px solid #e0e0e0;
}

/* Premium Card Styles */
.premium-card {
    background: linear-gradient(135deg, #6a0dad 0%, #8e44ad 100%);
    border-radius: 10px;
    padding: 15px;
    margin: 20px auto;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(106, 13, 173, 0.3);
    text-decoration: none;
    width: 250px;
    position: relative;
    overflow: hidden;
}

.premium-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
    z-index: 1;
}

.premium-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(106, 13, 173, 0.4);
}

.premium-card-icon {
    background-color: rgba(255, 255, 255, 0.2);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 20px;
    color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    z-index: 2;
}

.premium-card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    z-index: 2;
}

.premium-card-title {
    color: white;
    font-weight: 700;
    font-size: 16px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.premium-card-subtitle {
    color: rgba(255, 255, 255, 0.9);
    font-size: 12px;
    margin-top: 3px;
    line-height: 1.3;
}

.premium-card-arrow {
    color: white;
    font-size: 18px;
    opacity: 0.8;
    transition: transform 0.3s;
    z-index: 2;
}

.premium-card:hover .premium-card-arrow {
    transform: translateX(5px);
}

/* Advertisements Section Styles */
.advertisements-section {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
    width: 250px;
    margin: 20px auto;
}

.advertisements-section h3 {
    font-size: 16px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    color: #333;
    font-weight: 600;
}

.advertisements-section h3 i {
    margin-right: 10px;
    color: #6a0dad;
    font-size: 18px;
}

.ad-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 10px;
    border: 1px solid #eee;
    transition: all 0.2s ease;
}

.ad-item:hover {
    background-color: #f0f2f5;
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.ad-item:last-child {
    margin-bottom: 0;
}

.ad-image {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: 5px;
    margin-right: 10px;
    border: 1px solid #ddd;
}

.ad-text {
    font-size: 13px;
    color: #555;
    line-height: 1.4;
    margin: 0;
}

/* Sección de solicitudes de seguimiento */
.follow-requests-section {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
    width: 250px;
    margin: 0 auto;
}

.follow-requests-section h3 {
    font-size: 16px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    color: #333;
    font-weight: 600;
}

.follow-requests-section h3 i {
    margin-right: 10px;
    color: #0066cc;
    font-size: 18px;
}

.follow-requests-subtitle {
    font-size: 13px;
    color: #666;
    margin-bottom: 20px;
    margin-top: -15px;
    padding-left: 28px;
}

.follow-request {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid #e0e0e0;
    transition: all 0.3s;
    border-radius: 8px;
}

.follow-request:hover {
    background-color: #e6f0ff;
}

.follow-request:last-child {
    border-bottom: none;
    margin-bottom: 5px;
}

.request-user {
    display: flex;
    align-items: center;
}

.request-user-pic {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
    border: 1px solid #e0e0e0;
    object-fit: cover;
}

.request-user-info h4 {
    font-size: 14px;
    margin-bottom: 3px;
    font-weight: 600;
    color: #333;
}

.request-user-info p {
    font-size: 12px;
    color: #666;
    margin: 0;
}

.request-actions {
    display: flex;
}

.accept-button, .decline-button {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-left: 5px;
    transition: all 0.3s;
}

.accept-button {
    background-color: #2ecc71;
    color: white;
}

.decline-button {
    background-color: #e74c3c;
    color: white;
}

.accept-button:hover, .decline-button:hover {
    transform: scale(1.1);
}

.view-all-button {
    background-color: transparent;
    color: #6a0dad; /* Cambiado a color púrpura */
    border: none;
    padding: 12px 0;
    width: 100%;
    text-align: center;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    margin-top: 10px;
    border-top: 1px solid #e0e0e0;
    transition: all 0.3s;
    display: flex;
    justify-content: center;
    align-items: center;
}

.view-all-button:hover {
    color: #8e44ad; /* Púrpura más claro al pasar el ratón */
}

.request-count {
    background-color: #6a0dad; /* Cambiado a color púrpura */
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    margin-left: 8px;
}

/* Botones flotantes */
.floating-buttons {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    z-index: 100;
}

.chat-button, .top-button {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    border: none;
    margin-top: 10px;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: transform 0.2s, box-shadow 0.2s;
}

.chat-button:hover, .top-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
}

.chat-button {
    background-color: #6a0dad; /* Cambiado to color púrpura */
    color: white;
}

.top-button {
    background-color: white;
    color: #333;
    border: 1px solid #e0e0e0;
}

/* Premium Card Styles */
.premium-card {
    background: linear-gradient(135deg, #6a0dad 0%, #8e44ad 100%);
    border-radius: 10px;
    padding: 15px;
    margin: 20px auto;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(106, 13, 173, 0.3);
    text-decoration: none;
    width: 250px;
    position: relative;
    overflow: hidden;
}

.premium-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
    z-index: 1;
}

.premium-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(106, 13, 173, 0.4);
}

.premium-card-icon {
    background-color: rgba(255, 255, 255, 0.2);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 20px;
    color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    z-index: 2;
}

.premium-card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    z-index: 2;
}

.premium-card-title {
    color: white;
    font-weight: 700;
    font-size: 16px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.premium-card-subtitle {
    color: rgba(255, 255, 255, 0.9);
    font-size: 12px;
    margin-top: 3px;
    line-height: 1.3;
}

.premium-card-arrow {
    color: white;
    font-size: 18px;
    opacity: 0.8;
    transition: transform 0.3s;
    z-index: 2;
}

.premium-card:hover .premium-card-arrow {
    transform: translateX(5px);
}

/* Advertisements Section Styles */
.advertisements-section {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
    width: 250px;
    margin: 20px auto;
}

.advertisements-section h3 {
    font-size: 16px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    color: #333;
    font-weight: 600;
}

.advertisements-section h3 i {
    margin-right: 10px;
    color: #6a0dad;
    font-size: 18px;
}

.ad-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 10px;
    border: 1px solid #eee;
    transition: all 0.2s ease;
}

.ad-item:hover {
    background-color: #f0f2f5;
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.ad-item:last-child {
    margin-bottom: 0;
}

.ad-image {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: 5px;
    margin-right: 10px;
    border: 1px solid #ddd;
}

.ad-text {
    font-size: 13px;
    color: #555;
    line-height: 1.4;
    margin: 0;
}

/* Sección de solicitudes de seguimiento */
.follow-requests-section {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
    width: 250px;
    margin: 0 auto;
}

.follow-requests-section h3 {
    font-size: 16px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    color: #333;
    font-weight: 600;
}

.follow-requests-section h3 i {
    margin-right: 10px;
    color: #0066cc;
    font-size: 18px;
}

.follow-requests-subtitle {
    font-size: 13px;
    color: #666;
    margin-bottom: 20px;
    margin-top: -15px;
    padding-left: 28px;
}

.follow-request {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid #e0e0e0;
    transition: all 0.3s;
    border-radius: 8px;
}

.follow-request:hover {
    background-color: #e6f0ff;
}

.follow-request:last-child {
    border-bottom: none;
    margin-bottom: 5px;
}

.request-user {
    display: flex;
    align-items: center;
}

.request-user-pic {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
    border: 1px solid #e0e0e0;
    object-fit: cover;
}

.request-user-info h4 {
    font-size: 14px;
    margin-bottom: 3px;
    font-weight: 600;
    color: #333;
}

.request-user-info p {
    font-size: 12px;
    color: #666;
    margin: 0;
}

.request-actions {
    display: flex;
}

.accept-button, .decline-button {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-left: 5px;
    transition: all 0.3s;
}

.accept-button {
    background-color: #2ecc71;
    color: white;
}

.decline-button {
    background-color: #e74c3c;
    color: white;
}

.accept-button:hover, .decline-button:hover {
    transform: scale(1.1);
}

.view-all-button {
    background-color: transparent;
    color: #6a0dad; /* Cambiado a color púrpura */
    border: none;
    padding: 12px 0;
    width: 100%;
    text-align: center;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    margin-top: 10px;
    border-top: 1px solid #e0e0e0;
    transition: all 0.3s;
    display: flex;
    justify-content: center;
    align-items: center;
}

.view-all-button:hover {
    color: #8e44ad; /* Púrpura más claro al pasar el ratón */
}

.request-count {
    background-color: #6a0dad; /* Cambiado a color púrpura */
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    margin-left: 8px;
}

/* Botones flotantes */
.floating-buttons {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    z-index: 100;
}

.chat-button, .top-button {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    border: none;
    margin-top: 10px;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: transform 0.2s, box-shadow 0.2s;
}

.chat-button:hover, .top-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
}

.chat-button {
    background-color: #6a0dad; /* Cambiado to color púrpura */
    color: white;
}

.top-button {
    background-color: white;
    color: #333;
    border: 1px solid #e0e0e0;
}

/* Premium Card Styles */
.premium-card {
    background: linear-gradient(135deg, #6a0dad 0%, #8e44ad 100%);
    border-radius: 10px;
    padding: 15px;
    margin: 20px auto;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(106, 13, 173, 0.3);
    text-decoration: none;
    width: 250px;
    position: relative;
    overflow: hidden;
}

.premium-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
    z-index: 1;
}

.premium-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(106, 13, 173, 0.4);
}

.premium-card-icon {
    background-color: rgba(255, 255, 255, 0.2);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 20px;
    color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    z-index: 2;
}

.premium-card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    z-index: 2;
}

.premium-card-title {
    color: white;
    font-weight: 700;
    font-size: 16px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.premium-card-subtitle {
    color: rgba(255, 255, 255, 0.9);
    font-size: 12px;
    margin-top: 3px;
    line-height: 1.3;
}

.premium-card-arrow {
    color: white;
    font-size: 18px;
    opacity: 0.8;
    transition: transform 0.3s;
    z-index: 2;
}

.premium-card:hover .premium-card-arrow {
    transform: translateX(5px);
}

/* Advertisements Section Styles */
.advertisements-section {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
    width: 250px;
    margin: 20px auto;
}

.advertisements-section h3 {
    font-size: 16px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    color: #333;
    font-weight: 600;
}

.advertisements-section h3 i {
    margin-right: 10px;
    color: #6a0dad;
    font-size: 18px;
}

.ad-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 10px;
    border: 1px solid #eee;
    transition: all 0.2s ease;
}

.ad-item:hover {
    background-color: #f0f2f5;
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.ad-item:last-child {
    margin-bottom: 0;
}

.ad-image {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: 5px;
    margin-right: 10px;
    border: 1px solid #ddd;
}

.ad-text {
    font-size: 13px;
    color: #555;
    line-height: 1.4;
    margin: 0;
}

/* Sección de solicitudes de seguimiento */
.follow-requests-section {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
    width: 250px;
    margin: 0 auto;
}

.follow-requests-section h3 {
    font-size: 16px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    color: #333;
    font-weight: 600;
}

.follow-requests-section h3 i {
    margin-right: 10px;
    color: #0066cc;
    font-size: 18px;
}

.follow-requests-subtitle {
    font-size: 13px;
    color: #666;
    margin-bottom: 20px;
    margin-top: -15px;
    padding-left: 28px;
}

.follow-request {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid #e0e0e0;
    transition: all 0.3s;
    border-radius: 8px;
}

.follow-request:hover {
    background-color: #e6f0ff;
}

.follow-request:last-child {
    border-bottom: none;
    margin-bottom: 5px;
}

.request-user {
    display: flex;
    align-items: center;
}

.request-user-pic {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
    border: 1px solid #e0e0e0;
    object-fit: cover;
}

.request-user-info h4 {
    font-size: 14px;
    margin-bottom: 3px;
    font-weight: 600;
    color: #333;
}

.request-user-info p {
    font-size: 12px;
    color: #666;
    margin: 0;
}

.request-actions {
    display: flex;
}

.accept-button, .decline-button {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-left: 5px;
    transition: all 0.3s;
}

.accept-button {
    background-color: #2ecc71;
    color: white;
}

.decline-button {
    background-color: #e74c3c;
    color: white;
}

.accept-button:hover, .decline-button:hover {
    transform: scale(1.1);
}

.view-all-button {
    background-color: transparent;
    color: #6a0dad; /* Cambiado a color púrpura */
    border: none;
    padding: 12px 0;
    width: 100%;
    text-align: center;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    margin-top: 10px;
    border-top: 1px solid #e0e0e0;
    transition: all 0.3s;
    display: flex;
    justify-content: center;
    align-items: center;
}

.view-all-button:hover {
    color: #8e44ad; /* Púrpura más claro al pasar el ratón */
}

.request-count {
    background-color: #6a0dad; /* Cambiado a color púrpura */
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    margin-left: 8px;
}

/* Botones flotantes */
.floating-buttons {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    z-index: 100;
}

.chat-button, .top-button {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    border: none;
    margin-top: 10px;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: transform 0.2s, box-shadow 0.2s;
}

.chat-button:hover, .top-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
}

.chat-button {
    background-color: #6a0dad; /* Cambiado to color púrpura */
    color: white;
}

.top-button {
    background-color: white;
    color: #333;
    border: 1px solid #e0e0e0;
}

/* Premium Card Styles */
.premium-card {
    background: linear-gradient(135deg, #6a0dad 0%, #8e44ad 100%);
    border-radius: 10px;
    padding: 15px;
    margin: 20px auto;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(106, 13, 173, 0.3);
    text-decoration: none;
    width: 250px;
    position: relative;
    overflow: hidden;
}

.premium-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
    z-index: 1;
}

.premium-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(106, 13, 173, 0.4);
}

.premium-card-icon {
    background-color: rgba(255, 255, 255, 0.2);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 20px;
    color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    z-index: 2;
}

.premium-card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    z-index: 2;
}

.premium-card-title {
    color: white;
    font-weight: 700;
    font-size: 16px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.premium-card-subtitle {
    color: rgba(255, 255, 255, 0.9);
    font-size: 12px;
    margin-top: 3px;
    line-height: 1.3;
}

.premium-card-arrow {
    color: white;
    font-size: 18px;
    opacity: 0.8;
    transition: transform 0.3s;
    z-index: 2;
}

.premium-card:hover .premium-card-arrow {
    transform: translateX(5px);
}

/* Advertisements Section Styles */
.advertisements-section {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
    width: 250px;
    margin: 20px auto;
}

.advertisements-section h3 {
    font-size: 16px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    color: #333;
    font-weight: 600;
}

.advertisements-section h3 i {
    margin-right: 10px;
    color: #6a0dad;
    font-size: 18px;
}

.ad-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 10px;
    border: 1px solid #eee;
    transition: all 0.2s ease;
}

.ad-item:hover {
    background-color: #f0f2f5;
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.ad-item:last-child {
    margin-bottom: 0;
}

.ad-image {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: 5px;
    margin-right: 10px;
    border: 1px solid #ddd;
}

.ad-text {
    font-size: 13px;
    color: #555;
    line-height: 1.4;
    margin: 0;
}

/* Sección de solicitudes de seguimiento */
.follow-requests-section {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
    width: 250px;
    margin: 0 auto;
}

.follow-requests-section h3 {
    font-size: 16px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    color: #333;
    font-weight: 600;
}

.follow-requests-section h3 i {
    margin-right: 10px;
    color: #0066cc;
    font-size: 18px;
}

.follow-requests-subtitle {
    font-size: 13px;
    color: #666;
    margin-bottom: 20px;
    margin-top: -15px;
    padding-left: 28px;
}

.follow-request {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid #e0e0e0;
    transition: all 0.3s;
    border-radius: 8px;
}

.follow-request:hover {
    background-color: #e6f0ff;
}

.follow-request:last-child {
    border-bottom: none;
    margin-bottom: 5px;
}

.request-user {
    display: flex;
    align-items: center;
}

.request-user-pic {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
    border: 1px solid #e0e0e0;
    object-fit: cover;
}

.request-user-info h4 {
    font-size: 14px;
    margin-bottom: 3px;
    font-weight: 600;
    color: #333;
}

.request-user-info p {
    font-size: 12px;
    color: #666;
    margin: 0;
}

.request-actions {
    display: flex;
}

.accept-button, .decline-button {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-left: 5px;
    transition: all 0.3s;
}

.accept-button {
    background-color: #2ecc71;
    color: white;
}

.decline-button {
    background-color: #e74c3c;
    color: white;
}

.accept-button:hover, .decline-button:hover {
    transform: scale(1.1);
}

.view-all-button {
    background-color: transparent;
    color: #6a0dad; /* Cambiado a color púrpura */
    border: none;
    padding: 12px 0;
    width: 100%;
    text-align: center;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    margin-top: 10px;
    border-top: 1px solid #e0e0e0;
    transition: all 0.3s;
    display: flex;
    justify-content: center;
    align-items: center;
}

.view-all-button:hover {
    color: #8e44ad; /* Púrpura más claro al pasar el ratón */
}

.request-count {
    background-color: #6a0dad; /* Cambiado a color púrpura */
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    margin-left: 8px;
}

/* Botones flotantes */
.floating-buttons {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    z-index: 100;
}

.chat-button, .top-button {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    border: none;
    margin-top: 10px;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: transform 0.2s, box-shadow 0.2s;
}

.chat-button:hover, .top-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
}

.chat-button {
    background-color: #6a0dad; /* Cambiado to color púrpura */
    color: white;
}

.top-button {
    background-color: white;
    color: #333;
    border: 1px solid #e0e0e0;
}

/* Premium Card Styles */
.premium-card {
    background: linear-gradient(135deg, #6a0dad 0%, #8e44ad 100%);
    border-radius: 10px;
    padding: 15px;
    margin: 20px auto;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(106, 13, 173, 0.3);
    text-decoration: none;
    width: 250px;
    position: relative;
    overflow: hidden;
}

.premium-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
    z-index: 1;
}

.premium-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(106, 13, 173, 0.4);
}

.premium-card-icon {
    background-color: rgba(255, 255, 255, 0.2);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 20px;
    color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    z-index: 2;
}

.premium-card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    z-index: 2;
}

.premium-card-title {
    color: white;
    font-weight: 700;
    font-size: 16px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.premium-card-subtitle {
    color: rgba(255, 255, 255, 0.9);
    font-size: 12px;
    margin-top: 3px;
    line-height: 1.3;
}

.premium-card-arrow {
    color: white;
    font-size: 18px;
    opacity: 0.8;
    transition: transform 0.3s;
    z-index: 2;
}

.premium-card:hover .premium-card-arrow {
    transform: translateX(5px);
}

/* Advertisements Section Styles */
.advertisements-section {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
    width: 250px;
    margin: 20px auto;
}

.advertisements-section h3 {
    font-size: 16px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    color: #333;
    font-weight: 600;
}

.advertisements-section h3 i {
    margin-right: 10px;
    color: #6a0dad;
    font-size: 18px;
}

.ad-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 10px;
    border: 1px solid #eee;
    transition: all 0.2s ease;
}

.ad-item:hover {
    background-color: #f0f2f5;
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.ad-item:last-child {
    margin-bottom: 0;
}

.ad-image {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: 5px;
    margin-right: 10px;
    border: 1px solid #ddd;
}

.ad-text {
    font-size: 13px;
    color: #555;
    line-height: 1.4;
    margin: 0;
}

/* Sección de solicitudes de seguimiento */
.follow-requests-section {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
    width: 250px;
    margin: 0 auto;
}

.follow-requests-section h3 {
    font-size: 16px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    color: #333;
    font-weight: 600;
}

.follow-requests-section h3 i {
    margin-right: 10px;
    color: #0066cc;
    font-size: 18px;
}

.follow-requests-subtitle {
    font-size: 13px;
    color: #666;
    margin-bottom: 20px;
    margin-top: -15px;
    padding-left: 28px;
}

.follow-request {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid #e0e0e0;
    transition: all 0.3s;
    border-radius: 8px;
}

.follow-request:hover {
    background-color: #e6f0ff;
}

.follow-request:last-child {
    border-bottom: none;
    margin-bottom: 5px;
}

.request-user {
    display: flex;
    align-items: center;
}

.request-user-pic {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
    border: 1px solid #e0e0e0;
    object-fit: cover;
}

.request-user-info h4 {
    font-size: 14px;
    margin-bottom: 3px;
    font-weight: 600;
    color: #333;
}

.request-user-info p {
    font-size: 12px;
    color: #666;
    margin: 0;
}

.request-actions {
    display: flex;
}

.accept-button, .decline-button {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-left: 5px;
    transition: all 0.3s;
}

.accept-button {
    background-color: #2ecc71;
    color: white;
}

.decline-button {
    background-color: #e74c3c;
    color: white;
}

.decline-button:hover, .accept-button:hover {
    transform: scale(1.1);
}

.view-all-button {
    background-color: transparent;
    color: #6a0dad; /* Cambiado a color púrpura */
    border: none;
    padding: 12px 0;
    width: 100%;
    text-align: center;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    margin-top: 10px;
    border-top: 1px solid #e0e0e0;
    transition: all 0.3s;
    display: flex;
    justify-content: center;
    align-items: center;
}

.view-all-button:hover {
    color: #8e44ad; /* Púrpura más claro al pasar el ratón */
}

.request-count {
    background-color: #6a0dad; /* Cambiado a color púrpura */
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    margin-left: 8px;
}

/* Botones flotantes */
.floating-buttons {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    z-index: 100;
}

.chat-button, .top-button {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    border: none;
    margin-top: 10px;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: transform 0.2s, box-shadow 0.2s;
}

.chat-button:hover, .top-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
}

.chat-button {
    background-color: #6a0dad; /* Cambiado to color púrpura */
    color: white;
}

.top-button {
    background-color: white;
    color: #333;
    border: 1px solid #e0e0e0;
}

/* Premium Card Styles */
.premium-card {
    background: linear-gradient(135deg, #6a0dad 0%, #8e44ad 100%);
    border-radius: 10px;
    padding: 15px;
    margin: 20px auto;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(106, 13, 173, 0.3);
    text-decoration: none;
    width: 250px;
    position: relative;
    overflow: hidden;
}

.premium-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
    z-index: 1;
}

.premium-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(106, 13, 173, 0.4);
}

.premium-card-icon {
    background-color: rgba(255, 255, 255, 0.2);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 20px;
    color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    z-index: 2;
}

.premium-card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    z-index: 2;
}

.premium-card-title {
    color: white;
    font-weight: 700;
    font-size: 16px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.premium-card-subtitle {
    color: rgba(255, 255, 255, 0.9);
    font-size: 12px;
    margin-top: 3px;
    line-height: 1.3;
}

.premium-card-arrow {
    color: white;
    font-size: 18px;
    opacity: 0.8;
    transition: transform 0.3s;
    z-index: 2;
}

.premium-card:hover .premium-card-arrow {
    transform: translateX(5px);
}

/* Advertisements Section Styles */
.advertisements-section {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
    width: 250px;
    margin: 20px auto;
}

.advertisements-section h3 {
    font-size: 16px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    color: #333;
    font-weight: 600;
}

.advertisements-section h3 i {
    margin-right: 10px;
    color: #6a0dad;
    font-size: 18px;
}

.ad-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 10px;
    border: 1px solid #eee;
    transition: all 0.2s ease;
}

.ad-item:hover {
    background-color: #f0f2f5;
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.ad-item:last-child {
    margin-bottom: 0;
}

.ad-image {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: 5px;
    margin-right: 10px;
    border: 1px solid #ddd;
}

.ad-text {
    font-size: 13px;
    color: #555;
    line-height: 1.4;
    margin: 0;
}

/* Sección de solicitudes de seguimiento */
.follow-requests-section {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
    width: 250px;
    margin: 0 auto;
}

.follow-requests-section h3 {
    font-size: 16px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    color: #333;
    font-weight: 600;
}

.follow-requests-section h3 i {
    margin-right: 10px;
    color: #0066cc;
    font-size: 18px;
}

.follow-requests-subtitle {
    font-size: 13px;
    color: #666;
    margin-bottom: 20px;
    margin-top: -15px;
    padding-left: 28px;
}

.follow-request {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid #e0e0e0;
    transition: all 0.3s;
    border-radius: 8px;
}

.follow-request:hover {
    background-color: #e6f0ff;
}

.follow-request:last-child {
    border-bottom: none;
    margin-bottom: 5px;
}

.request-user {
    display: flex;
    align-items: center;
}

.request-user-pic {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
    border: 1px solid #e0e0e0;
    object-fit: cover;
}

.request-user-info h4 {
    font-size: 14px;
    margin-bottom: 3px;
    font-weight: 600;
    color: #333;
}

.request-user-info p {
    font-size: 12px;
    color: #666;
    margin: 0;
}

.request-actions {
    display: flex;
}

.accept-button, .decline-button {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-left: 5px;
    transition: all 0.3s;
}

.accept-button {
    background-color: #2ecc71;
    color: white;
}

.decline-button {
    background-color: #e74c3c;
    color: white;
}

.decline-button:hover, .accept-button:hover {
    transform: scale(1.1);
}

.view-all-button {
    background-color: transparent;
    color: #6a0dad; /* Cambiado a color púrpura */
    border: none;
    padding: 12px 0;
    width: 100%;
    text-align: center;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    margin-top: 10px;
    border-top: 1px solid #e0e0e0;
    transition: all 0.3s;
    display: flex;
    justify-content: center;
    align-items: center;
}

.view-all-button:hover {
    color: #8e44ad; /* Púrpura más clear al pasar el ratón */
}

.request-count {
    background-color: #6a0dad; /* Cambiado a color púrpura */
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    margin-left: 8px;
}

/* Botones flotantes */
.floating-buttons {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    z-index: 100;
}

.chat-button, .top-button {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    border: none;
    margin-top: 10px;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: transform 0.2s, box-shadow 0.2s;
}

.chat-button:hover, .top-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
}

.chat-button {
    background-color: #6a0dad; /* Cambiado to color púrpura */
    color: white;
}

.top-button {
    background-color: white;
    color: #333;
    border: 1px solid #e0e0e0;
}

/* Premium Card Styles */
.premium-card {
    background: linear-gradient(135deg, #6a0dad 0%, #8e44ad 100%);
    border-radius: 10px;
    padding: 15px;
    margin: 20px auto;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(106, 13, 173, 0.3);
    text-decoration: none;
    width: 250px;
    position: relative;
    overflow: hidden;
}

.premium-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
    z-index: 1;
}

.premium-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(106, 13, 173, 0.4);
}

.premium-card-icon {
    background-color: rgba(255, 255, 255, 0.2);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 20px;
    color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    z-index: 2;
}

.premium-card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    z-index: 2;
}

.premium-card-title {
    color: white;
    font-weight: 700;
    font-size: 16px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.premium-card-subtitle {
    color: rgba(255, 255, 255, 0.9);
    font-size: 12px;
    margin-top: 3px;
    line-height: 1.3;
}

.premium-card-arrow {
    color: white;
    font-size: 18px;
    opacity: 0.8;
    transition: transform 0.3s;
    z-index: 2;
}

.premium-card:hover .premium-card-arrow {
    transform: translateX(5px);
}

/* Advertisements Section Styles */
.advertisements-section {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
    width: 250px;
    margin: 20px auto;
}

.advertisements-section h3 {
    font-size: 16px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    color: #333;
    font-weight: 600;
}

.advertisements-section h3 i {
    margin-right: 10px;
    color: #6a0dad;
    font-size: 18px;
}

.ad-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 10px;
    border: 1px solid #eee;
    transition: all 0.2s ease;
}

.ad-item:hover {
    background-color: #f0f2f5;
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.ad-item:last-child {
    margin-bottom: 0;
}

.ad-image {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: 5px;
    margin-right: 10px;
    border: 1px solid #ddd;
}

.ad-text {
    font-size: 13px;
    color: #555;
    line-height: 1.4;
    margin: 0;
}

/* Sección de solicitudes de seguimiento */
.follow-requests-section {
    background-color: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
    width: 250px;
    margin: 0 auto;
}

.follow-requests-section h3 {
    font-size: 16px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    color: #333;
    font-weight: 600;
}

.follow-requests-section h3 i {
    margin-right: 10px;
    color: #0066cc;
    font-size: 18px;
}

.follow-requests-subtitle {
    font-size: 13px;
    color: #666;
    margin-bottom: 20px;
    margin-top: -15px;
    padding-left: 28px;
}

.follow-request {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid #e0e0e0;
    transition: all 0.3s;
    border-radius: 8px;
}

.follow-request:hover {
    background-color: #e6f0ff;
}

.follow-request:last-child {
    border-bottom: none;
    margin-bottom: 5px;
}

.request-user {
    display: flex;
    align-items: center;
}

.request-user-pic {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
    border: 1px solid #e0e0e0;
    object-fit: cover;
}

.request-user-info h4 {
    font-size: 14px;
    margin-bottom: 3px;
    font-weight: 600;
    color: #333;
}

.request-user-info p {
    font-size: 12px;
    color: #666;
    margin: 0;
}

.request-actions {
    display: flex;
}

.accept-button, .decline-button {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-left: 5px;
    transition: all 0.3s;
}

.accept-button {
    background-color: #2ecc71;
    color: white;
}

.decline-button {
    background-color: #e74c3c;
    color: white;
}

.decline-button:hover, .accept-button:hover {
    transform: scale(1.1);
}

.view-all-button {
    background-color: transparent;
    color: #6a0dad; /* Cambiado a color púrpura */
    border: none;
    padding: 12px 0;
    width: 100%;
    text-align: center;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    margin-top: 10px;
    border-top: 1px solid #e0e0e0;
    transition: all 0.3s;
    display: flex;
    justify-content: center;
    align-items: center;
}

.view-all-button:hover {
    color: #8e44ad; /* Púrpura más clear al pasar el ratón */
}

.request-count {
    background-color: #6a0dad; /* Cambiado a color púrpura */
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    margin-left: 8px;
}

/* Responsive */
@media (max-width: 1024px) {
    .container {
        grid-template-columns: 220px 1fr;
    }
    .sidebar-right {
        display: none;
    }
}

@media (max-width: 768px) {
    .container {
        grid-template-columns: 1fr;
    }
    .sidebar-left {
        display: none;
    }
}

/* Discover Telyz Section */
.discover-telyz-section {
    background-color: #fff;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    margin-top: 20px; /* Added margin-top for spacing */
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.discover-telyz-section h3 {
    font-size: 16px;
    color: #333;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

.discover-telyz-section h3 .fa-compass {
    margin-right: 10px;
    color: #6a11ad;
}

.discover-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding: 8px 0; /* Added padding for better click area */
    border-radius: 5px;
    transition: background-color 0.2s ease;
    text-decoration: none; /* Remove underline for links */
    color: inherit; /* Inherit color from parent */
}

.discover-item:hover {
    background-color: #f0f2f5; /* Light background on hover */
    cursor: pointer;
}

.discover-item .discover-icon {
    font-size: 20px;
    color: #6a11ad;
    margin-right: 15px;
    width: 25px;
    text-align: center;
}

.discover-item .discover-text {
    font-size: 14px;
    color: #555;
    line-height: 1.4;
    flex-grow: 1; /* Allow text to take up remaining space */
}
