/**
 * User Validator
 * 
 * Validation middleware for user-related requests.
 */

const { body, validationResult } = require('express-validator');
const { SPORT_TYPES } = require('../../../shared/constants/sports');
const { USER_ROLES } = require('../../../shared/constants/users');

/**
 * Validate user creation/update
 */
exports.validateUser = [
  // First name validation
  body('firstName')
    .if(body('firstName').exists())
    .notEmpty()
    .withMessage('First name is required')
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters')
    .trim(),
  
  // Last name validation
  body('lastName')
    .if(body('lastName').exists())
    .notEmpty()
    .withMessage('Last name is required')
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters')
    .trim(),
  
  // Email validation
  body('email')
    .if(body('email').exists())
    .notEmpty()
    .withMessage('Email is required')
    .isEmail()
    .withMessage('Please provide a valid email address')
    .normalizeEmail(),
  
  // Password validation (only for creation)
  body('password')
    .if((value, { req }) => req.method === 'POST')
    .notEmpty()
    .withMessage('Password is required')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/[A-Z]/)
    .withMessage('Password must contain at least one uppercase letter')
    .matches(/[a-z]/)
    .withMessage('Password must contain at least one lowercase letter')
    .matches(/[0-9]/)
    .withMessage('Password must contain at least one number')
    .matches(/[!@#$%^&*(),.?":{}|<>]/)
    .withMessage('Password must contain at least one special character'),
  
  // Role validation
  body('role')
    .if(body('role').exists())
    .notEmpty()
    .withMessage('Role is required')
    .isIn(Object.values(USER_ROLES))
    .withMessage('Invalid role'),
  
  // Sport validation
  body('sport')
    .if(body('sport').exists())
    .notEmpty()
    .withMessage('Sport is required')
    .isIn(Object.values(SPORT_TYPES))
    .withMessage('Invalid sport'),
  
  // Position validation
  body('position')
    .if(body('position').exists())
    .notEmpty()
    .withMessage('Position is required')
    .isString()
    .withMessage('Position must be a string')
    .trim(),
  
  // Check for validation errors
  (req, res, next) => {
    const errors = validationResult(req);
    
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    
    next();
  },
];

/**
 * Validate password change
 */
exports.validatePasswordChange = [
  // Current password validation
  body('currentPassword')
    .notEmpty()
    .withMessage('Current password is required'),
  
  // New password validation
  body('newPassword')
    .notEmpty()
    .withMessage('New password is required')
    .isLength({ min: 8 })
    .withMessage('New password must be at least 8 characters long')
    .matches(/[A-Z]/)
    .withMessage('New password must contain at least one uppercase letter')
    .matches(/[a-z]/)
    .withMessage('New password must contain at least one lowercase letter')
    .matches(/[0-9]/)
    .withMessage('New password must contain at least one number')
    .matches(/[!@#$%^&*(),.?":{}|<>]/)
    .withMessage('New password must contain at least one special character'),
  
  // Confirm password validation
  body('confirmPassword')
    .notEmpty()
    .withMessage('Confirm password is required')
    .custom((value, { req }) => {
      if (value !== req.body.newPassword) {
        throw new Error('Passwords do not match');
      }
      return true;
    }),
  
  // Check for validation errors
  (req, res, next) => {
    const errors = validationResult(req);
    
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    
    next();
  },
];

/**
 * Validate profile update
 */
exports.validateProfileUpdate = [
  // Date of birth validation
  body('dateOfBirth')
    .if(body('dateOfBirth').exists())
    .isISO8601()
    .withMessage('Invalid date format')
    .toDate()
    .custom(value => {
      const now = new Date();
      const minDate = new Date();
      minDate.setFullYear(now.getFullYear() - 100);
      
      if (value > now) {
        throw new Error('Date of birth cannot be in the future');
      }
      
      if (value < minDate) {
        throw new Error('Date of birth is too far in the past');
      }
      
      return true;
    }),
  
  // Nationality validation
  body('nationality')
    .if(body('nationality').exists())
    .isString()
    .withMessage('Nationality must be a string')
    .trim(),
  
  // Height validation
  body('height')
    .if(body('height').exists())
    .isNumeric()
    .withMessage('Height must be a number')
    .custom(value => {
      if (value < 50 || value > 250) {
        throw new Error('Height must be between 50 and 250 cm');
      }
      return true;
    }),
  
  // Weight validation
  body('weight')
    .if(body('weight').exists())
    .isNumeric()
    .withMessage('Weight must be a number')
    .custom(value => {
      if (value < 30 || value > 200) {
        throw new Error('Weight must be between 30 and 200 kg');
      }
      return true;
    }),
  
  // Preferred foot validation
  body('preferredFoot')
    .if(body('preferredFoot').exists())
    .isIn(['right', 'left', 'both'])
    .withMessage('Invalid preferred foot'),
  
  // Jersey number validation
  body('jerseyNumber')
    .if(body('jerseyNumber').exists())
    .isInt({ min: 1, max: 99 })
    .withMessage('Jersey number must be between 1 and 99'),
  
  // Specialties validation
  body('specialties')
    .if(body('specialties').exists())
    .isArray()
    .withMessage('Specialties must be an array'),
  
  body('specialties.*')
    .if(body('specialties').exists())
    .isString()
    .withMessage('Each specialty must be a string')
    .trim(),
  
  // Attributes validation
  body('attributes')
    .if(body('attributes').exists())
    .isObject()
    .withMessage('Attributes must be an object'),
  
  body('attributes.*.speed')
    .if(body('attributes.speed').exists())
    .isInt({ min: 0, max: 100 })
    .withMessage('Speed must be between 0 and 100'),
  
  body('attributes.*.strength')
    .if(body('attributes.strength').exists())
    .isInt({ min: 0, max: 100 })
    .withMessage('Strength must be between 0 and 100'),
  
  body('attributes.*.intelligence')
    .if(body('attributes.intelligence').exists())
    .isInt({ min: 0, max: 100 })
    .withMessage('Intelligence must be between 0 and 100'),
  
  body('attributes.*.accuracy')
    .if(body('attributes.accuracy').exists())
    .isInt({ min: 0, max: 100 })
    .withMessage('Accuracy must be between 0 and 100'),
  
  // Check for validation errors
  (req, res, next) => {
    const errors = validationResult(req);
    
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    
    next();
  },
];
