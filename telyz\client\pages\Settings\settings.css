/* Base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: '<PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: #f0f2f5;
    color: #333;
}

.container {
    display: flex;
    min-height: 100vh;
    max-width: 1155px;
    margin: 0 auto;
    background-color: #f0f2f5;
}

/* Sidebar styles (copied exactly from template3.css) */
.sidebar-left {
    background-color: #f0f2f5;
    padding: 15px 0;
    position: sticky;
    top: 0;
    height: 100vh;
    width: 297px;
    overflow-y: auto;
}

.logo {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

.logo-link {
    text-decoration: none;
    cursor: pointer;
    transition: transform 0.2s ease;
    display: block;
}

.logo-link:hover {
    transform: scale(1.05);
}

.logo-container {
    width: 40px;
    height: 40px;
    background-color: #6a0dad;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(106, 13, 173, 0.3);
}

.sidebar-menu {
    padding: 0 15px;
    max-height: calc(100vh - 100px);
    width: 100%;
}

.menu-title {
    font-size: 12px;
    color: #666;
    margin: 20px 0 8px;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.sidebar-menu ul {
    list-style: none;
}

.sidebar-menu > ul > li {
    margin: 4px 0;
    padding: 8px 12px;
    border-radius: 10px;
    transition: all 0.3s;
}

.sidebar-menu li:hover {
    background-color: rgba(106, 13, 173, 0.05);
}

/* Submenu styles */
.has-submenu {
    position: relative;
}

.submenu {
    display: none;
    list-style: none;
    padding-left: 0;
    margin-top: 5px;
    margin-bottom: 5px;
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 8px;
    overflow: visible;
    max-height: 0;
    transition: max-height 0.3s ease-in-out;
    width: 100%;
    min-width: 260px;
    padding: 8px 0;
}

.has-submenu:hover .submenu {
    display: block;
    max-height: 400px;
}

.submenu li {
    margin: 0;
    padding: 8px 12px 8px 5px;
    min-width: 240px;
    position: relative;
}

.submenu li:hover {
    background-color: rgba(106, 13, 173, 0.1);
    transform: translateX(5px);
}

.sidebar-menu a {
    text-decoration: none;
    color: #4a0080;
    display: flex;
    align-items: center;
    font-size: 16px;
    padding: 6px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 500;
    transition: all 0.3s;
    position: relative;
}

.sidebar-menu a:hover {
    background-color: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
    transform: translateX(2px);
}

.sidebar-menu a.active {
    color: #6a0dad;
    font-weight: 600;
}

.sidebar-menu i {
    margin-right: 15px;
    font-size: 20px;
    width: 25px;
    text-align: center;
    color: #6a0dad;
}

/* AI Submenu specific styles */
.ai-submenu {
    overflow: visible !important;
    width: 260px;
    padding: 8px 0;
}

.ai-submenu li {
    overflow: visible;
    padding: 12px 15px 12px 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.03);
}

.ai-submenu li:last-child {
    border-bottom: none;
}

.ai-submenu a {
    font-size: 14px;
    padding: 4px 0;
    white-space: normal;
    overflow: visible;
    text-overflow: clip;
    display: flex;
    align-items: flex-start;
    width: 100%;
    line-height: 1.4;
}

.ai-submenu a span {
    display: inline-block;
    white-space: normal;
    overflow: visible;
    width: auto;
    padding-top: 2px;
    font-weight: 500;
}

.ai-submenu i {
    font-size: 18px;
    margin-right: 12px;
    margin-left: 0;
    width: 20px;
    text-align: center;
    display: inline-flex;
    justify-content: center;
    align-items: center;
}

/* AI submenu icon colors */
.ai-submenu li:nth-child(1) i { color: #FF5722; }
.ai-submenu li:nth-child(2) i { color: #4CAF50; }
.ai-submenu li:nth-child(3) i { color: #2196F3; }
.ai-submenu li:nth-child(4) i { color: #FFC107; }
.ai-submenu li:nth-child(5) i { color: #9C27B0; }

/* Sports submenu icon colors */
.sports-submenu li:nth-child(1) i { color: #8e44ad; }
.sports-submenu li:nth-child(2) i { color: #9b59b6; }
.sports-submenu li:nth-child(3) i { color: #6a0dad; }
.sports-submenu li:nth-child(4) i { color: #5d3fd3; }
.sports-submenu li:nth-child(5) i { color: #7d3c98; }
.sports-submenu li:nth-child(6) i { color: #a569bd; }

/* Badge styles */
.badge {
    display: inline-block;
    padding: 2px 6px;
    font-size: 10px;
    font-weight: bold;
    border-radius: 10px;
    margin-left: 5px;
}

.new-badge {
    background-color: #ff3366;
    color: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    animation: pulse 2s infinite;
    transition: opacity 0.5s ease;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* User profile sidebar */
.user-profile-sidebar {
    display: flex;
    align-items: center;
    margin-top: 23px;
    padding: 12px 18px;
    border-radius: 10px;
    background-color: rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
}

.user-profile-sidebar:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.profile-pic-small {
    width: 46px;
    height: 46px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 12px;
    border: 2px solid #6a0dad;
}

.profile-pic-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-info-sidebar {
    flex: 1;
}

.profile-info-sidebar span:first-child {
    display: block;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.teams-clubs {
    font-size: 12px;
    color: #666;
    margin-top: 2px;
}

.profile-dropdown-toggle {
    margin-left: auto;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    border-radius: 50%;
    transition: all 0.2s;
    background-color: rgba(0, 0, 0, 0.05);
    cursor: pointer;
}

.profile-dropdown-toggle:hover {
    background-color: rgba(0, 0, 0, 0.1);
    color: #0066cc;
    transform: scale(1.1);
}

.profile-dropdown-toggle i {
    font-size: 20px;
    color: #444;
}

/* Profile dropdown menu */
.profile-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.2);
    width: 220px;
    z-index: 1000;
    overflow: hidden;
    display: none;
    border: 1px solid #eee;
    margin-top: 6px;
    max-height: 0;
    opacity: 0;
    transition: max-height 0.3s ease, opacity 0.3s ease;
    visibility: hidden;
    text-align: left;
}

.profile-dropdown-menu.show {
    display: block;
    max-height: 400px;
    opacity: 1;
    overflow-y: auto;
    z-index: 1001;
    visibility: visible;
}

.profile-dropdown-item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 14px 25px;
    text-decoration: none;
    color: #333;
    transition: all 0.2s ease;
    font-size: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.03);
    position: relative;
}

.profile-dropdown-item:last-child {
    border-bottom: none;
}

.profile-dropdown-item:hover {
    background-color: #f5f5f5;
    color: #6a0dad;
}

.profile-dropdown-item i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
    color: #6a0dad;
    font-size: 17px;
    display: inline-block;
}

.dropdown-divider {
    height: 1px;
    background-color: #e0e0e0;
    margin: 8px 0;
}

.logout-item {
    color: #e74c3c;
}

.logout-item:hover {
    background-color: rgba(231, 76, 60, 0.05);
    color: #e74c3c;
}

.logout-item i {
    color: #e74c3c;
}

/* Main content area */
.main-content {
    flex: 1;
    padding: 0;
    background-color: #f0f2f5;
    overflow-y: auto;
    max-height: 100vh;
}

/* Settings Header */
.settings-header {
    background: linear-gradient(135deg, #6a0dad 0%, #8e44ad 100%);
    color: white;
    padding: 30px;
    margin-bottom: 0;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-title h1 {
    font-size: 28px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-title p {
    opacity: 0.9;
    font-size: 16px;
}

.header-actions {
    display: flex;
    gap: 12px;
}

.btn-primary, .btn-secondary {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    backdrop-filter: blur(10px);
}

.btn-primary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}

/* Settings Navigation */
.settings-navigation {
    background: white;
    padding: 0 30px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-tabs {
    display: flex;
    gap: 0;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.nav-tabs::-webkit-scrollbar {
    display: none;
}

.nav-tab {
    background: none;
    border: none;
    padding: 16px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    border-bottom: 3px solid transparent;
    white-space: nowrap;
}

.nav-tab:hover {
    background: rgba(106, 13, 173, 0.05);
    color: #6a0dad;
}

.nav-tab.active {
    color: #6a0dad;
    border-bottom-color: #6a0dad;
    background: rgba(106, 13, 173, 0.05);
}

.nav-tab i {
    font-size: 16px;
}

/* Settings Content */
.settings-content {
    padding: 30px;
}

.settings-section {
    display: none;
}

.settings-section.active {
    display: block;
}

.section-header {
    margin-bottom: 30px;
}

.section-header h2 {
    font-size: 24px;
    color: #333;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.section-header p {
    color: #666;
    font-size: 16px;
}

/* Settings Grid */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.setting-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.setting-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.setting-card.full-width {
    grid-column: 1 / -1;
}

.setting-card.danger-zone {
    border: 2px solid #fee;
    background: #fffbfb;
}

/* Profile Picture Card - Same as page background */
.profile-picture-card {
    background: #f0f2f5 !important;
    border: 2px solid #e1e5e9;
}

.profile-picture-card:hover {
    background: #f0f2f5 !important;
    border-color: #6a0dad;
}

.card-header {
    margin-bottom: 20px;
}

.card-header h3 {
    font-size: 18px;
    color: #333;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.card-header p {
    color: #666;
    font-size: 14px;
}

.card-content {
    /* Content styles */
}

/* Form Elements */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.form-group label {
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

.form-input, .form-select, .form-textarea {
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s;
    font-family: inherit;
}

.form-input:focus, .form-select:focus, .form-textarea:focus {
    outline: none;
    border-color: #6a0dad;
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

.char-counter {
    text-align: right;
    font-size: 12px;
    color: #666;
    margin-top: 4px;
}

/* Profile Picture Section */
.profile-picture-section {
    display: flex;
    align-items: center;
    gap: 24px;
}

.current-picture {
    position: relative;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid #e1e5e9;
    transition: all 0.3s ease;
}

.current-picture:hover {
    border-color: #6a0dad;
}

.current-picture img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.picture-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(106, 13, 173, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s;
    cursor: pointer;
}

.current-picture:hover .picture-overlay {
    opacity: 1;
}

.picture-overlay i {
    color: white;
    font-size: 24px;
}

.picture-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.btn-outline {
    background: transparent;
    border: 2px solid #6a0dad;
    color: #6a0dad;
    padding: 10px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    justify-content: center;
}

.btn-outline:hover {
    background: #6a0dad;
    color: white;
}

.btn-outline.btn-danger {
    border-color: #dc3545;
    color: #dc3545;
}

.btn-outline.btn-danger:hover {
    background: #dc3545;
    color: white;
}

.btn-outline.full-width {
    width: 100%;
    justify-content: flex-start;
}

/* Toggle Switches */
.toggle-group {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.toggle-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
}

.toggle-info {
    flex: 1;
}

.toggle-info label {
    font-weight: 500;
    color: #333;
    font-size: 14px;
    margin-bottom: 4px;
    display: block;
}

.toggle-info span {
    color: #666;
    font-size: 13px;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 48px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-label {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #ccc;
    transition: 0.3s;
    border-radius: 24px;
}

.toggle-label:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background: white;
    transition: 0.3s;
    border-radius: 50%;
}

.toggle-switch input:checked + .toggle-label {
    background: #6a0dad;
}

.toggle-switch input:checked + .toggle-label:before {
    transform: translateX(24px);
}

/* Security Actions */
.security-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.button-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 2px;
}

.button-content span {
    font-weight: 500;
}

.button-content small {
    font-size: 12px;
    color: #666;
}

/* Subscription Info */
.subscription-info {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.current-plan {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px;
    background: linear-gradient(135deg, #6a0dad 0%, #8e44ad 100%);
    border-radius: 12px;
    color: white;
}

.plan-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.plan-details h4 {
    font-size: 18px;
    margin-bottom: 4px;
}

.plan-details p {
    opacity: 0.9;
    font-size: 14px;
}

.plan-features {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #28a745;
    font-size: 14px;
}

.feature-item i {
    font-size: 16px;
}

.plan-actions {
    display: flex;
    gap: 12px;
}

/* Account Stats */
.account-stats {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
}

.stat-icon {
    width: 48px;
    height: 48px;
    background: #6a0dad;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

.stat-info {
    flex: 1;
}

.stat-info label {
    font-weight: 500;
    color: #333;
    display: block;
    margin-bottom: 4px;
}

.stat-info span {
    color: #666;
    font-size: 14px;
}

/* Connected Accounts */
.connected-accounts {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.account-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.account-item:hover {
    background: #e9ecef;
}

.account-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.account-icon.google { background: #db4437; }
.account-icon.facebook { background: #3b5998; }
.account-icon.instagram { background: #e4405f; }
.account-icon.linkedin { background: #0077b5; }

.account-info {
    flex: 1;
}

.account-info label {
    font-weight: 500;
    color: #333;
    display: block;
    margin-bottom: 4px;
}

.account-info span {
    color: #666;
    font-size: 14px;
}

.btn-connect, .btn-disconnect {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-connect {
    background: #28a745;
    color: white;
}

.btn-connect:hover {
    background: #218838;
}

.btn-disconnect {
    background: #dc3545;
    color: white;
}

.btn-disconnect:hover {
    background: #c82333;
}

/* Theme Options */
.theme-options {
    display: flex;
    gap: 16px;
    margin-top: 12px;
}

.theme-option {
    position: relative;
}

.theme-option input {
    display: none;
}

.theme-option label {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.theme-option:hover label {
    background: #f8f9fa;
}

.theme-option input:checked + label {
    background: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
}

.theme-preview {
    width: 60px;
    height: 40px;
    border-radius: 6px;
    overflow: hidden;
    border: 2px solid #e1e5e9;
    transition: border-color 0.3s;
}

.theme-option input:checked + label .theme-preview {
    border-color: #6a0dad;
}

.theme-preview.light {
    background: white;
}

.theme-preview.dark {
    background: #2c3e50;
}

.theme-preview.auto {
    background: linear-gradient(90deg, white 50%, #2c3e50 50%);
}

.preview-header {
    height: 8px;
    background: #6a0dad;
}

.preview-content {
    display: flex;
    height: 24px;
}

.preview-sidebar {
    width: 20px;
    background: rgba(0, 0, 0, 0.1);
}

.preview-main {
    flex: 1;
    background: rgba(0, 0, 0, 0.05);
}

.theme-preview.dark .preview-sidebar {
    background: rgba(255, 255, 255, 0.2);
}

.theme-preview.dark .preview-main {
    background: rgba(255, 255, 255, 0.1);
}

/* Schedule Settings */
.schedule-settings {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.time-range {
    display: flex;
    align-items: center;
    gap: 12px;
}

.time-range span {
    color: #666;
    font-size: 14px;
}

.time-range input {
    flex: 1;
}

/* Storage Usage */
.storage-overview {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.storage-stat {
    display: flex;
    align-items: center;
    gap: 24px;
}

.storage-circle {
    width: 100px;
    height: 100px;
}

.circular-chart {
    display: block;
    margin: 0 auto;
    max-width: 100%;
    max-height: 100%;
}

.circle-bg {
    fill: none;
    stroke: #f0f0f0;
    stroke-width: 3.8;
}

.circle {
    fill: none;
    stroke: #6a0dad;
    stroke-width: 3.8;
    stroke-linecap: round;
    animation: progress 1s ease-out forwards;
}

@keyframes progress {
    0% {
        stroke-dasharray: 0 100;
    }
}

.percentage {
    fill: #6a0dad;
    font-size: 0.5em;
    text-anchor: middle;
    font-weight: bold;
}

.storage-info h4 {
    font-size: 20px;
    color: #333;
    margin-bottom: 4px;
}

.storage-info p {
    color: #666;
    font-size: 14px;
}

.storage-breakdown {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.breakdown-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #666;
}

.breakdown-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
}

.breakdown-color.videos { background: #6a0dad; }
.breakdown-color.images { background: #28a745; }
.breakdown-color.documents { background: #ffc107; }

/* Export Options */
.export-options {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.export-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
}

.export-icon {
    width: 48px;
    height: 48px;
    background: #6a0dad;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

.export-info {
    flex: 1;
}

.export-info h4 {
    font-size: 16px;
    color: #333;
    margin-bottom: 4px;
}

.export-info p {
    color: #666;
    font-size: 14px;
}

/* Danger Zone */
.danger-actions {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.danger-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: #fff5f5;
    border-radius: 8px;
    border: 1px solid #fed7d7;
}

.danger-info h4 {
    font-size: 16px;
    color: #333;
    margin-bottom: 4px;
}

.danger-info p {
    color: #666;
    font-size: 14px;
}

.btn-danger {
    background: #dc3545;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-danger:hover {
    background: #c82333;
    transform: translateY(-1px);
}

/* Floating Buttons */
.floating-buttons {
    position: fixed;
    bottom: 30px;
    right: 30px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    z-index: 1000;
}

.fab {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    border: none;
    background: #6a0dad;
    color: white;
    font-size: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(106, 13, 173, 0.3);
}

.fab:hover {
    background: #5a0c9a;
    transform: translateY(-3px);
    box-shadow: 0 6px 16px rgba(106, 13, 173, 0.4);
}

.top-btn {
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.top-btn.visible {
    opacity: 1;
    visibility: visible;
}

/* Tooltips */
.js-tooltip {
    position: fixed;
    width: 220px;
    background-color: rgba(106, 13, 173, 0.9);
    color: white;
    padding: 10px;
    border-radius: 6px;
    font-size: 12px;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
    z-index: 1001;
    line-height: 1.4;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    pointer-events: none;
    text-align: left;
}

.js-tooltip::before {
    content: "";
    position: absolute;
    top: 50%;
    left: -10px;
    transform: translateY(-50%);
    border-width: 5px;
    border-style: solid;
    border-color: transparent var(--tooltip-color, rgba(106, 13, 173, 0.9)) transparent transparent;
}

.js-tooltip.visible {
    opacity: 1;
    visibility: visible;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .settings-grid {
        grid-template-columns: 1fr;
    }
    
    .profile-picture-section {
        flex-direction: column;
        text-align: center;
    }
    
    .storage-stat {
        flex-direction: column;
        text-align: center;
    }
}

@media (max-width: 768px) {
    .container {
        flex-direction: column;
        max-width: 100%;
        margin: 0;
    }
    
    .sidebar-left {
        width: 100%;
        height: auto;
        position: static;
    }
    
    .main-content {
        padding: 0;
    }
    
    .settings-header {
        padding: 20px;
    }
    
    .header-content {
        flex-direction: column;
        gap: 20px;
        align-items: flex-start;
    }
    
    .header-actions {
        width: 100%;
        justify-content: flex-end;
    }
    
    .settings-navigation {
        padding: 0 20px;
    }
    
    .settings-content {
        padding: 20px;
    }
    
    .nav-tabs {
        justify-content: flex-start;
    }
    
    .nav-tab {
        padding: 12px 16px;
        font-size: 13px;
    }
    
    .nav-tab span {
        display: none;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .theme-options {
        justify-content: center;
    }
    
    .plan-actions {
        flex-direction: column;
    }
    
    .floating-buttons {
        bottom: 20px;
        right: 20px;
    }
    
    .fab {
        width: 48px;
        height: 48px;
        font-size: 18px;
    }
}

@media (max-width: 480px) {
    .settings-header {
        padding: 15px;
    }
    
    .header-title h1 {
        font-size: 24px;
    }
    
    .header-title p {
        font-size: 14px;
    }
    
    .settings-content {
        padding: 15px;
    }
    
    .setting-card {
        padding: 20px;
    }
    
    .nav-tab {
        padding: 10px 12px;
    }
    
    .current-plan {
        flex-direction: column;
        text-align: center;
    }
    
    .account-item {
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }
    
    .danger-item {
        flex-direction: column;
        gap: 12px;
        text-align: center;
    }
}