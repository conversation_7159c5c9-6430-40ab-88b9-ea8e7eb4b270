# Dependencias
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log

# Archivos de entorno
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Archivos de construcción
/build
/dist

# Archivos de caché
.cache/
.npm/
.eslintcache

# Archivos de cobertura
coverage/

# Archivos de registro
logs/
*.log

# Archivos de sistema operativo
.DS_Store
Thumbs.db

# Archivos de IDE
.idea/
.vscode/
*.sublime-project
*.sublime-workspace

# Archivos temporales
*.swp
*.swo
*~

# Archivos de base de datos
*.sqlite
*.db

# Archivos de configuración local
config.local.js

# Archivos de carga y almacenamiento
uploads/
!uploads/.gitkeep
storage/
!storage/.gitkeep

# Archivos de AI
ai-analysis/models/weights/
ai-analysis/data/raw/
ai-analysis/temp/

# Archivos de prueba
test/temp/
