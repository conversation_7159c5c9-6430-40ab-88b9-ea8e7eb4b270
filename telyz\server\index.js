/**
 * Telyz Server
 * 
 * Main entry point for the Telyz server application.
 */

const express = require('express');
const cors = require('cors');
const path = require('path');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Initialize Express app
const app = express();

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Serve static files from the client directory
app.use(express.static(path.join(__dirname, '../client')));

// API routes will be added here
// app.use('/api/users', require('./api/routes/users'));
// app.use('/api/auth', require('./api/routes/auth'));
// app.use('/api/profiles', require('./api/routes/profiles'));
// app.use('/api/opportunities', require('./api/routes/opportunities'));
// app.use('/api/ai-analysis', require('./api/routes/ai-analysis'));

// Serve the main HTML file for any other request
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, '../client/pages/Home/index.html'));
});

// Set port
const PORT = process.env.PORT || 5000;

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});

module.exports = app;
