/**
 * Authentication Validator
 * 
 * Validation middleware for authentication-related requests.
 */

const { body, validationResult } = require('express-validator');

/**
 * Validate login
 */
exports.validateLogin = [
  // Email validation
  body('email')
    .notEmpty()
    .withMessage('Email is required')
    .isEmail()
    .withMessage('Please provide a valid email address')
    .normalizeEmail(),
  
  // Password validation
  body('password')
    .notEmpty()
    .withMessage('Password is required'),
  
  // Check for validation errors
  (req, res, next) => {
    const errors = validationResult(req);
    
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    
    next();
  },
];

/**
 * Validate refresh token
 */
exports.validateRefreshToken = [
  // Refresh token validation
  body('refreshToken')
    .notEmpty()
    .withMessage('Refresh token is required'),
  
  // Check for validation errors
  (req, res, next) => {
    const errors = validationResult(req);
    
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    
    next();
  },
];

/**
 * Validate Google login
 */
exports.validateGoogleLogin = [
  // ID token validation
  body('idToken')
    .notEmpty()
    .withMessage('Google ID token is required'),
  
  // Check for validation errors
  (req, res, next) => {
    const errors = validationResult(req);
    
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    
    next();
  },
];

/**
 * Validate Facebook login
 */
exports.validateFacebookLogin = [
  // Access token validation
  body('accessToken')
    .notEmpty()
    .withMessage('Facebook access token is required'),
  
  // Check for validation errors
  (req, res, next) => {
    const errors = validationResult(req);
    
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    
    next();
  },
];

/**
 * Validate Apple login
 */
exports.validateAppleLogin = [
  // Identity token validation
  body('identityToken')
    .notEmpty()
    .withMessage('Apple identity token is required'),
  
  // Check for validation errors
  (req, res, next) => {
    const errors = validationResult(req);
    
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    
    next();
  },
];
