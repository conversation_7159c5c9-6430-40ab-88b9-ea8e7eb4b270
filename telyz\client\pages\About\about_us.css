/* Base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: '<PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: #f0f2f5;
    color: #333;
}

.container {
    display: flex;
    min-height: 100vh;
    max-width: 1155px;
    margin: 0 auto;
    background-color: #f0f2f5;
}

/* Sidebar styles (copied exactly from settings.css) */
.sidebar-left {
    background-color: #f0f2f5;
    padding: 15px 0;
    position: sticky;
    top: 0;
    height: 100vh;
    width: 297px;
    overflow-y: auto;
}

.logo {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

.logo-link {
    text-decoration: none;
    cursor: pointer;
    transition: transform 0.2s ease;
    display: block;
}

.logo-link:hover {
    transform: scale(1.05);
}

.logo-container {
    width: 40px;
    height: 40px;
    background-color: #6a0dad;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(106, 13, 173, 0.3);
}

.sidebar-menu {
    padding: 0 15px;
    max-height: calc(100vh - 100px);
    width: 100%;
}

.menu-title {
    font-size: 12px;
    color: #666;
    margin: 20px 0 8px;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.sidebar-menu ul {
    list-style: none;
}

.sidebar-menu > ul > li {
    margin: 4px 0;
    padding: 8px 12px;
    border-radius: 10px;
    transition: all 0.3s;
}

.sidebar-menu li:hover {
    background-color: rgba(106, 13, 173, 0.05);
}

/* Submenu styles */
.has-submenu {
    position: relative;
}

.submenu {
    display: none;
    list-style: none;
    padding-left: 0;
    margin-top: 5px;
    margin-bottom: 5px;
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 8px;
    overflow: visible;
    max-height: 0;
    transition: max-height 0.3s ease-in-out;
    width: 100%;
    min-width: 260px;
    padding: 8px 0;
}

.has-submenu:hover .submenu {
    display: block;
    max-height: 400px;
}

.submenu li {
    margin: 0;
    padding: 8px 12px 8px 5px;
    min-width: 240px;
    position: relative;
}

.submenu li:hover {
    background-color: rgba(106, 13, 173, 0.1);
    transform: translateX(5px);
}

.submenu a {
    font-size: 14px;
    padding: 4px 0;
    white-space: normal;
    overflow: visible;
    text-overflow: clip;
    display: flex;
    align-items: flex-start;
    width: 100%;
    line-height: 1.4;
}

.ai-submenu {
    overflow: visible !important;
    width: 260px;
    padding: 8px 0;
}

.ai-submenu li {
    overflow: visible;
    padding: 12px 15px 12px 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.03);
}

.ai-submenu li:last-child {
    border-bottom: none;
}

.ai-submenu a {
    font-size: 14px;
    padding: 4px 0;
    white-space: normal;
    overflow: visible;
    text-overflow: clip;
    display: flex;
    align-items: flex-start;
    width: 100%;
    line-height: 1.4;
}

.ai-submenu a span {
    display: inline-block;
    white-space: normal;
    overflow: visible;
    width: auto;
    padding-top: 2px;
    font-weight: 500;
}

.sports-submenu {
    overflow: visible !important;
    width: 260px;
    padding: 8px 0;
}

.sports-submenu li {
    overflow: visible;
    padding: 12px 15px 12px 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.03);
}

.sports-submenu li:last-child {
    border-bottom: none;
}

.sports-submenu a {
    font-size: 14px;
    padding: 4px 0;
    white-space: normal;
    overflow: visible;
    text-overflow: clip;
    display: flex;
    align-items: flex-start;
    width: 100%;
    line-height: 1.4;
}

.sports-submenu a span {
    display: inline-block;
    white-space: normal;
    overflow: visible;
    width: auto;
    padding-top: 2px;
    font-weight: 500;
}

.sidebar-menu a {
    text-decoration: none;
    color: #4a0080;
    display: flex;
    align-items: center;
    font-size: 16px;
    padding: 6px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 500;
    transition: all 0.3s;
    position: relative;
}

.sidebar-menu a:hover {
    background-color: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
    transform: translateX(2px);
}

.sidebar-menu a.active {
    color: #6a0dad;
    font-weight: 600;
}

.sidebar-menu i {
    margin-right: 15px;
    font-size: 20px;
    width: 25px;
    text-align: center;
    color: #6a0dad;
}

.badge {
    background-color: #ff4757;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 8px;
    font-weight: 600;
}

.new-badge {
    background: linear-gradient(45deg, #ff6b6b, #ff4757);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* User profile sidebar */
.user-profile-sidebar {
    display: flex;
    align-items: center;
    margin-top: 23px;
    padding: 12px 18px;
    border-radius: 10px;
    background-color: rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
}

.user-profile-sidebar:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.profile-pic-small {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 10px;
    border: 2px solid #6a0dad;
}

.profile-pic-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-info-sidebar {
    display: flex;
    flex-direction: column;
}

.profile-info-sidebar span {
    font-size: 14px;
    font-weight: 600;
}

.teams-clubs {
    font-size: 12px !important;
    color: #666;
    font-weight: normal !important;
}

.profile-dropdown-toggle {
    margin-left: auto;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    border-radius: 50%;
    transition: all 0.2s;
    background-color: rgba(0, 0, 0, 0.05);
    cursor: pointer;
}

.profile-dropdown-toggle:hover {
    background-color: rgba(0, 0, 0, 0.1);
    color: #0066cc;
    transform: scale(1.1);
}

.profile-dropdown-toggle i {
    font-size: 16px;
    color: #444;
}

.profile-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.2);
    width: 200px;
    z-index: 1000;
    overflow: hidden;
    display: none;
    border: 1px solid #eee;
    margin-top: 6px;
    max-height: 0;
    opacity: 0;
    transition: max-height 0.3s ease, opacity 0.3s ease;
    visibility: hidden;
}

.profile-dropdown-menu.show {
    display: block;
    max-height: 400px;
    opacity: 1;
    overflow-y: auto;
    z-index: 1001;
    visibility: visible;
}

.profile-dropdown-item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 12px 16px;
    text-decoration: none;
    color: #333;
    transition: all 0.2s ease;
    font-size: 14px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.03);
}

.profile-dropdown-item:last-child {
    border-bottom: none;
}

.profile-dropdown-item:hover {
    background-color: #f5f5f5;
    color: #6a0dad;
}

.profile-dropdown-item i {
    margin-right: 8px;
    width: 16px;
    text-align: center;
    color: #6a0dad;
    font-size: 14px;
}

.dropdown-divider {
    height: 1px;
    background-color: #e0e0e0;
    margin: 8px 0;
    opacity: 0.7;
}

.logout-item {
    color: #e74c3c;
}

.logout-item i {
    color: #e74c3c;
}

/* Main content */
.main-content {
    flex: 1;
    padding: 20px;
    background-color: #f0f2f5;
    border-radius: 15px;
    margin: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
    max-height: calc(100vh - 40px);
}

/* ================ ABOUT PAGE STYLES ================ */
.about-container {
    max-width: 800px;
    margin: 0 auto;
    background-color: #f0f2f5;
}

.about-header {
    text-align: center;
    margin-bottom: 40px;
    padding-bottom: 20px;
    border-bottom: 2px solid #f0f2f5;
    background-color: #f0f2f5;
}

.about-header h1 {
    color: #6a0dad;
    font-size: 32px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.about-header h1 i {
    color: #6a0dad;
}

.subtitle {
    color: #666;
    font-size: 18px;
    margin: 0;
    font-weight: 400;
}

/* ================ ABOUT MAIN CONTENT ================ */
.about-main {
    line-height: 1.7;
    background-color: #f0f2f5;
}

.about-main section {
    margin-bottom: 40px;
    background-color: #f0f2f5;
}

.about-main h2 {
    color: #2c3e50;
    font-size: 24px;
    margin-bottom: 20px;
    border-bottom: 3px solid #6a0dad;
    padding-bottom: 10px;
    display: inline-block;
}

.about-main p {
    color: #555;
    font-size: 16px;
    margin-bottom: 15px;
}

/* Company Overview */
.company-overview {
    background-color: #f0f2f5;
}

.company-overview p {
    text-align: justify;
}

/* Mission Section */
.our-mission {
    background-color: #f0f2f5;
}

/* Services Grid */
.what-we-do {
    background-color: #f0f2f5;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    margin-top: 25px;
}

.service-item {
    background: white;
    border-radius: 10px;
    padding: 25px;
    text-align: center;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.service-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.service-item i {
    font-size: 48px;
    color: #6a0dad;
    margin-bottom: 20px;
}

.service-item h3 {
    color: #2c3e50;
    font-size: 18px;
    margin-bottom: 10px;
}

.service-item p {
    color: #666;
    font-size: 14px;
    margin: 0;
}

/* Team Grid */
.leadership-team {
    background-color: #f0f2f5;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    margin-top: 25px;
}

.team-member {
    background: white;
    border-radius: 10px;
    padding: 25px;
    text-align: center;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.team-member:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.team-member img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin-bottom: 15px;
    border: 3px solid #6a0dad;
}

.team-member h3 {
    color: #2c3e50;
    font-size: 18px;
    margin-bottom: 5px;
}

.team-member .role {
    color: #6a0dad;
    font-weight: 600;
    margin-bottom: 10px;
}

.team-member .bio {
    color: #666;
    font-size: 14px;
    margin: 0;
}

/* Values List */
.our-values {
    background-color: #f0f2f5;
}

.values-list {
    margin-top: 25px;
}

.value-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    margin-bottom: 25px;
    padding: 20px;
    background: white;
    border-radius: 10px;
    border: 1px solid #e9ecef;
}

.value-item i {
    font-size: 28px;
    color: #6a0dad;
    margin-top: 5px;
}

.value-item h3 {
    color: #2c3e50;
    font-size: 18px;
    margin-bottom: 8px;
}

.value-item p {
    color: #666;
    margin: 0;
    font-size: 14px;
}

/* Contact Info */
.contact-us {
    background-color: #f0f2f5;
}

.contact-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 25px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background: white;
    border-radius: 10px;
    border: 1px solid #e9ecef;
}

.contact-item i {
    font-size: 24px;
    color: #6a0dad;
}

.contact-item strong {
    color: #2c3e50;
    font-size: 16px;
    display: block;
    margin-bottom: 5px;
}

.contact-item p {
    color: #666;
    margin: 0;
    font-size: 14px;
}

/* ================ RESPONSIVE DESIGN ================ */
@media (max-width: 768px) {
    .container {
        flex-direction: column;
        max-width: 100%;
    }
    
    .sidebar-left {
        width: 100%;
        height: auto;
        position: relative;
    }
    
    .main-content {
        margin: 10px;
        padding: 15px;
    }
    
    .about-container {
        max-width: 100%;
    }
    
    .about-header h1 {
        font-size: 24px;
        flex-direction: column;
        gap: 8px;
    }
    
    .subtitle {
        font-size: 16px;
    }
    
    .about-main h2 {
        font-size: 20px;
    }
    
    .services-grid,
    .team-grid {
        grid-template-columns: 1fr;
    }
    
    .contact-info {
        grid-template-columns: 1fr;
    }
}