/**
 * Storage Configuration
 * 
 * Configuration and setup for file storage.
 */

const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const config = require('./index');

// Create storage directory if it doesn't exist
if (config.storage.type === 'local') {
  const storageDir = config.storage.local.directory;
  if (!fs.existsSync(storageDir)) {
    fs.mkdirSync(storageDir, { recursive: true });
  }
}

/**
 * Local storage implementation
 */
const localStorage = {
  /**
   * Save a file to local storage
   * 
   * @param {Buffer|string} fileData - File data or path
   * @param {string} fileName - Original file name
   * @param {string} folder - Storage folder
   * @returns {Promise<string>} File URL
   */
  saveFile: async (fileData, fileName, folder = '') => {
    try {
      // Create folder if it doesn't exist
      const folderPath = path.join(config.storage.local.directory, folder);
      if (!fs.existsSync(folderPath)) {
        fs.mkdirSync(folderPath, { recursive: true });
      }
      
      // Generate unique file name
      const fileExtension = path.extname(fileName);
      const uniqueFileName = `${uuidv4()}${fileExtension}`;
      const filePath = path.join(folderPath, uniqueFileName);
      
      // Save file
      if (typeof fileData === 'string' && fs.existsSync(fileData)) {
        // Copy file from temporary location
        fs.copyFileSync(fileData, filePath);
      } else {
        // Write buffer to file
        fs.writeFileSync(filePath, fileData);
      }
      
      // Return file URL
      const fileUrl = `/${path.join(folder, uniqueFileName)}`;
      return fileUrl;
    } catch (error) {
      console.error(`Error saving file to local storage: ${error.message}`);
      throw error;
    }
  },
  
  /**
   * Get a file from local storage
   * 
   * @param {string} fileUrl - File URL
   * @returns {Promise<Buffer>} File data
   */
  getFile: async (fileUrl) => {
    try {
      // Remove leading slash
      const relativePath = fileUrl.startsWith('/') ? fileUrl.substring(1) : fileUrl;
      const filePath = path.join(config.storage.local.directory, relativePath);
      
      // Check if file exists
      if (!fs.existsSync(filePath)) {
        throw new Error('File not found');
      }
      
      // Read file
      const fileData = fs.readFileSync(filePath);
      return fileData;
    } catch (error) {
      console.error(`Error getting file from local storage: ${error.message}`);
      throw error;
    }
  },
  
  /**
   * Delete a file from local storage
   * 
   * @param {string} fileUrl - File URL
   * @returns {Promise<boolean>} Whether file was deleted
   */
  deleteFile: async (fileUrl) => {
    try {
      // Remove leading slash
      const relativePath = fileUrl.startsWith('/') ? fileUrl.substring(1) : fileUrl;
      const filePath = path.join(config.storage.local.directory, relativePath);
      
      // Check if file exists
      if (!fs.existsSync(filePath)) {
        return false;
      }
      
      // Delete file
      fs.unlinkSync(filePath);
      return true;
    } catch (error) {
      console.error(`Error deleting file from local storage: ${error.message}`);
      throw error;
    }
  },
  
  /**
   * Get a file URL
   * 
   * @param {string} fileUrl - File URL
   * @returns {string} Full file URL
   */
  getFileUrl: (fileUrl) => {
    // Remove leading slash
    const relativePath = fileUrl.startsWith('/') ? fileUrl.substring(1) : fileUrl;
    return `${config.server.host}/storage/${relativePath}`;
  },
};

/**
 * S3 storage implementation
 */
const s3Storage = {
  // Placeholder for S3 storage implementation
  saveFile: async () => {
    throw new Error('S3 storage not implemented');
  },
  getFile: async () => {
    throw new Error('S3 storage not implemented');
  },
  deleteFile: async () => {
    throw new Error('S3 storage not implemented');
  },
  getFileUrl: () => {
    throw new Error('S3 storage not implemented');
  },
};

/**
 * Google Cloud Storage implementation
 */
const gcsStorage = {
  // Placeholder for GCS storage implementation
  saveFile: async () => {
    throw new Error('GCS storage not implemented');
  },
  getFile: async () => {
    throw new Error('GCS storage not implemented');
  },
  deleteFile: async () => {
    throw new Error('GCS storage not implemented');
  },
  getFileUrl: () => {
    throw new Error('GCS storage not implemented');
  },
};

/**
 * Get storage implementation based on configuration
 * 
 * @returns {Object} Storage implementation
 */
const getStorageImplementation = () => {
  switch (config.storage.type) {
    case 'local':
      return localStorage;
    case 's3':
      return s3Storage;
    case 'gcs':
      return gcsStorage;
    default:
      return localStorage;
  }
};

// Export storage implementation
module.exports = getStorageImplementation();
