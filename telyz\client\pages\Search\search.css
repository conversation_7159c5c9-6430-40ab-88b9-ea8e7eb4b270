/* Base styles from template3.css */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: '<PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: #f0f2f5;
    color: #333;
}

.container {
    display: flex;
    min-height: 100vh;
    max-width: 1155px;
    margin: 0 auto;
    background-color: #f0f2f5;
}

/* Sidebar styles (copied exactly from template3.css) */
.sidebar-left {
    background-color: #f0f2f5;
    padding: 15px 0;
    position: sticky;
    top: 0;
    height: 100vh;
    width: 297px;
    overflow-y: auto;
}

.logo {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

.logo-link {
    text-decoration: none;
    cursor: pointer;
    transition: transform 0.2s ease;
    display: block;
}

.logo-link:hover {
    transform: scale(1.05);
}

.logo-container {
    width: 40px;
    height: 40px;
    background-color: #6a0dad;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(106, 13, 173, 0.3);
}

.sidebar-menu {
    padding: 0 15px;
    max-height: calc(100vh - 100px);
    width: 100%;
}

.menu-title {
    font-size: 12px;
    color: #666;
    margin: 20px 0 8px;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.sidebar-menu ul {
    list-style: none;
}

.sidebar-menu > ul > li {
    margin: 4px 0;
    padding: 8px 12px;
    border-radius: 10px;
    transition: all 0.3s;
}

.sidebar-menu li:hover {
    background-color: rgba(106, 13, 173, 0.05);
}

/* Submenu styles */
.has-submenu {
    position: relative;
}

.submenu {
    display: none;
    list-style: none;
    padding-left: 0;
    margin-top: 5px;
    margin-bottom: 5px;
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 8px;
    overflow: visible;
    max-height: 0;
    transition: max-height 0.3s ease-in-out;
    width: 100%;
    min-width: 260px;
    padding: 8px 0;
}

.has-submenu:hover .submenu {
    display: block;
    max-height: 400px;
}

.submenu li {
    margin: 0;
    padding: 8px 12px 8px 5px;
    min-width: 240px;
    position: relative;
}

.submenu li:hover {
    background-color: rgba(106, 13, 173, 0.1);
    transform: translateX(5px);
}

.sidebar-menu a {
    text-decoration: none;
    color: #4a0080;
    display: flex;
    align-items: center;
    font-size: 16px;
    padding: 6px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 500;
    transition: all 0.3s;
    position: relative;
}

.sidebar-menu a:hover {
    background-color: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
    transform: translateX(2px);
}

.sidebar-menu a.active {
    color: #6a0dad;
    font-weight: 600;
}

.sidebar-menu i {
    margin-right: 15px;
    font-size: 20px;
    width: 25px;
    text-align: center;
    color: #6a0dad;
}

/* AI Submenu specific styles */
.ai-submenu {
    overflow: visible !important;
    width: 260px;
    padding: 8px 0;
}

.ai-submenu li {
    overflow: visible;
    padding: 12px 15px 12px 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.03);
}

.ai-submenu li:last-child {
    border-bottom: none;
}

.ai-submenu a {
    font-size: 14px;
    padding: 4px 0;
    white-space: normal;
    overflow: visible;
    text-overflow: clip;
    display: flex;
    align-items: flex-start;
    width: 100%;
    line-height: 1.4;
}

.ai-submenu a span {
    display: inline-block;
    white-space: normal;
    overflow: visible;
    width: auto;
    padding-top: 2px;
    font-weight: 500;
}

.ai-submenu i {
    font-size: 18px;
    margin-right: 12px;
    margin-left: 0;
    width: 20px;
    text-align: center;
    display: inline-flex;
    justify-content: center;
    align-items: center;
}

/* AI submenu icon colors */
.ai-submenu li:nth-child(1) i { color: #FF5722; }
.ai-submenu li:nth-child(2) i { color: #4CAF50; }
.ai-submenu li:nth-child(3) i { color: #2196F3; }
.ai-submenu li:nth-child(4) i { color: #FFC107; }
.ai-submenu li:nth-child(5) i { color: #9C27B0; }

/* Sports submenu icon colors */
.sports-submenu li:nth-child(1) i { color: #8e44ad; }
.sports-submenu li:nth-child(2) i { color: #9b59b6; }
.sports-submenu li:nth-child(3) i { color: #6a0dad; }
.sports-submenu li:nth-child(4) i { color: #5d3fd3; }
.sports-submenu li:nth-child(5) i { color: #7d3c98; }
.sports-submenu li:nth-child(6) i { color: #a569bd; }

/* Badge styles */
.badge {
    display: inline-block;
    padding: 2px 6px;
    font-size: 10px;
    font-weight: bold;
    border-radius: 10px;
    margin-left: 5px;
}

.new-badge {
    background-color: #ff3366;
    color: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    animation: pulse 2s infinite;
    transition: opacity 0.5s ease;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* User profile sidebar */
.user-profile-sidebar {
    display: flex;
    align-items: center;
    margin-top: 23px;
    padding: 12px 18px;
    border-radius: 10px;
    background-color: rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
}

.user-profile-sidebar:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.profile-pic-small {
    width: 46px;
    height: 46px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 12px;
    border: 2px solid #6a0dad;
}

.profile-pic-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-info-sidebar {
    flex: 1;
}

.profile-info-sidebar span:first-child {
    display: block;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.teams-clubs {
    font-size: 12px;
    color: #666;
    margin-top: 2px;
}

.profile-dropdown-toggle {
    margin-left: auto;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    border-radius: 50%;
    transition: all 0.2s;
    background-color: rgba(0, 0, 0, 0.05);
    cursor: pointer;
}

.profile-dropdown-toggle:hover {
    background-color: rgba(0, 0, 0, 0.1);
    color: #0066cc;
    transform: scale(1.1);
}

.profile-dropdown-toggle i {
    font-size: 20px;
    color: #444;
}

/* Profile dropdown menu */
.profile-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.2);
    width: 220px;
    z-index: 1000;
    overflow: hidden;
    display: none;
    border: 1px solid #eee;
    margin-top: 6px;
    max-height: 0;
    opacity: 0;
    transition: max-height 0.3s ease, opacity 0.3s ease;
    visibility: hidden;
    text-align: left;
}

.profile-dropdown-menu.show {
    display: block;
    max-height: 400px;
    opacity: 1;
    overflow-y: auto;
    z-index: 1001;
    visibility: visible;
}

.profile-dropdown-item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 14px 25px;
    text-decoration: none;
    color: #333;
    transition: all 0.2s ease;
    font-size: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.03);
    position: relative;
}

.profile-dropdown-item:last-child {
    border-bottom: none;
}

.profile-dropdown-item:hover {
    background-color: #f5f5f5;
    color: #6a0dad;
}

.profile-dropdown-item i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
    color: #6a0dad;
    font-size: 17px;
    display: inline-block;
}

.dropdown-divider {
    height: 1px;
    background-color: #e0e0e0;
    margin: 8px 0;
}

.logout-item {
    color: #e74c3c;
}

.logout-item:hover {
    background-color: rgba(231, 76, 60, 0.05);
    color: #e74c3c;
}

.logout-item i {
    color: #e74c3c;
}

/* Main content area */
.main-content {
    flex: 1;
    padding: 20px;
    background-color: white;
    border-radius: 15px;
    margin: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
    max-height: calc(100vh - 40px);
}

/* Search Header */
.search-header {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #f0f2f5;
}

.search-title h1 {
    color: #6a0dad;
    font-size: 28px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
}

.search-title h1 i {
    margin-right: 12px;
    font-size: 24px;
}

.search-title p {
    color: #666;
    font-size: 16px;
}

/* Search Bar Section */
.search-bar-section {
    margin-bottom: 25px;
}

.main-search-bar {
    display: flex;
    gap: 15px;
    align-items: center;
}

.search-input-container {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
    background: white;
    border: 2px solid #e1e5e9;
    border-radius: 25px;
    padding: 0 20px;
    transition: all 0.3s ease;
}

.search-input-container:focus-within {
    border-color: #6a0dad;
    box-shadow: 0 0 0 3px rgba(106, 13, 173, 0.1);
}

.search-icon {
    color: #6a0dad;
    font-size: 18px;
    margin-right: 15px;
}

.search-input {
    flex: 1;
    border: none;
    outline: none;
    font-size: 16px;
    padding: 15px 0;
    color: #333;
}

.search-input::placeholder {
    color: #999;
}

.voice-search-btn {
    background: none;
    border: none;
    color: #6a0dad;
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.3s;
}

.voice-search-btn:hover {
    background-color: rgba(106, 13, 173, 0.1);
}

.advanced-search-btn {
    background: #6a0dad;
    color: white;
    border: none;
    padding: 15px 25px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.advanced-search-btn:hover {
    background: #5a0c9a;
    transform: translateY(-2px);
}

/* Search Categories */
.search-categories {
    margin-bottom: 25px;
}

.category-tabs {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.category-tab {
    background: white;
    border: 2px solid #e1e5e9;
    color: #666;
    padding: 12px 20px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.category-tab:hover {
    border-color: #6a0dad;
    color: #6a0dad;
}

.category-tab.active {
    background: #6a0dad;
    border-color: #6a0dad;
    color: white;
}

.category-tab i {
    font-size: 16px;
}

/* Advanced Filters Panel */
.advanced-filters-panel {
    background: white;
    border: 2px solid #e1e5e9;
    border-radius: 15px;
    margin-bottom: 25px;
    overflow: hidden;
    max-height: 0;
    opacity: 0;
    transition: all 0.3s ease;
}

.advanced-filters-panel.show {
    max-height: 600px;
    opacity: 1;
}

.filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e1e5e9;
}

.filters-header h3 {
    color: #333;
    font-size: 18px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.close-filters-btn {
    background: none;
    border: none;
    color: #666;
    font-size: 20px;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-filters-btn:hover {
    background: #e9ecef;
    color: #333;
}

.filters-content {
    padding: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-label {
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.filter-options {
    position: relative;
}

.filter-select,
.filter-input {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.filter-select:focus,
.filter-input:focus {
    outline: none;
    border-color: #6a0dad;
}

.range-inputs {
    display: flex;
    align-items: center;
    gap: 10px;
}

.age-input {
    flex: 1;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    text-align: center;
}

.range-separator {
    color: #666;
    font-weight: 500;
}

.skill-levels {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.skill-option {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 14px;
    color: #333;
}

.skill-option input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #6a0dad;
}

.filter-actions {
    grid-column: 1 / -1;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e1e5e9;
}

.clear-filters-btn,
.apply-filters-btn {
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.clear-filters-btn {
    background: white;
    border: 2px solid #dc3545;
    color: #dc3545;
}

.clear-filters-btn:hover {
    background: #dc3545;
    color: white;
}

.apply-filters-btn {
    background: #6a0dad;
    border: 2px solid #6a0dad;
    color: white;
}

.apply-filters-btn:hover {
    background: #5a0c9a;
    border-color: #5a0c9a;
}

/* Search Results */
.search-results {
    margin-bottom: 30px;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e1e5e9;
}

.results-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.results-count {
    font-size: 16px;
    color: #333;
}

.results-time {
    font-size: 14px;
    color: #666;
}

.results-controls {
    display: flex;
    align-items: center;
    gap: 20px;
}

.sort-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.sort-controls label {
    font-size: 14px;
    color: #666;
}

.sort-select {
    padding: 8px 12px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 14px;
}

.view-controls {
    display: flex;
    gap: 5px;
}

.view-btn {
    background: white;
    border: 2px solid #e1e5e9;
    color: #666;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.view-btn:hover,
.view-btn.active {
    background: #6a0dad;
    border-color: #6a0dad;
    color: white;
}

/* Results Grid */
.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.result-card {
    background: white;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.result-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border-color: #6a0dad;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.result-type {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    font-weight: 600;
    color: #6a0dad;
    background: rgba(106, 13, 173, 0.1);
    padding: 4px 12px;
    border-radius: 15px;
}

.card-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    background: none;
    border: none;
    color: #666;
    font-size: 16px;
    cursor: pointer;
    padding: 6px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.action-btn:hover {
    background: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
}

.card-content {
    margin-bottom: 15px;
}

.profile-section {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.profile-image {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #e1e5e9;
}

.profile-info {
    flex: 1;
}

.profile-name {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.profile-position {
    font-size: 14px;
    color: #6a0dad;
    font-weight: 500;
    margin-bottom: 5px;
}

.profile-location {
    font-size: 13px;
    color: #666;
    display: flex;
    align-items: center;
    gap: 5px;
}

.stats-section {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.stat-label {
    font-size: 12px;
    color: #666;
    font-weight: 500;
}

.stat-value {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.tags-section {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-bottom: 15px;
}

.tag {
    background: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.card-footer {
    display: flex;
    gap: 10px;
}

.primary-btn,
.secondary-btn {
    flex: 1;
    padding: 10px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    text-decoration: none;
}

.primary-btn {
    background: #6a0dad;
    color: white;
    border: 2px solid #6a0dad;
}

.primary-btn:hover {
    background: #5a0c9a;
    border-color: #5a0c9a;
    transform: translateY(-2px);
}

.secondary-btn {
    background: white;
    color: #6a0dad;
    border: 2px solid #6a0dad;
}

.secondary-btn:hover {
    background: #6a0dad;
    color: white;
}

/* Card Type Specific Styles */
.player-card {
    border-left: 4px solid #4caf50;
}

.team-card {
    border-left: 4px solid #2196f3;
}

.coach-card {
    border-left: 4px solid #ff9800;
}

.opportunity-card {
    border-left: 4px solid #f44336;
}

.event-card {
    border-left: 4px solid #9c27b0;
}

.scout-card {
    border-left: 4px solid #795548;
}

/* Pagination */
.pagination-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e1e5e9;
}

.pagination {
    display: flex;
    align-items: center;
    gap: 8px;
}

.page-btn {
    background: white;
    border: 2px solid #e1e5e9;
    color: #666;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    min-width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.page-btn:hover:not(.disabled) {
    border-color: #6a0dad;
    color: #6a0dad;
}

.page-btn.active {
    background: #6a0dad;
    border-color: #6a0dad;
    color: white;
}

.page-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-dots {
    color: #666;
    font-size: 14px;
    padding: 0 8px;
}

.pagination-info {
    font-size: 14px;
    color: #666;
}

/* Floating Buttons */
.floating-buttons {
    position: fixed;
    bottom: 30px;
    right: 30px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    z-index: 1000;
}

.chat-button,
.top-button {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: none;
    background: #6a0dad;
    color: white;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(106, 13, 173, 0.3);
}

.chat-button:hover,
.top-button:hover {
    background: #5a0c9a;
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(106, 13, 173, 0.4);
}

/* Tooltips */
.js-tooltip {
    position: fixed;
    width: 220px;
    background-color: rgba(106, 13, 173, 0.9);
    color: white;
    padding: 10px;
    border-radius: 6px;
    font-size: 12px;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
    z-index: 1001;
    line-height: 1.4;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    pointer-events: none;
    text-align: left;
}

.js-tooltip::before {
    content: "";
    position: absolute;
    top: 50%;
    left: -10px;
    transform: translateY(-50%);
    border-width: 5px;
    border-style: solid;
    border-color: transparent var(--tooltip-color, rgba(106, 13, 173, 0.9)) transparent transparent;
}

.js-tooltip.visible {
    opacity: 1;
    visibility: visible;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        flex-direction: column;
        max-width: 100%;
        margin: 0;
    }
    
    .sidebar-left {
        width: 100%;
        height: auto;
        position: static;
    }
    
    .main-content {
        margin: 10px;
        border-radius: 10px;
    }
    
    .main-search-bar {
        flex-direction: column;
        gap: 10px;
    }
    
    .category-tabs {
        justify-content: center;
    }
    
    .results-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .results-controls {
        width: 100%;
        justify-content: space-between;
    }
    
    .results-grid {
        grid-template-columns: 1fr;
    }
    
    .filters-content {
        grid-template-columns: 1fr;
    }
    
    .pagination-section {
        flex-direction: column;
        gap: 15px;
    }
    
    .floating-buttons {
        bottom: 20px;
        right: 20px;
    }
}

@media (max-width: 480px) {
    .main-content {
        padding: 15px;
    }
    
    .search-title h1 {
        font-size: 24px;
    }
    
    .search-title p {
        font-size: 14px;
    }
    
    .search-input-container {
        padding: 0 15px;
    }
    
    .search-input {
        font-size: 14px;
        padding: 12px 0;
    }
    
    .category-tab {
        padding: 10px 15px;
        font-size: 13px;
    }
    
    .result-card {
        padding: 15px;
    }
    
    .profile-name {
        font-size: 16px;
    }
    
    .stats-section {
        padding: 10px;
    }
    
    .pagination {
        flex-wrap: wrap;
        justify-content: center;
    }
}

/* Animation keyframes */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Search result animations */
.result-card {
    animation: fadeIn 0.5s ease-out;
}

.result-card:nth-child(1) { animation-delay: 0.1s; }
.result-card:nth-child(2) { animation-delay: 0.2s; }
.result-card:nth-child(3) { animation-delay: 0.3s; }
.result-card:nth-child(4) { animation-delay: 0.4s; }
.result-card:nth-child(5) { animation-delay: 0.5s; }
.result-card:nth-child(6) { animation-delay: 0.6s; }